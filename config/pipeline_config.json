{"redis_url": "redis://localhost:6379", "redis_db": 0, "csv_backup_enabled": true, "max_retries": 3, "retry_delay": 5, "batch_size": 10, "concurrent_workers": 3, "checkpoint_interval": 100, "data_dir": "data", "logs_dir": "logs", "enable_monitoring": true, "auto_resume": true, "scraper_config": {"delay_between_requests": 2.0, "timeout": 30, "user_agent": "Enhanced MCP Pipeline Bot 1.0", "max_concurrent_requests": 3, "respect_robots_txt": true, "enable_caching": true, "cache_duration": 3600}, "generator_config": {"input_file": "data/completed_data.csv", "output_file": "data/generated_content.json", "batch_size": 3, "timeout": 300, "enable_quality_check": true, "min_content_length": 100, "max_content_length": 10000}, "workflows": {"daily_automation": {"schedule": {"daily": true, "time": "02:00"}, "auto_discover": true, "auto_cleanup": true, "cleanup_days": 30, "generate_reports": true, "notification_enabled": false, "notification_email": "", "max_runtime_hours": 6}, "continuous_scraping": {"schedule": {"hourly": true, "interval": 6}, "max_iterations": 0, "auto_pause_on_errors": true, "error_threshold": 10, "resource_monitoring": true}}, "error_handling": {"max_retries_per_category": {"network": 5, "rate_limit": 3, "parsing": 2, "authentication": 0, "resource": 2}, "retry_delays": {"network": 2.0, "rate_limit": 10.0, "parsing": 1.0, "resource": 30.0}, "escalation_enabled": true, "critical_error_notification": false}, "monitoring": {"metrics_enabled": true, "metrics_interval": 60, "performance_tracking": true, "resource_monitoring": true, "alert_thresholds": {"error_rate": 0.1, "memory_usage": 0.8, "disk_usage": 0.9, "response_time": 30.0}}, "admin_panel": {"host": "0.0.0.0", "port": 9000, "debug": false, "secret_key": "change-this-in-production", "session_timeout": 3600, "enable_authentication": false, "allowed_ips": [], "rate_limiting": {"enabled": true, "requests_per_minute": 60}}, "data_management": {"auto_backup": true, "backup_interval_hours": 24, "backup_retention_days": 7, "compression_enabled": true, "encryption_enabled": false, "data_validation": true}, "performance": {"connection_pool_size": 10, "connection_timeout": 30, "read_timeout": 60, "max_memory_usage_mb": 1024, "gc_threshold": 1000, "optimize_for": "balanced"}}