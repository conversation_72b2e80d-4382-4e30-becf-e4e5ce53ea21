{"environment": "staging", "data_dir": "data", "checkpoint_interval": 50, "auto_resume": true, "enable_monitoring": true, "redis": {"url": "redis://localhost:6379", "db": 2, "password": null, "ssl": false, "socket_timeout": 5, "socket_connect_timeout": 5, "max_connections": 10, "retry_on_timeout": true}, "database": {"enabled": false, "url": "sqlite:///pipeline_staging.db", "pool_size": 5, "max_overflow": 10, "echo": false}, "scraping": {"batch_size": 8, "concurrent_workers": 3, "delay_between_requests": 2.0, "timeout": 35, "max_retries": 4, "retry_delay": 5.0, "user_agent": "Enhanced MCP Pipeline Bot 1.0 (Staging)", "respect_robots_txt": true, "enable_caching": true, "cache_duration": 3600}, "content_generation": {"batch_size": 3, "timeout": 400, "max_retries": 3, "enable_quality_check": true, "min_content_length": 100, "max_content_length": 12000, "input_file": "data/completed_data.csv", "output_file": "data/generated_content.json"}, "monitoring": {"enabled": true, "collection_interval": 45, "retention_days": 7, "buffer_size": 1000, "alert_thresholds": {"cpu_percent": 80.0, "memory_percent": 85.0, "disk_percent": 90.0, "error_rate": 0.08, "response_time": 30.0}}, "logging": {"level": "INFO", "logs_dir": "logs", "max_file_size": 10485760, "backup_count": 5, "enable_compression": true, "enable_structured_logging": true, "enable_real_time_streaming": true}, "admin_panel": {"host": "0.0.0.0", "port": 9001, "debug": true, "secret_key": "staging-secret-key-change-for-production", "session_timeout": 3600, "enable_authentication": true, "allowed_ips": [], "rate_limiting": {"enabled": true, "requests_per_minute": 60}}, "security": {"enable_ssl": false, "ssl_cert_path": null, "ssl_key_path": null, "api_key_required": false, "api_keys": [], "cors_enabled": true, "cors_origins": ["http://localhost:3000", "http://127.0.0.1:3000"]}}