{"environment": "development", "data_dir": "data", "checkpoint_interval": 25, "auto_resume": true, "enable_monitoring": true, "redis": {"url": "redis://localhost:6379", "db": 1, "password": null, "ssl": false, "socket_timeout": 5, "socket_connect_timeout": 5, "max_connections": 5, "retry_on_timeout": true}, "database": {"enabled": false, "url": "sqlite:///pipeline_dev.db", "pool_size": 3, "max_overflow": 5, "echo": true}, "scraping": {"batch_size": 5, "concurrent_workers": 3, "delay_between_requests": 1.0, "timeout": 30, "max_retries": 3, "retry_delay": 3.0, "user_agent": "Enhanced MCP Pipeline Bot 1.0 (Development)", "respect_robots_txt": true, "enable_caching": true, "cache_duration": 1800}, "content_generation": {"batch_size": 3, "timeout": 300, "max_retries": 2, "enable_quality_check": true, "min_content_length": 50, "max_content_length": 10000, "input_file": "data/completed_data.csv", "output_file": "data/generated_content.json"}, "monitoring": {"enabled": true, "collection_interval": 60, "retention_days": 3, "buffer_size": 500, "alert_thresholds": {"cpu_percent": 85.0, "memory_percent": 90.0, "disk_percent": 95.0, "error_rate": 0.15, "response_time": 45.0}}, "logging": {"level": "DEBUG", "logs_dir": "logs", "max_file_size": 5242880, "backup_count": 3, "enable_compression": false, "enable_structured_logging": true, "enable_real_time_streaming": true}, "admin_panel": {"host": "127.0.0.1", "port": 9000, "debug": true, "secret_key": "development-secret-key-not-for-production", "session_timeout": 3600, "enable_authentication": false, "allowed_ips": [], "rate_limiting": {"enabled": false, "requests_per_minute": 120}}, "security": {"enable_ssl": false, "ssl_cert_path": null, "ssl_key_path": null, "api_key_required": false, "api_keys": [], "cors_enabled": true, "cors_origins": ["*"]}}