{"environment": "production", "data_dir": "data", "checkpoint_interval": 50, "auto_resume": true, "enable_monitoring": true, "redis": {"url": "redis://localhost:6379", "db": 0, "password": null, "ssl": false, "socket_timeout": 5, "socket_connect_timeout": 5, "max_connections": 20, "retry_on_timeout": true}, "database": {"enabled": false, "url": "sqlite:///pipeline_prod.db", "pool_size": 10, "max_overflow": 20, "echo": false}, "scraping": {"batch_size": 8, "concurrent_workers": 2, "delay_between_requests": 3.0, "timeout": 45, "max_retries": 5, "retry_delay": 10.0, "user_agent": "Enhanced MCP Pipeline Bot 1.0", "respect_robots_txt": true, "enable_caching": true, "cache_duration": 7200}, "content_generation": {"batch_size": 2, "timeout": 600, "max_retries": 3, "enable_quality_check": true, "min_content_length": 200, "max_content_length": 15000, "input_file": "data/completed_data.csv", "output_file": "data/generated_content.json"}, "monitoring": {"enabled": true, "collection_interval": 30, "retention_days": 14, "buffer_size": 2000, "alert_thresholds": {"cpu_percent": 75.0, "memory_percent": 80.0, "disk_percent": 85.0, "error_rate": 0.05, "response_time": 20.0}}, "logging": {"level": "INFO", "logs_dir": "logs", "max_file_size": 20971520, "backup_count": 10, "enable_compression": true, "enable_structured_logging": true, "enable_real_time_streaming": true}, "admin_panel": {"host": "0.0.0.0", "port": 9000, "debug": false, "secret_key": "CHANGE-THIS-IN-PRODUCTION-USE-STRONG-SECRET", "session_timeout": 7200, "enable_authentication": true, "allowed_ips": [], "rate_limiting": {"enabled": true, "requests_per_minute": 30}}, "security": {"enable_ssl": false, "ssl_cert_path": null, "ssl_key_path": null, "api_key_required": false, "api_keys": [], "cors_enabled": true, "cors_origins": []}}