#!/usr/bin/env python3
"""
Enhanced MCP Pipeline Deployment Setup
Automated setup and deployment script for the enhanced MCP pipeline system.
Handles environment setup, dependency installation, and configuration.
"""

import os
import sys
import subprocess
import shutil
import json
import platform
from pathlib import Path
from typing import Dict, List, Optional, Any
import argparse


class PipelineDeployer:
    """Automated deployment and setup for the enhanced MCP pipeline"""
    
    def __init__(self, target_env: str = "development"):
        self.target_env = target_env
        self.system_info = self._get_system_info()
        self.project_root = Path(__file__).parent.parent
        self.requirements_file = self.project_root / "requirements.txt"
        
        print(f"🚀 Enhanced MCP Pipeline Deployment Setup")
        print(f"📋 Target Environment: {target_env}")
        print(f"💻 System: {self.system_info['platform']} {self.system_info['version']}")
        print(f"🐍 Python: {self.system_info['python_version']}")

    def _get_system_info(self) -> Dict[str, str]:
        """Get system information"""
        return {
            'platform': platform.system(),
            'version': platform.release(),
            'architecture': platform.machine(),
            'python_version': platform.python_version(),
            'python_executable': sys.executable
        }

    def check_prerequisites(self) -> bool:
        """Check system prerequisites"""
        print("\n🔍 Checking Prerequisites...")
        
        issues = []
        
        # Check Python version
        if sys.version_info < (3, 8):
            issues.append("Python 3.8+ required")
        else:
            print("✅ Python version OK")
        
        # Check pip
        try:
            subprocess.run([sys.executable, "-m", "pip", "--version"], 
                         check=True, capture_output=True)
            print("✅ pip available")
        except subprocess.CalledProcessError:
            issues.append("pip not available")
        
        # Check git (optional)
        try:
            subprocess.run(["git", "--version"], check=True, capture_output=True)
            print("✅ git available")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️  git not available (optional)")
        
        # Check Redis (optional)
        try:
            import redis
            client = redis.Redis(host='localhost', port=6379, db=0)
            client.ping()
            print("✅ Redis server running")
        except Exception:
            print("⚠️  Redis server not running (will use CSV-only mode)")
        
        if issues:
            print(f"\n❌ Prerequisites check failed:")
            for issue in issues:
                print(f"   - {issue}")
            return False
        
        print("✅ All prerequisites satisfied")
        return True

    def create_virtual_environment(self, venv_path: str = "venv") -> bool:
        """Create Python virtual environment"""
        try:
            print(f"\n🐍 Creating virtual environment: {venv_path}")
            
            venv_dir = self.project_root / venv_path
            
            if venv_dir.exists():
                print(f"⚠️  Virtual environment already exists at {venv_dir}")
                response = input("Remove and recreate? (y/N): ").strip().lower()
                if response == 'y':
                    shutil.rmtree(venv_dir)
                else:
                    print("Using existing virtual environment")
                    return True
            
            # Create virtual environment
            subprocess.run([
                sys.executable, "-m", "venv", str(venv_dir)
            ], check=True)
            
            print("✅ Virtual environment created")
            
            # Get activation script path
            if platform.system() == "Windows":
                activate_script = venv_dir / "Scripts" / "activate.bat"
                pip_executable = venv_dir / "Scripts" / "pip.exe"
            else:
                activate_script = venv_dir / "bin" / "activate"
                pip_executable = venv_dir / "bin" / "pip"
            
            print(f"📝 To activate: source {activate_script}")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to create virtual environment: {e}")
            return False

    def install_dependencies(self, venv_path: str = "venv") -> bool:
        """Install Python dependencies"""
        try:
            print("\n📦 Installing dependencies...")
            
            # Determine pip executable
            if venv_path:
                venv_dir = self.project_root / venv_path
                if platform.system() == "Windows":
                    pip_executable = venv_dir / "Scripts" / "pip.exe"
                else:
                    pip_executable = venv_dir / "bin" / "pip"
                
                if not pip_executable.exists():
                    print(f"❌ pip not found in virtual environment: {pip_executable}")
                    return False
            else:
                pip_executable = "pip"
            
            # Create requirements.txt if it doesn't exist
            if not self.requirements_file.exists():
                self._create_requirements_file()
            
            # Install requirements
            subprocess.run([
                str(pip_executable), "install", "-r", str(self.requirements_file)
            ], check=True)
            
            print("✅ Dependencies installed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install dependencies: {e}")
            return False

    def _create_requirements_file(self):
        """Create requirements.txt file"""
        requirements = [
            "redis>=4.0.0",
            "aioredis>=2.0.0",
            "pandas>=1.3.0",
            "flask>=2.0.0",
            "requests>=2.25.0",
            "beautifulsoup4>=4.9.0",
            "schedule>=1.1.0",
            "psutil>=5.8.0",
            "pyyaml>=6.0",
            "python-dotenv>=0.19.0",
            "asyncio-mqtt>=0.11.0",
            "websockets>=10.0",
            "jinja2>=3.0.0",
            "werkzeug>=2.0.0",
            "click>=8.0.0"
        ]
        
        with open(self.requirements_file, 'w') as f:
            f.write('\n'.join(requirements))
        
        print(f"📝 Created {self.requirements_file}")

    def setup_directories(self) -> bool:
        """Setup required directories"""
        try:
            print("\n📁 Setting up directories...")
            
            directories = [
                "data",
                "data/metrics",
                "data/reports",
                "logs",
                "config",
                "templates",
                "static",
                "deploy/scripts",
                "deploy/configs"
            ]
            
            for directory in directories:
                dir_path = self.project_root / directory
                dir_path.mkdir(parents=True, exist_ok=True)
                print(f"✅ Created directory: {directory}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to setup directories: {e}")
            return False

    def create_configuration(self) -> bool:
        """Create environment-specific configuration"""
        try:
            print(f"\n⚙️  Creating configuration for {self.target_env}...")
            
            # Import config manager
            sys.path.append(str(self.project_root))
            from pipeline.config.config_manager import ConfigManager, Environment
            
            config_manager = ConfigManager()
            
            # Create environment-specific config
            env = Environment(self.target_env)
            overrides = self._get_environment_overrides(env)
            
            success = config_manager.create_environment_config(env, overrides)
            
            if success:
                print(f"✅ Configuration created for {self.target_env}")
                return True
            else:
                print(f"❌ Failed to create configuration for {self.target_env}")
                return False
                
        except Exception as e:
            print(f"❌ Failed to create configuration: {e}")
            return False

    def _get_environment_overrides(self, env: 'Environment') -> Dict[str, Any]:
        """Get environment-specific configuration overrides"""
        if env.value == "production":
            return {
                'admin_panel': {
                    'debug': False,
                    'host': '0.0.0.0',
                    'port': 9000,
                    'enable_authentication': True
                },
                'logging': {
                    'level': 'INFO',
                    'enable_structured_logging': True
                },
                'security': {
                    'enable_ssl': False,  # Set to True if SSL certificates available
                    'api_key_required': False,
                    'cors_origins': []
                },
                'monitoring': {
                    'enabled': True,
                    'collection_interval': 30
                },
                'scraping': {
                    'delay_between_requests': 3.0,
                    'respect_robots_txt': True,
                    'concurrent_workers': 2
                }
            }
        elif env.value == "staging":
            return {
                'admin_panel': {
                    'debug': True,
                    'port': 9001
                },
                'logging': {
                    'level': 'DEBUG'
                },
                'scraping': {
                    'delay_between_requests': 2.0
                }
            }
        else:  # development
            return {
                'admin_panel': {
                    'debug': True,
                    'enable_authentication': False
                },
                'logging': {
                    'level': 'DEBUG'
                },
                'scraping': {
                    'delay_between_requests': 1.0,
                    'concurrent_workers': 3
                }
            }

    def create_startup_scripts(self) -> bool:
        """Create startup scripts for different platforms"""
        try:
            print("\n📜 Creating startup scripts...")
            
            scripts_dir = self.project_root / "deploy" / "scripts"
            scripts_dir.mkdir(exist_ok=True)
            
            # Unix/Linux startup script
            unix_script = scripts_dir / "start_pipeline.sh"
            with open(unix_script, 'w') as f:
                f.write(self._get_unix_startup_script())
            unix_script.chmod(0o755)
            print("✅ Created Unix startup script")
            
            # Windows startup script
            windows_script = scripts_dir / "start_pipeline.bat"
            with open(windows_script, 'w') as f:
                f.write(self._get_windows_startup_script())
            print("✅ Created Windows startup script")
            
            # Docker startup script
            docker_script = scripts_dir / "start_pipeline_docker.sh"
            with open(docker_script, 'w') as f:
                f.write(self._get_docker_startup_script())
            docker_script.chmod(0o755)
            print("✅ Created Docker startup script")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to create startup scripts: {e}")
            return False

    def _get_unix_startup_script(self) -> str:
        """Get Unix/Linux startup script content"""
        return f'''#!/bin/bash
# Enhanced MCP Pipeline Startup Script (Unix/Linux)
# Environment: {self.target_env}

set -e

# Configuration
PIPELINE_ENV="{self.target_env}"
PROJECT_ROOT="$(cd "$(dirname "${{BASH_SOURCE[0]}}")/../.." && pwd)"
VENV_PATH="$PROJECT_ROOT/venv"
LOG_FILE="$PROJECT_ROOT/logs/startup.log"

# Colors for output
RED='\\033[0;31m'
GREEN='\\033[0;32m'
YELLOW='\\033[1;33m'
NC='\\033[0m' # No Color

echo "🚀 Starting Enhanced MCP Pipeline..."
echo "📋 Environment: $PIPELINE_ENV"
echo "📁 Project Root: $PROJECT_ROOT"

# Create logs directory
mkdir -p "$PROJECT_ROOT/logs"

# Check if virtual environment exists
if [ ! -d "$VENV_PATH" ]; then
    echo -e "${{RED}}❌ Virtual environment not found at $VENV_PATH${{NC}}"
    echo "Run setup.py first to create the environment"
    exit 1
fi

# Activate virtual environment
echo "🐍 Activating virtual environment..."
source "$VENV_PATH/bin/activate"

# Set environment variables
export PIPELINE_ENV="$PIPELINE_ENV"
export PYTHONPATH="$PROJECT_ROOT:$PYTHONPATH"

# Change to project directory
cd "$PROJECT_ROOT"

# Check Redis (optional)
if command -v redis-cli &> /dev/null; then
    if redis-cli ping &> /dev/null; then
        echo -e "${{GREEN}}✅ Redis server is running${{NC}}"
    else
        echo -e "${{YELLOW}}⚠️  Redis server not running (will use CSV-only mode)${{NC}}"
    fi
fi

# Start the pipeline
echo "🎯 Starting pipeline system..."
python start_enhanced_pipeline.py quick 2>&1 | tee -a "$LOG_FILE"
'''

    def _get_windows_startup_script(self) -> str:
        """Get Windows startup script content"""
        return f'''@echo off
REM Enhanced MCP Pipeline Startup Script (Windows)
REM Environment: {self.target_env}

setlocal enabledelayedexpansion

REM Configuration
set PIPELINE_ENV={self.target_env}
set PROJECT_ROOT=%~dp0..\..
set VENV_PATH=%PROJECT_ROOT%\\venv
set LOG_FILE=%PROJECT_ROOT%\\logs\\startup.log

echo 🚀 Starting Enhanced MCP Pipeline...
echo 📋 Environment: %PIPELINE_ENV%
echo 📁 Project Root: %PROJECT_ROOT%

REM Create logs directory
if not exist "%PROJECT_ROOT%\\logs" mkdir "%PROJECT_ROOT%\\logs"

REM Check if virtual environment exists
if not exist "%VENV_PATH%" (
    echo ❌ Virtual environment not found at %VENV_PATH%
    echo Run setup.py first to create the environment
    pause
    exit /b 1
)

REM Activate virtual environment
echo 🐍 Activating virtual environment...
call "%VENV_PATH%\\Scripts\\activate.bat"

REM Set environment variables
set PIPELINE_ENV=%PIPELINE_ENV%
set PYTHONPATH=%PROJECT_ROOT%;%PYTHONPATH%

REM Change to project directory
cd /d "%PROJECT_ROOT%"

REM Start the pipeline
echo 🎯 Starting pipeline system...
python start_enhanced_pipeline.py quick
'''

    def _get_docker_startup_script(self) -> str:
        """Get Docker startup script content"""
        return f'''#!/bin/bash
# Enhanced MCP Pipeline Docker Startup Script
# Environment: {self.target_env}

set -e

# Configuration
export PIPELINE_ENV="{self.target_env}"
export PYTHONPATH="/app:$PYTHONPATH"

# Change to app directory
cd /app

# Wait for Redis (if configured)
if [ "$REDIS_URL" ]; then
    echo "⏳ Waiting for Redis..."
    while ! redis-cli -u "$REDIS_URL" ping &> /dev/null; do
        echo "Redis not ready, waiting..."
        sleep 2
    done
    echo "✅ Redis is ready"
fi

# Create required directories
mkdir -p data logs config

# Start the pipeline
echo "🚀 Starting Enhanced MCP Pipeline in Docker..."
exec python start_enhanced_pipeline.py quick
'''

    def create_docker_files(self) -> bool:
        """Create Docker configuration files"""
        try:
            print("\n🐳 Creating Docker files...")
            
            # Dockerfile
            dockerfile_path = self.project_root / "Dockerfile"
            with open(dockerfile_path, 'w') as f:
                f.write(self._get_dockerfile_content())
            print("✅ Created Dockerfile")
            
            # Docker Compose
            compose_path = self.project_root / "docker-compose.yml"
            with open(compose_path, 'w') as f:
                f.write(self._get_docker_compose_content())
            print("✅ Created docker-compose.yml")
            
            # .dockerignore
            dockerignore_path = self.project_root / ".dockerignore"
            with open(dockerignore_path, 'w') as f:
                f.write(self._get_dockerignore_content())
            print("✅ Created .dockerignore")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to create Docker files: {e}")
            return False

    def _get_dockerfile_content(self) -> str:
        """Get Dockerfile content"""
        return '''# Enhanced MCP Pipeline Dockerfile
FROM python:3.9-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    git \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create required directories
RUN mkdir -p data logs config

# Set environment variables
ENV PYTHONPATH=/app
ENV PIPELINE_ENV=production

# Expose admin panel port
EXPOSE 9000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:9000/api/enhanced/status || exit 1

# Start the application
CMD ["python", "start_enhanced_pipeline.py", "quick"]
'''

    def _get_docker_compose_content(self) -> str:
        """Get Docker Compose content"""
        return f'''version: '3.8'

services:
  pipeline:
    build: .
    ports:
      - "9000:9000"
    environment:
      - PIPELINE_ENV={self.target_env}
      - REDIS_URL=redis://redis:6379
      - DATA_DIR=/app/data
      - LOG_LEVEL=INFO
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/api/enhanced/status"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis_data:
'''

    def _get_dockerignore_content(self) -> str:
        """Get .dockerignore content"""
        return '''# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Data (exclude from image, mount as volume)
data/
!data/.gitkeep

# Config (exclude sensitive configs)
config/*.json
!config/pipeline_config.example.json

# Git
.git/
.gitignore

# Documentation
docs/
*.md
!README.md

# Tests
tests/
pytest.ini
.coverage

# Deployment
deploy/
'''

    def create_systemd_service(self) -> bool:
        """Create systemd service file for Linux"""
        try:
            if platform.system() != "Linux":
                print("⚠️  Systemd service only available on Linux")
                return True
            
            print("\n🔧 Creating systemd service...")
            
            service_content = f'''[Unit]
Description=Enhanced MCP Pipeline
After=network.target redis.service
Wants=redis.service

[Service]
Type=simple
User=pipeline
Group=pipeline
WorkingDirectory={self.project_root}
Environment=PIPELINE_ENV={self.target_env}
Environment=PYTHONPATH={self.project_root}
ExecStart={self.project_root}/venv/bin/python start_enhanced_pipeline.py quick
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=mcp-pipeline

[Install]
WantedBy=multi-user.target
'''
            
            service_file = self.project_root / "deploy" / "mcp-pipeline.service"
            with open(service_file, 'w') as f:
                f.write(service_content)
            
            print("✅ Created systemd service file")
            print(f"📝 To install: sudo cp {service_file} /etc/systemd/system/")
            print("📝 To enable: sudo systemctl enable mcp-pipeline")
            print("📝 To start: sudo systemctl start mcp-pipeline")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to create systemd service: {e}")
            return False

    def run_deployment(self, skip_venv: bool = False, skip_deps: bool = False) -> bool:
        """Run complete deployment process"""
        print(f"\n🎯 Starting deployment for {self.target_env} environment...")
        
        steps = [
            ("Prerequisites", self.check_prerequisites),
            ("Directories", self.setup_directories),
            ("Configuration", self.create_configuration),
            ("Startup Scripts", self.create_startup_scripts),
            ("Docker Files", self.create_docker_files),
            ("Systemd Service", self.create_systemd_service)
        ]
        
        if not skip_venv:
            steps.insert(1, ("Virtual Environment", lambda: self.create_virtual_environment()))
        
        if not skip_deps:
            steps.insert(-4, ("Dependencies", lambda: self.install_dependencies()))
        
        failed_steps = []
        
        for step_name, step_func in steps:
            try:
                if not step_func():
                    failed_steps.append(step_name)
            except Exception as e:
                print(f"❌ Step '{step_name}' failed: {e}")
                failed_steps.append(step_name)
        
        if failed_steps:
            print(f"\n❌ Deployment completed with errors:")
            for step in failed_steps:
                print(f"   - {step}")
            return False
        else:
            print(f"\n✅ Deployment completed successfully!")
            self._print_next_steps()
            return True

    def _print_next_steps(self):
        """Print next steps after deployment"""
        print(f"\n📋 Next Steps:")
        print(f"1. Review configuration in config/config_{self.target_env}.json")
        print(f"2. Start Redis server (if using Redis): redis-server")
        print(f"3. Start the pipeline:")
        
        if platform.system() == "Windows":
            print(f"   deploy\\scripts\\start_pipeline.bat")
        else:
            print(f"   ./deploy/scripts/start_pipeline.sh")
        
        print(f"4. Access admin panel: http://localhost:9000")
        print(f"5. Check logs in logs/ directory")
        
        if self.target_env == "production":
            print(f"\n🔒 Production Notes:")
            print(f"   - Review security settings in configuration")
            print(f"   - Set up SSL certificates if needed")
            print(f"   - Configure firewall rules")
            print(f"   - Set up monitoring and alerting")


def main():
    """Main deployment function"""
    parser = argparse.ArgumentParser(description="Enhanced MCP Pipeline Deployment Setup")
    parser.add_argument('--env', choices=['development', 'staging', 'production'], 
                       default='development', help='Target environment')
    parser.add_argument('--skip-venv', action='store_true', 
                       help='Skip virtual environment creation')
    parser.add_argument('--skip-deps', action='store_true', 
                       help='Skip dependency installation')
    
    args = parser.parse_args()
    
    deployer = PipelineDeployer(args.env)
    success = deployer.run_deployment(args.skip_venv, args.skip_deps)
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
