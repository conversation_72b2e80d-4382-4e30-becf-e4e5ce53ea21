2025-06-25 07:54:02,468 - __main__ - ERROR - initialize:142 - ❌ Failed to initialize pipeline system: 'StateManager' object has no attribute 'logger'
2025-06-25 07:54:35,559 - pipeline.core.data_sync_manager - ERROR - sync_redis_from_csv:365 - Error syncing <PERSON><PERSON> from CSV: Invalid input of type: 'list'. Convert to a bytes, string, int or float first.
2025-06-25 07:54:35,560 - __main__ - ERROR - initialize:142 - ❌ Failed to initialize pipeline system: 'StateManager' object has no attribute 'logger'
2025-06-25 07:55:14,321 - pipeline.core.data_sync_manager - ERROR - sync_redis_from_csv:365 - Error syncing Red<PERSON> from CSV: Invalid input of type: 'list'. Convert to a bytes, string, int or float first.
2025-06-25 07:55:14,322 - __main__ - ERROR - initialize:142 - ❌ Failed to initialize pipeline system: 'StateManager' object has no attribute 'logger'
2025-06-25 09:19:03,015 - __main__ - ERROR - initialize:142 - ❌ Failed to initialize pipeline system: 'StateManager' object has no attribute 'logger'
2025-06-25 09:22:12,884 - __main__ - ERROR - initialize:142 - ❌ Failed to initialize pipeline system: 'StateManager' object has no attribute 'logger'
2025-06-25 09:24:40,944 - pipeline.orchestrator.pipeline_orchestrator - ERROR - _save_workflows:515 - Failed to save workflows: Object of type DataSyncManager is not JSON serializable
2025-06-25 09:24:56,621 - pipeline.scrapers.resumable_scraper.scrape_20250625_092440 - ERROR - _process_single_url:329 - Failed to scrape https://github.com/microsoft/vscode after 3 attempts: dict contains fields not in fieldnames: 'owner', 'repo'
2025-06-25 09:25:00,625 - pipeline.orchestrator.pipeline_orchestrator - ERROR - _save_workflows:515 - Failed to save workflows: Object of type DataSyncManager is not JSON serializable
2025-06-25 09:25:05,631 - pipeline.orchestrator.pipeline_orchestrator - ERROR - _save_workflows:515 - Failed to save workflows: Object of type DataSyncManager is not JSON serializable
2025-06-25 09:26:17,339 - pipeline.orchestrator.pipeline_orchestrator - ERROR - _load_workflows:566 - Failed to load workflows: Expecting value: line 15 column 28 (char 362)
2025-06-25 09:28:17,385 - pipeline.core.data_sync_manager - ERROR - sync_redis_from_csv:365 - Error syncing Redis from CSV: Invalid input of type: 'list'. Convert to a bytes, string, int or float first.
2025-06-25 09:28:48,140 - pipeline.core.data_sync_manager - ERROR - sync_redis_from_csv:365 - Error syncing Redis from CSV: Invalid input of type: 'bool'. Convert to a bytes, string, int or float first.
