2025-06-24 07:40:56,031 - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-06-24 07:40:58,164 - INFO - Content processor connected to Redis event bus
2025-06-24 07:40:58,183 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 07:41:10,941 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 07:41:10,953 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 07:41:10,954 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:10,956 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:10,956 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:10,969 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 07:41:18,344 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 07:41:18,355 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 07:41:18,357 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:18,357 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:18,361 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:18,373 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 07:41:40,686 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 07:41:40,707 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 07:41:40,709 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:40,709 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:40,717 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:40,732 - INFO - Enhanced SEO for content: Transforming Workflows with MCP Servers: Python Executor & DroidMind
2025-06-24 07:41:40,732 - INFO - Enhanced SEO for content: Transforming Workflows with MCP Servers: Python Executor & DroidMind
2025-06-24 07:41:40,759 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 07:41:57,202 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 07:41:57,206 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 07:41:57,207 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:57,207 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:57,211 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:41:57,219 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 07:41:57,453 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 429 Too Many Requests"
2025-06-24 07:41:57,453 - INFO - Retrying request to /chat/completions in 3.000000 seconds
2025-06-24 07:42:07,476 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 07:42:07,481 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 07:42:07,481 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:42:07,486 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:42:07,493 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 07:42:07,999 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 429 Too Many Requests"
2025-06-24 07:42:08,000 - INFO - Retrying request to /chat/completions in 4.000000 seconds
2025-06-24 07:42:31,592 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 07:42:31,597 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 07:42:31,598 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:42:31,599 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:42:31,605 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:42:31,611 - INFO - Enhanced SEO for content: MCP-Server-RabbitMQ: Your Ultimate RabbitMQ Management Tool
2025-06-24 07:42:31,611 - INFO - Enhanced SEO for content: MCP-Server-RabbitMQ: Your Ultimate RabbitMQ Management Tool
2025-06-24 07:42:31,623 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 07:42:45,980 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 07:42:45,986 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 07:42:45,987 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:42:45,987 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:42:45,991 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:42:45,996 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 07:42:52,246 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 07:42:52,248 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 07:42:52,248 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:42:52,248 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:42:52,251 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:42:52,257 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 07:42:52,560 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 429 Too Many Requests"
2025-06-24 07:42:52,561 - INFO - Retrying request to /chat/completions in 20.000000 seconds
2025-06-24 07:43:38,416 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 07:43:38,427 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 07:43:38,428 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:43:38,428 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:43:38,440 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 07:43:38,446 - INFO - Enhanced SEO for content: ida-pro-mcp
2025-06-24 08:02:11,516 - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-06-24 08:02:13,843 - INFO - Content processor connected to Redis event bus
2025-06-24 08:02:13,868 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:02:23,219 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:02:23,242 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:02:23,244 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:02:23,245 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:02:23,248 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:02:23,277 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:02:31,622 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:02:31,628 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:02:31,629 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:02:31,630 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:02:31,637 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:02:31,643 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:02:51,337 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:02:51,348 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:02:51,348 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:02:51,349 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:02:51,358 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:02:51,364 - ERROR - Content generation error: 'dict' object has no attribute 'lower'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/run_content_generation.py", line 115, in run_content_generation
    processor.process_filtered_urls(filter_urls, max_repos)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/content_processor/processor/content_processor.py", line 2177, in process_filtered_urls
    processed_batch = self.process_batch(batch)
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/content_processor/processor/content_processor.py", line 1662, in process_batch
    final_item = self._intelligently_populate_all_fields(
        content_data, item, analysis_data, seo_data, run_instructions
    )
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/content_processor/processor/content_processor.py", line 479, in _intelligently_populate_all_fields
    final_item = self._add_eeat_enhancements(final_item, item)
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/content_processor/processor/content_processor.py", line 581, in _add_eeat_enhancements
    final_item['case_studies'] = self._generate_case_studies(final_item)
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/content_processor/processor/content_processor.py", line 631, in _generate_case_studies
    if 'decision' in use_case.lower() or 'strategy' in use_case.lower():
                     ^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'lower'
2025-06-24 08:03:44,602 - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-06-24 08:03:46,640 - INFO - Content processor connected to Redis event bus
2025-06-24 08:03:46,663 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:03:59,257 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:03:59,293 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:03:59,293 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:03:59,294 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:03:59,295 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:03:59,307 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:04:06,185 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:04:06,187 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:04:06,187 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:04:06,187 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:04:06,190 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:04:06,195 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:04:37,396 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:04:37,411 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:04:37,413 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:04:37,413 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:04:37,427 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:04:37,438 - ERROR - Content generation error: 'dict' object has no attribute 'lower'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/run_content_generation.py", line 118, in run_content_generation
    processor.process_limited(max_repos)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/content_processor/processor/content_processor.py", line 2098, in process_limited
    processed_batch = self.process_batch(batch)
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/content_processor/processor/content_processor.py", line 1662, in process_batch
    final_item = self._intelligently_populate_all_fields(
        content_data, item, analysis_data, seo_data, run_instructions
    )
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/content_processor/processor/content_processor.py", line 479, in _intelligently_populate_all_fields
    final_item = self._add_eeat_enhancements(final_item, item)
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/content_processor/processor/content_processor.py", line 581, in _add_eeat_enhancements
    final_item['case_studies'] = self._generate_case_studies(final_item)
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/content_processor/processor/content_processor.py", line 631, in _generate_case_studies
    if 'decision' in use_case.lower() or 'strategy' in use_case.lower():
                     ^^^^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'lower'
2025-06-24 08:10:09,661 - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-06-24 08:10:11,731 - INFO - Content processor connected to Redis event bus
2025-06-24 08:10:11,757 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:10:28,674 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:10:28,708 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:10:28,709 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:10:28,710 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:10:28,711 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:10:28,742 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:10:37,131 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:10:37,139 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:10:37,140 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:10:37,142 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:10:37,152 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:10:37,194 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:10:58,599 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:10:58,607 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:10:58,608 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:10:58,609 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:10:58,616 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:10:58,646 - INFO - Enhanced SEO for content: Complete Guide to Kokoro TTS MCP Server for Text-to-Speech Automation
2025-06-24 08:20:25,220 - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-06-24 08:20:27,214 - INFO - Content processor connected to Redis event bus
2025-06-24 08:20:27,252 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:20:43,801 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:20:43,827 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:20:43,828 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:20:43,829 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:20:43,831 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:20:43,877 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:20:56,975 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:20:56,987 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:20:56,988 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:20:56,988 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:20:56,997 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:20:57,043 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:21:21,216 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:21:21,221 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:21:21,225 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:21:21,225 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:21:21,240 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:21:21,282 - INFO - Enhanced SEO for content: Complete Guide to Installing and Using Last9 MCP Server for Real-Time Observability
2025-06-24 08:22:35,944 - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-06-24 08:22:38,280 - INFO - Content processor connected to Redis event bus
2025-06-24 08:22:38,314 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:22:52,962 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:22:52,990 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:22:52,991 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:22:52,991 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:22:52,992 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:22:53,035 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:23:02,173 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:23:02,174 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:23:02,174 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:23:02,174 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:23:02,176 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:23:02,192 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:23:15,055 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:23:15,083 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:23:15,084 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:23:15,101 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:23:15,141 - INFO - Enhanced SEO for content: imessage-query-fastmcp-mcp-server
2025-06-24 08:43:22,859 - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-06-24 08:43:25,953 - INFO - Content processor connected to Redis event bus
2025-06-24 08:43:26,062 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:43:27,761 - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-06-24 08:43:29,621 - INFO - Content processor connected to Redis event bus
2025-06-24 08:43:29,646 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:43:36,970 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:43:36,994 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:43:36,995 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:43:36,999 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:43:36,999 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:43:37,027 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:43:37,375 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 429 Too Many Requests"
2025-06-24 08:43:37,376 - INFO - Retrying request to /chat/completions in 51.000000 seconds
2025-06-24 08:43:51,120 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:43:51,138 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:43:51,139 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:43:51,139 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:43:51,140 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:43:51,154 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:43:51,432 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 429 Too Many Requests"
2025-06-24 08:43:51,432 - INFO - Retrying request to /chat/completions in 37.000000 seconds
2025-06-24 08:44:39,940 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:44:39,945 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:44:39,946 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:44:39,946 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:44:39,953 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:44:39,960 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:44:40,415 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:44:40,434 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:44:40,435 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:44:40,435 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:44:40,445 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:44:40,453 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 08:44:40,927 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 429 Too Many Requests"
2025-06-24 08:44:40,928 - INFO - Retrying request to /chat/completions in 49.000000 seconds
2025-06-24 08:45:01,383 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:45:01,391 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:45:01,392 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:45:01,392 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:45:01,399 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:45:53,122 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 08:45:53,127 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 08:45:53,128 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:45:53,128 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 08:45:53,138 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:29:56,678 - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-06-24 23:29:58,710 - INFO - Content processor connected to Redis event bus
2025-06-24 23:29:58,735 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 23:29:59,848 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 401 PermissionDenied"
2025-06-24 23:29:59,875 - ERROR - LiteLLM call failed: litellm.AuthenticationError: AzureException AuthenticationError - Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource.
2025-06-24 23:29:59,878 - ERROR - Content generation error: litellm.AuthenticationError: AzureException AuthenticationError - Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource.
Traceback (most recent call last):
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/litellm/llms/azure/azure.py", line 328, in completion
    headers, response = self.make_sync_azure_openai_chat_completion_request(
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        azure_client=azure_client, data=data, timeout=timeout
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/litellm/llms/azure/azure.py", line 148, in make_sync_azure_openai_chat_completion_request
    raise e
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/litellm/llms/azure/azure.py", line 140, in make_sync_azure_openai_chat_completion_request
    raw_response = azure_client.chat.completions.with_raw_response.create(
        **data, timeout=timeout
    )
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/openai/_legacy_response.py", line 364, in wrapped
    return cast(LegacyAPIResponse[R], func(*args, **kwargs))
                                      ~~~~^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/openai/_utils/_utils.py", line 287, in wrapper
    return func(*args, **kwargs)
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/openai/resources/chat/completions/completions.py", line 925, in create
    return self._post(
           ~~~~~~~~~~^
        "/chat/completions",
        ^^^^^^^^^^^^^^^^^^^^
    ...<43 lines>...
        stream_cls=Stream[ChatCompletionChunk],
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/openai/_base_client.py", line 1249, in post
    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))
                           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/openai/_base_client.py", line 1037, in request
    raise self._make_status_error_from_response(err.response) from None
openai.AuthenticationError: Error code: 401 - {'error': {'code': '401', 'message': 'Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource.'}}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/litellm/main.py", line 1364, in completion
    response = azure_chat_completions.completion(
        model=model,
    ...<17 lines>...
        client=client,  # pass AsyncAzureOpenAI, AzureOpenAI client
    )
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/litellm/llms/azure/azure.py", line 358, in completion
    raise AzureOpenAIError(
    ...<4 lines>...
    )
litellm.llms.azure.common_utils.AzureOpenAIError: Error code: 401 - {'error': {'code': '401', 'message': 'Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource.'}}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/run_content_generation.py", line 118, in run_content_generation
    processor.process_limited(max_repos)
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/content_processor/processor/content_processor.py", line 2114, in process_limited
    processed_batch = self.process_batch(batch)
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/content_processor/processor/content_processor.py", line 1634, in process_batch
    result = crew.kickoff()
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/crewai/crew.py", line 659, in kickoff
    result = self._run_sequential_process()
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/crewai/crew.py", line 768, in _run_sequential_process
    return self._execute_tasks(self.tasks)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/crewai/crew.py", line 871, in _execute_tasks
    task_output = task.execute_sync(
        agent=agent_to_use,
        context=context,
        tools=cast(List[BaseTool], tools_for_task),
    )
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/crewai/task.py", line 351, in execute_sync
    return self._execute_core(agent, context, tools)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/crewai/task.py", line 499, in _execute_core
    raise e  # Re-raise the exception after emitting the event
    ^^^^^^^
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/crewai/task.py", line 415, in _execute_core
    result = agent.execute_task(
        task=self,
        context=context,
        tools=tools,
    )
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/crewai/agent.py", line 435, in execute_task
    raise e
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/crewai/agent.py", line 411, in execute_task
    result = self._execute_without_timeout(task_prompt, task)
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/crewai/agent.py", line 507, in _execute_without_timeout
    return self.agent_executor.invoke(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~^
        {
        ^
    ...<4 lines>...
        }
        ^
    )["output"]
    ^
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/crewai/agents/crew_agent_executor.py", line 121, in invoke
    raise e
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/crewai/agents/crew_agent_executor.py", line 110, in invoke
    formatted_answer = self._invoke_loop()
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/crewai/agents/crew_agent_executor.py", line 206, in _invoke_loop
    raise e
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/crewai/agents/crew_agent_executor.py", line 153, in _invoke_loop
    answer = get_llm_response(
        llm=self.llm,
    ...<2 lines>...
        printer=self._printer,
    )
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/crewai/utilities/agent_utils.py", line 160, in get_llm_response
    raise e
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/crewai/utilities/agent_utils.py", line 151, in get_llm_response
    answer = llm.call(
        messages,
        callbacks=callbacks,
    )
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/crewai/llm.py", line 956, in call
    return self._handle_non_streaming_response(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        params, callbacks, available_functions
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/crewai/llm.py", line 768, in _handle_non_streaming_response
    response = litellm.completion(**params)
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/litellm/utils.py", line 1283, in wrapper
    raise e
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/litellm/utils.py", line 1161, in wrapper
    result = original_function(*args, **kwargs)
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/litellm/main.py", line 3241, in completion
    raise exception_type(
          ~~~~~~~~~~~~~~^
        model=model,
        ^^^^^^^^^^^^
    ...<3 lines>...
        extra_kwargs=kwargs,
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/litellm/litellm_core_utils/exception_mapping_utils.py", line 2239, in exception_type
    raise e
  File "/Users/<USER>/Documents/binary_products/handy_scripts/mcp_automated_pipeline/.venv/lib/python3.13/site-packages/litellm/litellm_core_utils/exception_mapping_utils.py", line 2024, in exception_type
    raise AuthenticationError(
    ...<5 lines>...
    )
litellm.exceptions.AuthenticationError: litellm.AuthenticationError: AzureException AuthenticationError - Access denied due to invalid subscription key or wrong API endpoint. Make sure to provide a valid key for an active subscription and use a correct regional API endpoint for your resource.
2025-06-24 23:30:41,693 - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-06-24 23:30:43,905 - INFO - Content processor connected to Redis event bus
2025-06-24 23:30:43,937 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 23:31:01,243 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 23:31:01,265 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 23:31:01,267 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:31:01,268 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:31:01,269 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:31:01,280 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 23:31:08,158 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 23:31:08,159 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 23:31:08,160 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:31:08,164 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:31:08,171 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 23:31:28,056 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 23:31:28,060 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 23:31:28,064 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:31:28,069 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:31:28,074 - INFO - Enhanced SEO for content: Framelink Figma MCP: Optimize Figma Design Metadata for AI Coding Tools
2025-06-24 23:31:28,100 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 23:31:40,279 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 23:31:40,292 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 23:31:40,294 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:31:40,294 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:31:40,304 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:31:40,320 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 23:31:40,557 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 429 Too Many Requests"
2025-06-24 23:31:40,558 - INFO - Retrying request to /chat/completions in 5.000000 seconds
2025-06-24 23:31:52,795 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 23:31:52,796 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 23:31:52,797 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:31:52,797 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:31:52,800 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:31:52,804 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 23:31:53,074 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 429 Too Many Requests"
2025-06-24 23:31:53,074 - INFO - Retrying request to /chat/completions in 9.000000 seconds
2025-06-24 23:32:33,068 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 23:32:33,076 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 23:32:33,077 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:32:33,084 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:32:33,092 - INFO - Enhanced SEO for content: Real-Time Crypto Fear & Greed Index MCP Server Guide
2025-06-24 23:32:33,111 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 23:32:44,476 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 23:32:44,481 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 23:32:44,482 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:32:44,483 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:32:44,489 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:32:44,496 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 23:32:55,229 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 23:32:55,246 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 23:32:55,250 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:32:55,251 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:32:55,262 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:32:55,270 - INFO - 
LiteLLM completion() model= article-generator-trail; provider = azure
2025-06-24 23:33:17,031 - INFO - HTTP Request: POST https://bincha-ai.openai.azure.com/openai/deployments/article-generator-trail/chat/completions?api-version=2024-08-01-preview "HTTP/1.1 200 OK"
2025-06-24 23:33:17,035 - INFO - Wrapper: Completed Call, calling success_handler
2025-06-24 23:33:17,035 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:33:17,039 - INFO - selected model name for cost calculation: azure/gpt-4o-2024-11-20
2025-06-24 23:33:17,056 - INFO - Enhanced SEO for content: mcp-graphql-forge
2025-06-25 09:29:08,649 - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-06-25 09:29:10,777 - INFO - Content processor connected to Redis event bus
