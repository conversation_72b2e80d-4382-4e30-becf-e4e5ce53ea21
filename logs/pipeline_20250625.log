2025-06-25 07:54:02,467 - pipeline.core.data_sync_manager - WARNING - _init_redis:71 - Red<PERSON> connection failed: <PERSON>rror 61 connecting to localhost:6379. Connection refused.
2025-06-25 07:54:02,468 - pipeline.core.data_sync_manager - INFO - sync_redis_from_csv:328 - Redis not available, skipping sync
2025-06-25 07:54:02,468 - __main__ - INFO - initialize:125 - 🚀 Initializing Enhanced MCP Pipeline System
2025-06-25 07:54:02,468 - __main__ - ERROR - initialize:142 - ❌ Failed to initialize pipeline system: 'StateManager' object has no attribute 'logger'
2025-06-25 07:54:02,468 - __main__ - INFO - cleanup:400 - 🧹 Cleaning up resources...
2025-06-25 07:54:02,468 - __main__ - INFO - cleanup:405 - ✅ Cleanup complete
2025-06-25 07:54:35,556 - pipeline.core.data_sync_manager - INFO - _init_redis:69 - Connected to Redis for data synchronization
2025-06-25 07:54:35,559 - pipeline.core.data_sync_manager - ERROR - sync_redis_from_csv:365 - Error syncing Redis from CSV: Invalid input of type: 'list'. Convert to a bytes, string, int or float first.
2025-06-25 07:54:35,559 - __main__ - INFO - initialize:125 - 🚀 Initializing Enhanced MCP Pipeline System
2025-06-25 07:54:35,560 - __main__ - ERROR - initialize:142 - ❌ Failed to initialize pipeline system: 'StateManager' object has no attribute 'logger'
2025-06-25 07:54:35,560 - __main__ - INFO - cleanup:400 - 🧹 Cleaning up resources...
2025-06-25 07:54:35,560 - __main__ - INFO - cleanup:405 - ✅ Cleanup complete
2025-06-25 07:55:14,315 - pipeline.core.data_sync_manager - INFO - _init_redis:69 - Connected to Redis for data synchronization
2025-06-25 07:55:14,321 - pipeline.core.data_sync_manager - ERROR - sync_redis_from_csv:365 - Error syncing Redis from CSV: Invalid input of type: 'list'. Convert to a bytes, string, int or float first.
2025-06-25 07:55:14,321 - __main__ - INFO - initialize:125 - 🚀 Initializing Enhanced MCP Pipeline System
2025-06-25 07:55:14,322 - __main__ - ERROR - initialize:142 - ❌ Failed to initialize pipeline system: 'StateManager' object has no attribute 'logger'
2025-06-25 07:55:14,323 - __main__ - INFO - cleanup:400 - 🧹 Cleaning up resources...
2025-06-25 07:55:14,323 - __main__ - INFO - cleanup:405 - ✅ Cleanup complete
2025-06-25 09:19:03,015 - pipeline.core.data_sync_manager - WARNING - _init_redis:71 - Redis connection failed: Error 61 connecting to localhost:6379. Connection refused.
2025-06-25 09:19:03,015 - pipeline.core.data_sync_manager - INFO - sync_redis_from_csv:328 - Redis not available, skipping sync
2025-06-25 09:19:03,015 - __main__ - INFO - initialize:125 - 🚀 Initializing Enhanced MCP Pipeline System
2025-06-25 09:19:03,015 - __main__ - ERROR - initialize:142 - ❌ Failed to initialize pipeline system: 'StateManager' object has no attribute 'logger'
2025-06-25 09:19:03,015 - __main__ - INFO - cleanup:400 - 🧹 Cleaning up resources...
2025-06-25 09:19:03,015 - __main__ - INFO - cleanup:405 - ✅ Cleanup complete
2025-06-25 09:22:12,884 - pipeline.core.data_sync_manager - WARNING - _init_redis:71 - Redis connection failed: Error 61 connecting to localhost:6379. Connection refused.
2025-06-25 09:22:12,884 - pipeline.core.data_sync_manager - INFO - sync_redis_from_csv:328 - Redis not available, skipping sync
2025-06-25 09:22:12,884 - __main__ - INFO - initialize:125 - 🚀 Initializing Enhanced MCP Pipeline System
2025-06-25 09:22:12,884 - __main__ - ERROR - initialize:142 - ❌ Failed to initialize pipeline system: 'StateManager' object has no attribute 'logger'
2025-06-25 09:22:12,884 - __main__ - INFO - cleanup:400 - 🧹 Cleaning up resources...
2025-06-25 09:22:12,884 - __main__ - INFO - cleanup:405 - ✅ Cleanup complete
2025-06-25 09:24:04,300 - pipeline.core.data_sync_manager - WARNING - _init_redis:71 - Redis connection failed: Error 61 connecting to localhost:6379. Connection refused.
2025-06-25 09:24:04,301 - pipeline.core.data_sync_manager - INFO - sync_redis_from_csv:328 - Redis not available, skipping sync
2025-06-25 09:24:04,301 - __main__ - INFO - initialize:125 - 🚀 Initializing Enhanced MCP Pipeline System
2025-06-25 09:24:04,301 - pipeline.core.state_manager - WARNING - _init_redis:77 - Redis not available, using file-based state management
2025-06-25 09:24:04,302 - pipeline.orchestrator.pipeline_orchestrator - INFO - initialize:107 - Initializing Pipeline Orchestrator
2025-06-25 09:24:04,302 - pipeline.orchestrator.pipeline_orchestrator - INFO - _start_scheduler:489 - Started workflow scheduler
2025-06-25 09:24:04,302 - pipeline.orchestrator.pipeline_orchestrator - INFO - initialize:115 - Pipeline Orchestrator initialized successfully
2025-06-25 09:24:04,303 - __main__ - INFO - initialize:138 - ✅ Pipeline system initialized successfully
2025-06-25 09:24:04,303 - __main__ - INFO - show_status:350 - 📊 Pipeline Status Report
2025-06-25 09:24:04,303 - __main__ - INFO - show_status:354 - 💾 Data Synchronization Status:
2025-06-25 09:24:04,303 - __main__ - INFO - show_status:355 -   Redis Available: False
2025-06-25 09:24:04,303 - __main__ - INFO - show_status:356 -   CSV Scraped Count: 0
2025-06-25 09:24:04,303 - __main__ - INFO - show_status:357 -   CSV Content Count: 3
2025-06-25 09:24:04,303 - __main__ - INFO - show_status:366 - 📝 Repositories needing content generation: 0
2025-06-25 09:24:04,303 - __main__ - INFO - show_status:372 - 📭 No active workflows
2025-06-25 09:24:04,303 - __main__ - INFO - cleanup:400 - 🧹 Cleaning up resources...
2025-06-25 09:24:04,304 - pipeline.orchestrator.pipeline_orchestrator - INFO - shutdown:652 - Shutting down Pipeline Orchestrator
2025-06-25 09:24:09,307 - pipeline.orchestrator.pipeline_orchestrator - INFO - shutdown:675 - Pipeline Orchestrator shutdown complete
2025-06-25 09:24:09,311 - __main__ - INFO - cleanup:405 - ✅ Cleanup complete
2025-06-25 09:24:40,943 - pipeline.core.data_sync_manager - WARNING - _init_redis:71 - Redis connection failed: Error 61 connecting to localhost:6379. Connection refused.
2025-06-25 09:24:40,943 - pipeline.core.data_sync_manager - INFO - sync_redis_from_csv:328 - Redis not available, skipping sync
2025-06-25 09:24:40,943 - __main__ - INFO - initialize:125 - 🚀 Initializing Enhanced MCP Pipeline System
2025-06-25 09:24:40,943 - pipeline.core.state_manager - WARNING - _init_redis:77 - Redis not available, using file-based state management
2025-06-25 09:24:40,943 - pipeline.orchestrator.pipeline_orchestrator - INFO - initialize:107 - Initializing Pipeline Orchestrator
2025-06-25 09:24:40,944 - pipeline.orchestrator.pipeline_orchestrator - INFO - _load_workflows:551 - Loaded 0 workflows
2025-06-25 09:24:40,944 - pipeline.orchestrator.pipeline_orchestrator - INFO - _start_scheduler:489 - Started workflow scheduler
2025-06-25 09:24:40,944 - pipeline.orchestrator.pipeline_orchestrator - INFO - initialize:115 - Pipeline Orchestrator initialized successfully
2025-06-25 09:24:40,944 - __main__ - INFO - initialize:138 - ✅ Pipeline system initialized successfully
2025-06-25 09:24:40,944 - __main__ - INFO - run_scraping_pipeline:152 - 🕷️ Starting scraping pipeline: scrape_20250625_092440
2025-06-25 09:24:40,944 - __main__ - INFO - run_scraping_pipeline:170 - 📊 URL Analysis: 1 total, 1 to process, 0 skipped
2025-06-25 09:24:40,944 - pipeline.orchestrator.pipeline_orchestrator - ERROR - _save_workflows:515 - Failed to save workflows: Object of type DataSyncManager is not JSON serializable
2025-06-25 09:24:40,944 - pipeline.orchestrator.pipeline_orchestrator - INFO - create_workflow:139 - Created workflow scrape_20250625_092440 of type scrape_only
2025-06-25 09:24:40,945 - pipeline.orchestrator.pipeline_orchestrator - INFO - run_workflow:163 - Starting workflow scrape_20250625_092440
2025-06-25 09:24:40,945 - pipeline.core.state_manager - WARNING - _init_redis:77 - Redis not available, using file-based state management
2025-06-25 09:24:40,945 - pipeline.scrapers.resumable_scraper.scrape_20250625_092440 - INFO - initialize:106 - Initializing scraper for pipeline scrape_20250625_092440
2025-06-25 09:24:40,951 - pipeline.core.url_manager - INFO - add_urls:198 - Added 1 new URLs to collection 'scrape_20250625_092440'
2025-06-25 09:24:40,952 - pipeline.core.state_manager - INFO - create_pipeline_state:170 - Created state for pipeline scrape_20250625_092440 with 1 tasks
2025-06-25 09:24:40,952 - pipeline.scrapers.resumable_scraper.scrape_20250625_092440 - INFO - initialize:129 - Fresh start: Added 1 URLs to pipeline
2025-06-25 09:24:40,952 - pipeline.scrapers.resumable_scraper.scrape_20250625_092440 - INFO - start_scraping:166 - Starting scraping for pipeline scrape_20250625_092440
2025-06-25 09:24:42,975 - enhanced_content_analyzer - INFO - analyze_repository:131 - Enhanced analysis completed for microsoft/vscode
2025-06-25 09:24:42,977 - pipeline.scrapers.resumable_scraper.scrape_20250625_092440 - WARNING - _process_single_url:324 - Failed to scrape https://github.com/microsoft/vscode (attempt 0): dict contains fields not in fieldnames: 'owner', 'repo'
2025-06-25 09:24:47,535 - enhanced_content_analyzer - INFO - analyze_repository:131 - Enhanced analysis completed for microsoft/vscode
2025-06-25 09:24:47,537 - pipeline.scrapers.resumable_scraper.scrape_20250625_092440 - WARNING - _process_single_url:324 - Failed to scrape https://github.com/microsoft/vscode (attempt 1): dict contains fields not in fieldnames: 'owner', 'repo'
2025-06-25 09:24:52,014 - enhanced_content_analyzer - INFO - analyze_repository:131 - Enhanced analysis completed for microsoft/vscode
2025-06-25 09:24:52,017 - pipeline.scrapers.resumable_scraper.scrape_20250625_092440 - WARNING - _process_single_url:324 - Failed to scrape https://github.com/microsoft/vscode (attempt 2): dict contains fields not in fieldnames: 'owner', 'repo'
2025-06-25 09:24:56,618 - enhanced_content_analyzer - INFO - analyze_repository:131 - Enhanced analysis completed for microsoft/vscode
2025-06-25 09:24:56,621 - pipeline.scrapers.resumable_scraper.scrape_20250625_092440 - ERROR - _process_single_url:329 - Failed to scrape https://github.com/microsoft/vscode after 3 attempts: dict contains fields not in fieldnames: 'owner', 'repo'
2025-06-25 09:25:00,624 - pipeline.scrapers.resumable_scraper.scrape_20250625_092440 - INFO - start_scraping:186 - No more URLs to process
2025-06-25 09:25:00,625 - pipeline.scrapers.resumable_scraper.scrape_20250625_092440 - INFO - start_scraping:210 - Scraping completed for pipeline scrape_20250625_092440
2025-06-25 09:25:00,625 - pipeline.orchestrator.pipeline_orchestrator - INFO - run_workflow:171 - Workflow scrape_20250625_092440 completed successfully
2025-06-25 09:25:00,625 - pipeline.orchestrator.pipeline_orchestrator - ERROR - _save_workflows:515 - Failed to save workflows: Object of type DataSyncManager is not JSON serializable
2025-06-25 09:25:00,625 - __main__ - INFO - run_scraping_pipeline:192 - ✅ Scraping pipeline scrape_20250625_092440 completed successfully
2025-06-25 09:25:00,625 - pipeline.core.data_sync_manager - INFO - sync_csv_from_redis:371 - Redis not available, skipping sync
2025-06-25 09:25:00,625 - __main__ - INFO - cleanup:400 - 🧹 Cleaning up resources...
2025-06-25 09:25:00,625 - pipeline.orchestrator.pipeline_orchestrator - INFO - shutdown:652 - Shutting down Pipeline Orchestrator
2025-06-25 09:25:05,631 - pipeline.orchestrator.pipeline_orchestrator - ERROR - _save_workflows:515 - Failed to save workflows: Object of type DataSyncManager is not JSON serializable
2025-06-25 09:25:05,631 - pipeline.orchestrator.pipeline_orchestrator - INFO - shutdown:675 - Pipeline Orchestrator shutdown complete
2025-06-25 09:25:05,631 - __main__ - INFO - cleanup:405 - ✅ Cleanup complete
2025-06-25 09:26:17,338 - pipeline.core.data_sync_manager - WARNING - _init_redis:71 - Redis connection failed: Error 61 connecting to localhost:6379. Connection refused.
2025-06-25 09:26:17,338 - pipeline.core.data_sync_manager - INFO - sync_redis_from_csv:328 - Redis not available, skipping sync
2025-06-25 09:26:17,338 - __main__ - INFO - initialize:125 - 🚀 Initializing Enhanced MCP Pipeline System
2025-06-25 09:26:17,339 - pipeline.core.state_manager - WARNING - _init_redis:77 - Redis not available, using file-based state management
2025-06-25 09:26:17,339 - pipeline.orchestrator.pipeline_orchestrator - INFO - initialize:119 - Initializing Pipeline Orchestrator
2025-06-25 09:26:17,339 - pipeline.orchestrator.pipeline_orchestrator - ERROR - _load_workflows:566 - Failed to load workflows: Expecting value: line 15 column 28 (char 362)
2025-06-25 09:26:17,340 - pipeline.orchestrator.pipeline_orchestrator - INFO - _start_scheduler:501 - Started workflow scheduler
2025-06-25 09:26:17,340 - pipeline.orchestrator.pipeline_orchestrator - INFO - initialize:127 - Pipeline Orchestrator initialized successfully
2025-06-25 09:26:17,340 - __main__ - INFO - initialize:138 - ✅ Pipeline system initialized successfully
2025-06-25 09:26:17,340 - __main__ - INFO - run_scraping_pipeline:152 - 🕷️ Starting scraping pipeline: scrape_20250625_092617
2025-06-25 09:26:17,343 - __main__ - INFO - run_scraping_pipeline:170 - 📊 URL Analysis: 1 total, 1 to process, 0 skipped
2025-06-25 09:26:17,344 - pipeline.orchestrator.pipeline_orchestrator - INFO - create_workflow:151 - Created workflow scrape_20250625_092617 of type scrape_only
2025-06-25 09:26:17,344 - pipeline.orchestrator.pipeline_orchestrator - INFO - run_workflow:175 - Starting workflow scrape_20250625_092617
2025-06-25 09:26:17,344 - pipeline.core.state_manager - WARNING - _init_redis:77 - Redis not available, using file-based state management
2025-06-25 09:26:17,344 - pipeline.scrapers.resumable_scraper.scrape_20250625_092617 - INFO - initialize:106 - Initializing scraper for pipeline scrape_20250625_092617
2025-06-25 09:26:17,346 - pipeline.core.url_manager - INFO - add_urls:198 - Added 1 new URLs to collection 'scrape_20250625_092617'
2025-06-25 09:26:17,346 - pipeline.core.state_manager - INFO - create_pipeline_state:170 - Created state for pipeline scrape_20250625_092617 with 1 tasks
2025-06-25 09:26:17,346 - pipeline.scrapers.resumable_scraper.scrape_20250625_092617 - INFO - initialize:129 - Fresh start: Added 1 URLs to pipeline
2025-06-25 09:26:17,346 - pipeline.scrapers.resumable_scraper.scrape_20250625_092617 - INFO - start_scraping:166 - Starting scraping for pipeline scrape_20250625_092617
2025-06-25 09:26:19,024 - enhanced_content_analyzer - INFO - analyze_repository:131 - Enhanced analysis completed for microsoft/vscode
2025-06-25 09:26:23,030 - pipeline.scrapers.resumable_scraper.scrape_20250625_092617 - INFO - start_scraping:186 - No more URLs to process
2025-06-25 09:26:23,031 - pipeline.scrapers.resumable_scraper.scrape_20250625_092617 - INFO - start_scraping:210 - Scraping completed for pipeline scrape_20250625_092617
2025-06-25 09:26:23,032 - pipeline.orchestrator.pipeline_orchestrator - INFO - run_workflow:183 - Workflow scrape_20250625_092617 completed successfully
2025-06-25 09:26:23,032 - __main__ - INFO - run_scraping_pipeline:192 - ✅ Scraping pipeline scrape_20250625_092617 completed successfully
2025-06-25 09:26:23,032 - pipeline.core.data_sync_manager - INFO - sync_csv_from_redis:371 - Redis not available, skipping sync
2025-06-25 09:26:23,032 - __main__ - INFO - cleanup:400 - 🧹 Cleaning up resources...
2025-06-25 09:26:23,032 - pipeline.orchestrator.pipeline_orchestrator - INFO - shutdown:664 - Shutting down Pipeline Orchestrator
2025-06-25 09:26:28,038 - pipeline.orchestrator.pipeline_orchestrator - INFO - shutdown:687 - Pipeline Orchestrator shutdown complete
2025-06-25 09:26:28,038 - __main__ - INFO - cleanup:405 - ✅ Cleanup complete
2025-06-25 09:26:59,400 - pipeline.core.data_sync_manager - WARNING - _init_redis:71 - Redis connection failed: Error 61 connecting to localhost:6379. Connection refused.
2025-06-25 09:26:59,400 - pipeline.core.data_sync_manager - INFO - sync_redis_from_csv:328 - Redis not available, skipping sync
2025-06-25 09:26:59,400 - __main__ - INFO - initialize:125 - 🚀 Initializing Enhanced MCP Pipeline System
2025-06-25 09:26:59,400 - pipeline.core.state_manager - WARNING - _init_redis:77 - Redis not available, using file-based state management
2025-06-25 09:26:59,400 - pipeline.orchestrator.pipeline_orchestrator - INFO - initialize:119 - Initializing Pipeline Orchestrator
2025-06-25 09:26:59,401 - pipeline.orchestrator.pipeline_orchestrator - INFO - _load_workflows:563 - Loaded 1 workflows
2025-06-25 09:26:59,401 - pipeline.orchestrator.pipeline_orchestrator - INFO - _start_scheduler:501 - Started workflow scheduler
2025-06-25 09:26:59,401 - pipeline.orchestrator.pipeline_orchestrator - INFO - initialize:127 - Pipeline Orchestrator initialized successfully
2025-06-25 09:26:59,401 - __main__ - INFO - initialize:138 - ✅ Pipeline system initialized successfully
2025-06-25 09:26:59,401 - __main__ - INFO - run_content_generation_pipeline:212 - 📝 Starting content generation pipeline: generate_20250625_092659
2025-06-25 09:26:59,406 - __main__ - INFO - run_content_generation_pipeline:224 - 📊 Found 1 repositories without generated content
2025-06-25 09:26:59,407 - pipeline.orchestrator.pipeline_orchestrator - INFO - create_workflow:151 - Created workflow generate_20250625_092659 of type generate_only
2025-06-25 09:26:59,407 - pipeline.orchestrator.pipeline_orchestrator - INFO - run_workflow:175 - Starting workflow generate_20250625_092659
2025-06-25 09:26:59,407 - pipeline.core.state_manager - WARNING - _init_redis:77 - Redis not available, using file-based state management
2025-06-25 09:26:59,407 - pipeline.generators.resumable_generator.generate_20250625_092659 - INFO - initialize:110 - Initializing content generator for pipeline generate_20250625_092659
2025-06-25 09:26:59,409 - pipeline.generators.resumable_generator.generate_20250625_092659 - INFO - initialize:128 - Content processor initialized successfully
2025-06-25 09:26:59,411 - pipeline.core.url_manager - INFO - add_urls:198 - Added 1 new URLs to collection 'generate_20250625_092659_content'
2025-06-25 09:26:59,411 - pipeline.core.state_manager - INFO - create_pipeline_state:170 - Created state for pipeline generate_20250625_092659_content with 1 tasks
2025-06-25 09:26:59,411 - pipeline.generators.resumable_generator.generate_20250625_092659 - INFO - initialize:158 - Fresh start: Prepared 1 items for content generation
2025-06-25 09:26:59,411 - pipeline.generators.resumable_generator.generate_20250625_092659 - INFO - start_generation:219 - Starting content generation for pipeline generate_20250625_092659
2025-06-25 09:27:00,418 - pipeline.generators.resumable_generator.generate_20250625_092659 - INFO - start_generation:243 - No more items to process for content generation
2025-06-25 09:27:00,419 - pipeline.generators.resumable_generator.generate_20250625_092659 - INFO - start_generation:282 - Content generation completed for pipeline generate_20250625_092659
2025-06-25 09:27:00,420 - pipeline.orchestrator.pipeline_orchestrator - INFO - run_workflow:183 - Workflow generate_20250625_092659 completed successfully
2025-06-25 09:27:00,421 - __main__ - INFO - run_content_generation_pipeline:246 - ✅ Content generation pipeline generate_20250625_092659 completed successfully
2025-06-25 09:27:00,421 - pipeline.core.data_sync_manager - INFO - sync_csv_from_redis:371 - Redis not available, skipping sync
2025-06-25 09:27:00,421 - __main__ - INFO - cleanup:400 - 🧹 Cleaning up resources...
2025-06-25 09:27:00,421 - pipeline.orchestrator.pipeline_orchestrator - INFO - shutdown:664 - Shutting down Pipeline Orchestrator
2025-06-25 09:27:05,426 - pipeline.orchestrator.pipeline_orchestrator - INFO - shutdown:687 - Pipeline Orchestrator shutdown complete
2025-06-25 09:27:05,426 - __main__ - INFO - cleanup:405 - ✅ Cleanup complete
2025-06-25 09:28:17,122 - pipeline.core.data_sync_manager - INFO - _init_redis:69 - Connected to Redis for data synchronization
2025-06-25 09:28:17,385 - pipeline.core.data_sync_manager - ERROR - sync_redis_from_csv:365 - Error syncing Redis from CSV: Invalid input of type: 'list'. Convert to a bytes, string, int or float first.
2025-06-25 09:28:17,387 - __main__ - INFO - initialize:125 - 🚀 Initializing Enhanced MCP Pipeline System
2025-06-25 09:28:17,388 - pipeline.core.state_manager - WARNING - _init_redis:77 - Redis not available, using file-based state management
2025-06-25 09:28:17,388 - pipeline.orchestrator.pipeline_orchestrator - INFO - initialize:119 - Initializing Pipeline Orchestrator
2025-06-25 09:28:17,389 - pipeline.orchestrator.pipeline_orchestrator - INFO - _load_workflows:563 - Loaded 2 workflows
2025-06-25 09:28:17,389 - pipeline.orchestrator.pipeline_orchestrator - INFO - _start_scheduler:501 - Started workflow scheduler
2025-06-25 09:28:17,390 - pipeline.orchestrator.pipeline_orchestrator - INFO - initialize:127 - Pipeline Orchestrator initialized successfully
2025-06-25 09:28:17,390 - __main__ - INFO - initialize:138 - ✅ Pipeline system initialized successfully
2025-06-25 09:28:17,390 - __main__ - INFO - run_scraping_pipeline:152 - 🕷️ Starting scraping pipeline: scrape_20250625_092817
2025-06-25 09:28:17,648 - __main__ - INFO - run_scraping_pipeline:170 - 📊 URL Analysis: 1 total, 1 to process, 0 skipped
2025-06-25 09:28:17,650 - pipeline.orchestrator.pipeline_orchestrator - INFO - create_workflow:151 - Created workflow scrape_20250625_092817 of type scrape_only
2025-06-25 09:28:17,650 - pipeline.orchestrator.pipeline_orchestrator - INFO - run_workflow:175 - Starting workflow scrape_20250625_092817
2025-06-25 09:28:17,650 - pipeline.core.state_manager - WARNING - _init_redis:77 - Redis not available, using file-based state management
2025-06-25 09:28:17,650 - pipeline.scrapers.resumable_scraper.scrape_20250625_092817 - INFO - initialize:106 - Initializing scraper for pipeline scrape_20250625_092817
2025-06-25 09:28:17,655 - pipeline.core.url_manager - INFO - add_urls:198 - Added 1 new URLs to collection 'scrape_20250625_092817'
2025-06-25 09:28:17,656 - pipeline.core.state_manager - INFO - create_pipeline_state:170 - Created state for pipeline scrape_20250625_092817 with 1 tasks
2025-06-25 09:28:17,656 - pipeline.scrapers.resumable_scraper.scrape_20250625_092817 - INFO - initialize:129 - Fresh start: Added 1 URLs to pipeline
2025-06-25 09:28:17,656 - pipeline.scrapers.resumable_scraper.scrape_20250625_092817 - INFO - start_scraping:166 - Starting scraping for pipeline scrape_20250625_092817
2025-06-25 09:28:19,452 - enhanced_content_analyzer - INFO - analyze_repository:131 - Enhanced analysis completed for openai/whisper
2025-06-25 09:28:23,457 - pipeline.scrapers.resumable_scraper.scrape_20250625_092817 - INFO - start_scraping:186 - No more URLs to process
2025-06-25 09:28:23,458 - pipeline.scrapers.resumable_scraper.scrape_20250625_092817 - INFO - start_scraping:210 - Scraping completed for pipeline scrape_20250625_092817
2025-06-25 09:28:23,459 - pipeline.orchestrator.pipeline_orchestrator - INFO - run_workflow:183 - Workflow scrape_20250625_092817 completed successfully
2025-06-25 09:28:23,460 - __main__ - INFO - run_scraping_pipeline:192 - ✅ Scraping pipeline scrape_20250625_092817 completed successfully
2025-06-25 09:28:24,995 - pipeline.core.data_sync_manager - INFO - sync_csv_from_redis:406 - Synced 3 scraped items and 2 content items from Redis to CSV
2025-06-25 09:28:24,996 - __main__ - INFO - cleanup:400 - 🧹 Cleaning up resources...
2025-06-25 09:28:24,996 - pipeline.orchestrator.pipeline_orchestrator - INFO - shutdown:664 - Shutting down Pipeline Orchestrator
2025-06-25 09:28:30,002 - pipeline.orchestrator.pipeline_orchestrator - INFO - shutdown:687 - Pipeline Orchestrator shutdown complete
2025-06-25 09:28:30,003 - __main__ - INFO - cleanup:405 - ✅ Cleanup complete
2025-06-25 09:28:48,137 - pipeline.core.data_sync_manager - INFO - _init_redis:69 - Connected to Redis for data synchronization
2025-06-25 09:28:48,140 - pipeline.core.data_sync_manager - ERROR - sync_redis_from_csv:365 - Error syncing Redis from CSV: Invalid input of type: 'bool'. Convert to a bytes, string, int or float first.
2025-06-25 09:28:48,140 - __main__ - INFO - initialize:125 - 🚀 Initializing Enhanced MCP Pipeline System
2025-06-25 09:28:48,140 - pipeline.core.state_manager - WARNING - _init_redis:77 - Redis not available, using file-based state management
2025-06-25 09:28:48,140 - pipeline.orchestrator.pipeline_orchestrator - INFO - initialize:119 - Initializing Pipeline Orchestrator
2025-06-25 09:28:48,141 - pipeline.orchestrator.pipeline_orchestrator - INFO - _load_workflows:563 - Loaded 3 workflows
2025-06-25 09:28:48,141 - pipeline.orchestrator.pipeline_orchestrator - INFO - _start_scheduler:501 - Started workflow scheduler
2025-06-25 09:28:48,141 - pipeline.orchestrator.pipeline_orchestrator - INFO - initialize:127 - Pipeline Orchestrator initialized successfully
2025-06-25 09:28:48,141 - __main__ - INFO - initialize:138 - ✅ Pipeline system initialized successfully
2025-06-25 09:28:48,141 - __main__ - INFO - run_content_generation_pipeline:212 - 📝 Starting content generation pipeline: generate_20250625_092848
2025-06-25 09:28:49,878 - __main__ - INFO - run_content_generation_pipeline:224 - 📊 Found 0 repositories without generated content
2025-06-25 09:28:49,878 - __main__ - INFO - run_content_generation_pipeline:227 - ✅ All repositories already have generated content, nothing to do
2025-06-25 09:28:49,878 - __main__ - INFO - cleanup:400 - 🧹 Cleaning up resources...
2025-06-25 09:28:49,878 - pipeline.orchestrator.pipeline_orchestrator - INFO - shutdown:664 - Shutting down Pipeline Orchestrator
2025-06-25 09:28:54,883 - pipeline.orchestrator.pipeline_orchestrator - INFO - shutdown:687 - Pipeline Orchestrator shutdown complete
2025-06-25 09:28:54,883 - __main__ - INFO - cleanup:405 - ✅ Cleanup complete
