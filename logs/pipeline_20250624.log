2025-06-24 23:45:10,688 - pipeline.core.data_sync_manager - INFO - _init_redis:69 - Connected to Red<PERSON> for data synchronization
2025-06-24 23:45:10,692 - pipeline.core.data_sync_manager - ERROR - sync_redis_from_csv:365 - Error syncing Redis from CSV: Invalid input of type: 'bool'. Convert to a bytes, string, int or float first.
2025-06-24 23:45:10,693 - __main__ - INFO - initialize:125 - 🚀 Initializing Enhanced MCP Pipeline System
2025-06-24 23:45:10,693 - __main__ - ERROR - initialize:142 - ❌ Failed to initialize pipeline system: 'StateManager' object has no attribute 'logger'
2025-06-24 23:45:10,693 - __main__ - INFO - cleanup:400 - 🧹 Cleaning up resources...
2025-06-24 23:45:10,693 - __main__ - INFO - cleanup:405 - ✅ Cleanup complete
2025-06-24 23:45:20,253 - pipeline.core.data_sync_manager - INFO - _init_redis:69 - Connected to <PERSON><PERSON> for data synchronization
2025-06-24 23:45:20,262 - pipeline.core.data_sync_manager - ERROR - sync_redis_from_csv:365 - Error syncing Redis from CSV: Invalid input of type: 'bool'. Convert to a bytes, string, int or float first.
2025-06-24 23:45:20,264 - __main__ - INFO - initialize:125 - 🚀 Initializing Enhanced MCP Pipeline System
2025-06-24 23:45:20,265 - __main__ - ERROR - initialize:142 - ❌ Failed to initialize pipeline system: 'StateManager' object has no attribute 'logger'
2025-06-24 23:45:20,265 - __main__ - INFO - cleanup:400 - 🧹 Cleaning up resources...
2025-06-24 23:45:20,265 - __main__ - INFO - cleanup:405 - ✅ Cleanup complete
