2025-06-26 08:28:46,577 - pipeline.core.data_sync_manager - WARNING - _init_redis:71 - Redis connection failed: Error 61 connecting to localhost:6379. Connection refused.
2025-06-26 08:28:46,577 - pipeline.core.data_sync_manager - INFO - sync_redis_from_csv:328 - Redis not available, skipping sync
2025-06-26 08:28:46,578 - __main__ - INFO - initialize:125 - 🚀 Initializing Enhanced MCP Pipeline System
2025-06-26 08:28:46,579 - pipeline.core.state_manager - WARNING - _init_redis:77 - Redis not available, using file-based state management
2025-06-26 08:28:46,579 - pipeline.orchestrator.pipeline_orchestrator - INFO - initialize:119 - Initializing Pipeline Orchestrator
2025-06-26 08:28:46,580 - pipeline.orchestrator.pipeline_orchestrator - INFO - _load_workflows:563 - Loaded 3 workflows
2025-06-26 08:28:46,580 - pipeline.orchestrator.pipeline_orchestrator - INFO - _start_scheduler:501 - Started workflow scheduler
2025-06-26 08:28:46,580 - pipeline.orchestrator.pipeline_orchestrator - INFO - initialize:127 - Pipeline Orchestrator initialized successfully
2025-06-26 08:28:46,580 - __main__ - INFO - initialize:138 - ✅ Pipeline system initialized successfully
2025-06-26 08:28:46,580 - __main__ - INFO - cleanup:400 - 🧹 Cleaning up resources...
2025-06-26 08:28:46,580 - pipeline.orchestrator.pipeline_orchestrator - INFO - shutdown:664 - Shutting down Pipeline Orchestrator
2025-06-26 08:28:51,659 - pipeline.orchestrator.pipeline_orchestrator - INFO - shutdown:687 - Pipeline Orchestrator shutdown complete
2025-06-26 08:28:51,664 - __main__ - INFO - cleanup:405 - ✅ Cleanup complete
2025-06-26 08:29:54,864 - pipeline.core.data_sync_manager - INFO - _init_redis:69 - Connected to Redis for data synchronization
2025-06-26 08:29:55,172 - pipeline.core.data_sync_manager - ERROR - sync_redis_from_csv:365 - Error syncing Redis from CSV: Invalid input of type: 'bool'. Convert to a bytes, string, int or float first.
2025-06-26 08:29:55,172 - __main__ - INFO - initialize:125 - 🚀 Initializing Enhanced MCP Pipeline System
2025-06-26 08:29:55,173 - pipeline.core.state_manager - WARNING - _init_redis:77 - Redis not available, using file-based state management
2025-06-26 08:29:55,173 - pipeline.orchestrator.pipeline_orchestrator - INFO - initialize:119 - Initializing Pipeline Orchestrator
2025-06-26 08:29:55,175 - pipeline.orchestrator.pipeline_orchestrator - INFO - _load_workflows:563 - Loaded 3 workflows
2025-06-26 08:29:55,176 - pipeline.orchestrator.pipeline_orchestrator - INFO - _start_scheduler:501 - Started workflow scheduler
2025-06-26 08:29:55,176 - pipeline.orchestrator.pipeline_orchestrator - INFO - initialize:127 - Pipeline Orchestrator initialized successfully
2025-06-26 08:29:55,176 - __main__ - INFO - initialize:138 - ✅ Pipeline system initialized successfully
2025-06-26 08:29:55,176 - __main__ - INFO - cleanup:400 - 🧹 Cleaning up resources...
2025-06-26 08:29:55,176 - pipeline.orchestrator.pipeline_orchestrator - INFO - shutdown:664 - Shutting down Pipeline Orchestrator
2025-06-26 08:30:00,184 - pipeline.orchestrator.pipeline_orchestrator - INFO - shutdown:687 - Pipeline Orchestrator shutdown complete
2025-06-26 08:30:00,184 - __main__ - INFO - cleanup:405 - ✅ Cleanup complete
2025-06-26 08:37:30,442 - pipeline.core.data_sync_manager - INFO - _init_redis:69 - Connected to Redis for data synchronization
2025-06-26 08:37:30,471 - pipeline.core.data_sync_manager - INFO - sync_redis_from_csv:386 - Synced 0 items from CSV to Redis
2025-06-26 08:37:30,473 - __main__ - INFO - initialize:125 - 🚀 Initializing Enhanced MCP Pipeline System
2025-06-26 08:37:30,474 - pipeline.core.state_manager - WARNING - _init_redis:77 - Redis not available, using file-based state management
2025-06-26 08:37:30,474 - pipeline.orchestrator.pipeline_orchestrator - INFO - initialize:119 - Initializing Pipeline Orchestrator
2025-06-26 08:37:30,475 - pipeline.orchestrator.pipeline_orchestrator - INFO - _start_scheduler:501 - Started workflow scheduler
2025-06-26 08:37:30,475 - pipeline.orchestrator.pipeline_orchestrator - INFO - initialize:127 - Pipeline Orchestrator initialized successfully
2025-06-26 08:37:30,475 - __main__ - INFO - initialize:138 - ✅ Pipeline system initialized successfully
2025-06-26 08:37:30,475 - __main__ - INFO - cleanup:400 - 🧹 Cleaning up resources...
2025-06-26 08:37:30,475 - pipeline.orchestrator.pipeline_orchestrator - INFO - shutdown:664 - Shutting down Pipeline Orchestrator
2025-06-26 08:37:35,478 - pipeline.orchestrator.pipeline_orchestrator - INFO - shutdown:687 - Pipeline Orchestrator shutdown complete
2025-06-26 08:37:35,478 - __main__ - INFO - cleanup:405 - ✅ Cleanup complete
