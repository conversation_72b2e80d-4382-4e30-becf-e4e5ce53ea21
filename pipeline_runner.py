#!/usr/bin/env python3
"""
Enhanced MCP Pipeline Runner
Main entry point for the automated web scraping and content generation pipeline
with Redis and CSV dual storage, state management, and resumability features.
"""

import asyncio
import argparse
import logging
import sys
import signal
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

# Add pipeline modules to path
sys.path.append(str(Path(__file__).parent / "pipeline"))

# Import pipeline components
from pipeline.core.pipeline_manager import PipelineManager, PipelineConfig
from pipeline.core.state_manager import StateManager
from pipeline.core.url_manager import URLManager, URLPriority
from pipeline.core.error_handler import <PERSON>rror<PERSON>andler
from pipeline.core.data_sync_manager import DataSyncManager
from pipeline.scrapers.resumable_scraper import ResumableScraper
from pipeline.generators.resumable_generator import ResumableContentGenerator
from pipeline.orchestrator.pipeline_orchestrator import (
    PipelineOrchestrator, WorkflowType, WorkflowDefinition
)


class EnhancedPipelineRunner:
    """Main pipeline runner with comprehensive management features"""
    
    def __init__(self, config_file: str = None):
        self.config = self._load_config(config_file)
        self.orchestrator = None
        self.error_handler = None
        self.data_sync_manager = None
        self.running = False

        # Setup logging
        self._setup_logging()
        self.logger = logging.getLogger(__name__)

        # Initialize data sync manager
        from dataclasses import asdict
        config_dict = asdict(self.config) if hasattr(self.config, '__dataclass_fields__') else {}
        self.data_sync_manager = DataSyncManager(config_dict)

        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _load_config(self, config_file: str = None) -> PipelineConfig:
        """Load configuration from file or use defaults"""
        if config_file and Path(config_file).exists():
            import json
            with open(config_file, 'r') as f:
                config_data = json.load(f)
            
            return PipelineConfig(
                redis_url=config_data.get('redis_url', 'redis://localhost:6379'),
                redis_db=config_data.get('redis_db', 0),
                csv_backup_enabled=config_data.get('csv_backup_enabled', True),
                max_retries=config_data.get('max_retries', 3),
                retry_delay=config_data.get('retry_delay', 5),
                batch_size=config_data.get('batch_size', 10),
                concurrent_workers=config_data.get('concurrent_workers', 3),
                checkpoint_interval=config_data.get('checkpoint_interval', 100),
                data_dir=config_data.get('data_dir', 'data'),
                logs_dir=config_data.get('logs_dir', 'logs'),
                enable_monitoring=config_data.get('enable_monitoring', True),
                auto_resume=config_data.get('auto_resume', True)
            )
        else:
            return PipelineConfig()

    def _setup_logging(self):
        """Setup comprehensive logging"""
        logs_dir = Path(self.config.logs_dir)
        logs_dir.mkdir(exist_ok=True)
        
        # Create formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        simple_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        
        # Setup file handlers
        main_log_file = logs_dir / f"pipeline_{datetime.now().strftime('%Y%m%d')}.log"
        error_log_file = logs_dir / f"errors_{datetime.now().strftime('%Y%m%d')}.log"
        
        file_handler = logging.FileHandler(main_log_file)
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(detailed_formatter)
        
        error_handler = logging.FileHandler(error_log_file)
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(detailed_formatter)
        
        # Setup console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(simple_formatter)
        
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)
        root_logger.addHandler(file_handler)
        root_logger.addHandler(error_handler)
        root_logger.addHandler(console_handler)

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        self.logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.running = False

    async def initialize(self) -> bool:
        """Initialize all pipeline components"""
        try:
            self.logger.info("🚀 Initializing Enhanced MCP Pipeline System")
            
            # Initialize error handler
            self.error_handler = ErrorHandler(self.config.data_dir)
            
            # Initialize orchestrator
            self.orchestrator = PipelineOrchestrator(self.config)
            success = await self.orchestrator.initialize()
            
            if not success:
                self.logger.error("Failed to initialize orchestrator")
                return False
            
            self.logger.info("✅ Pipeline system initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize pipeline system: {e}")
            return False

    async def run_scraping_pipeline(self, urls: List[str], pipeline_id: str = None,
                                  resume: bool = False, config: Dict[str, Any] = None) -> bool:
        """Run a scraping pipeline with duplicate detection"""
        try:
            if not pipeline_id:
                pipeline_id = f"scrape_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            self.logger.info(f"🕷️ Starting scraping pipeline: {pipeline_id}")

            # Check for duplicates and filter URLs
            force_update = config.get('force_update', False) if config else False
            filtered_urls = []
            skipped_count = 0

            for url in urls:
                is_scraped, existing_data = self.data_sync_manager.is_repo_scraped(url)

                if is_scraped and not force_update:
                    self.logger.info(f"⏭️ Skipping already scraped URL: {url}")
                    skipped_count += 1
                else:
                    filtered_urls.append(url)
                    if is_scraped and force_update:
                        self.logger.info(f"🔄 Force updating URL: {url}")

            self.logger.info(f"📊 URL Analysis: {len(urls)} total, {len(filtered_urls)} to process, {skipped_count} skipped")

            if not filtered_urls:
                self.logger.info("✅ All URLs already scraped, nothing to do")
                return True

            # Create workflow with filtered URLs
            workflow_config = {
                'urls': filtered_urls,
                'resume': resume,
                'scraper_config': config or {},
                'data_sync_manager': self.data_sync_manager
            }

            await self.orchestrator.create_workflow(
                pipeline_id, WorkflowType.SCRAPE_ONLY, workflow_config
            )

            # Run workflow
            success = await self.orchestrator.run_workflow(pipeline_id)

            if success:
                self.logger.info(f"✅ Scraping pipeline {pipeline_id} completed successfully")
                # Sync data after successful scraping
                self.data_sync_manager.sync_csv_from_redis()
            else:
                self.logger.error(f"❌ Scraping pipeline {pipeline_id} failed")

            return success

        except Exception as e:
            self.logger.error(f"❌ Scraping pipeline error: {e}")
            return False

    async def run_content_generation_pipeline(self, pipeline_id: str = None,
                                            resume: bool = False,
                                            config: Dict[str, Any] = None) -> bool:
        """Run a content generation pipeline with duplicate detection"""
        try:
            if not pipeline_id:
                pipeline_id = f"generate_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            self.logger.info(f"📝 Starting content generation pipeline: {pipeline_id}")

            # Get repositories that need content generation
            force_update = config.get('force_update', False) if config else False

            if force_update:
                # Get all scraped repos for force update
                repos_to_process = self.data_sync_manager.get_all_scraped_repos()
                self.logger.info(f"🔄 Force updating content for {len(repos_to_process)} repositories")
            else:
                # Get only repos without content
                repos_to_process = self.data_sync_manager.get_repos_without_content()
                self.logger.info(f"📊 Found {len(repos_to_process)} repositories without generated content")

            if not repos_to_process:
                self.logger.info("✅ All repositories already have generated content, nothing to do")
                return True

            # Create workflow
            workflow_config = {
                'resume': resume,
                'generator_config': config or {},
                'repos_to_process': repos_to_process,
                'data_sync_manager': self.data_sync_manager
            }

            await self.orchestrator.create_workflow(
                pipeline_id, WorkflowType.GENERATE_ONLY, workflow_config
            )

            # Run workflow
            success = await self.orchestrator.run_workflow(pipeline_id)

            if success:
                self.logger.info(f"✅ Content generation pipeline {pipeline_id} completed successfully")
                # Sync data after successful content generation
                self.data_sync_manager.sync_csv_from_redis()
            else:
                self.logger.error(f"❌ Content generation pipeline {pipeline_id} failed")

            return success

        except Exception as e:
            self.logger.error(f"❌ Content generation pipeline error: {e}")
            return False

    async def run_full_pipeline(self, urls: List[str] = None, pipeline_id: str = None,
                              resume: bool = False, config: Dict[str, Any] = None) -> bool:
        """Run full pipeline (scraping + content generation)"""
        try:
            if not pipeline_id:
                pipeline_id = f"full_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            self.logger.info(f"🔄 Starting full pipeline: {pipeline_id}")
            
            # Load URLs if not provided
            if not urls:
                urls = await self._load_urls_from_master_data()
            
            if not urls:
                self.logger.warning("No URLs available for processing")
                return True
            
            # Create workflow
            workflow_config = {
                'urls': urls,
                'resume': resume,
                'scraper_config': config.get('scraper', {}) if config else {},
                'generator_config': config.get('generator', {}) if config else {}
            }
            
            await self.orchestrator.create_workflow(
                pipeline_id, WorkflowType.SCRAPE_THEN_GENERATE, workflow_config
            )
            
            # Run workflow
            success = await self.orchestrator.run_workflow(pipeline_id)
            
            if success:
                self.logger.info(f"✅ Full pipeline {pipeline_id} completed successfully")
            else:
                self.logger.error(f"❌ Full pipeline {pipeline_id} failed")
            
            return success
            
        except Exception as e:
            self.logger.error(f"❌ Full pipeline error: {e}")
            return False

    async def setup_daily_automation(self, config: Dict[str, Any] = None) -> bool:
        """Setup daily automated pipeline"""
        try:
            self.logger.info("⏰ Setting up daily automation")
            
            workflow_config = {
                'auto_discover': True,
                'auto_cleanup': True,
                'cleanup_days': 30,
                'generate_reports': True,
                'schedule': {
                    'daily': True,
                    'time': '02:00'  # 2 AM daily
                },
                'scraper_config': config.get('scraper', {}) if config else {},
                'generator_config': config.get('generator', {}) if config else {}
            }
            
            await self.orchestrator.create_workflow(
                'daily_automation', WorkflowType.DAILY_FULL_PIPELINE, workflow_config
            )
            
            self.logger.info("✅ Daily automation setup complete")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to setup daily automation: {e}")
            return False

    async def _load_urls_from_master_data(self) -> List[str]:
        """Load URLs from master data file"""
        try:
            master_file = Path('data/master_data.csv')
            if master_file.exists():
                import pandas as pd
                df = pd.read_csv(master_file)
                urls = df['url'].tolist()
                self.logger.info(f"Loaded {len(urls)} URLs from master data")
                return urls
            else:
                self.logger.warning("Master data file not found")
                return []
        except Exception as e:
            self.logger.error(f"Failed to load URLs from master data: {e}")
            return []

    async def show_status(self) -> None:
        """Show current pipeline status including data sync status"""
        try:
            self.logger.info("📊 Pipeline Status Report")

            # Show data sync status
            sync_status = self.data_sync_manager.get_sync_status()
            self.logger.info("💾 Data Synchronization Status:")
            self.logger.info(f"  Redis Available: {sync_status.get('redis_available', False)}")
            self.logger.info(f"  CSV Scraped Count: {sync_status.get('csv_scraped_count', 0)}")
            self.logger.info(f"  CSV Content Count: {sync_status.get('csv_content_count', 0)}")

            if sync_status.get('redis_available'):
                self.logger.info(f"  Redis Scraped Count: {sync_status.get('redis_scraped_count', 0)}")
                self.logger.info(f"  Redis Content Count: {sync_status.get('redis_content_count', 0)}")
                self.logger.info(f"  Sync Needed: {sync_status.get('sync_needed', False)}")

            # Get repositories without content
            repos_without_content = self.data_sync_manager.get_repos_without_content()
            self.logger.info(f"📝 Repositories needing content generation: {len(repos_without_content)}")

            # Get all workflows
            workflows = await self.orchestrator.get_all_workflows()

            if not workflows:
                self.logger.info("📭 No active workflows")
                return

            self.logger.info(f"🔄 Active Workflows: {len(workflows)}")
            for workflow_id, status in workflows.items():
                if status:
                    self.logger.info(
                        f"  {workflow_id}: {status['status']} "
                        f"({status['run_count']} runs, "
                        f"{status['success_count']} successes, "
                        f"{status['failure_count']} failures)"
                    )

            # Get error statistics
            if self.error_handler:
                error_stats = await self.error_handler.get_error_statistics()
                self.logger.info(f"📈 Error Statistics (24h): {error_stats['total_errors']} total errors")

                if error_stats['by_category']:
                    for category, count in error_stats['by_category'].items():
                        self.logger.info(f"  {category}: {count}")

        except Exception as e:
            self.logger.error(f"Failed to show status: {e}")

    async def cleanup(self) -> None:
        """Cleanup resources"""
        try:
            self.logger.info("🧹 Cleaning up resources...")
            
            if self.orchestrator:
                await self.orchestrator.shutdown()
            
            self.logger.info("✅ Cleanup complete")
            
        except Exception as e:
            self.logger.error(f"❌ Cleanup error: {e}")


async def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Enhanced MCP Pipeline Runner")
    parser.add_argument('--config', help='Configuration file path')
    parser.add_argument('--mode', choices=['scrape', 'generate', 'full', 'daily', 'status', 'sync', 'cleanup'],
                       default='full', help='Pipeline mode')
    parser.add_argument('--urls', nargs='+', help='URLs to process (for scraping)')
    parser.add_argument('--pipeline-id', help='Custom pipeline ID')
    parser.add_argument('--resume', action='store_true', help='Resume from last checkpoint')
    parser.add_argument('--force-update', action='store_true', help='Force update even if data exists')
    parser.add_argument('--batch-size', type=int, default=10, help='Batch size for processing')
    parser.add_argument('--delay', type=float, default=2.0, help='Delay between requests')
    
    args = parser.parse_args()
    
    # Create runner
    runner = EnhancedPipelineRunner(args.config)
    
    try:
        # Initialize
        if not await runner.initialize():
            sys.exit(1)
        
        # Run based on mode
        if args.mode == 'scrape':
            urls = args.urls or []
            if not urls:
                print("No URLs provided for scraping mode")
                sys.exit(1)
            
            config = {
                'batch_size': args.batch_size,
                'delay': args.delay,
                'force_update': args.force_update
            }
            success = await runner.run_scraping_pipeline(
                urls, args.pipeline_id, args.resume, config
            )
            sys.exit(0 if success else 1)
        
        elif args.mode == 'generate':
            config = {
                'batch_size': args.batch_size,
                'force_update': args.force_update
            }
            success = await runner.run_content_generation_pipeline(
                args.pipeline_id, args.resume, config
            )
            sys.exit(0 if success else 1)
        
        elif args.mode == 'full':
            config = {
                'scraper': {'batch_size': args.batch_size, 'delay': args.delay},
                'generator': {'batch_size': args.batch_size}
            }
            success = await runner.run_full_pipeline(
                args.urls, args.pipeline_id, args.resume, config
            )
            sys.exit(0 if success else 1)
        
        elif args.mode == 'daily':
            config = {
                'scraper': {'batch_size': args.batch_size, 'delay': args.delay},
                'generator': {'batch_size': args.batch_size}
            }
            success = await runner.setup_daily_automation(config)
            sys.exit(0 if success else 1)
        
        elif args.mode == 'status':
            await runner.show_status()
            sys.exit(0)

        elif args.mode == 'sync':
            print("🔄 Synchronizing data between Redis and CSV...")

            # Sync Redis from CSV (recovery)
            print("📥 Syncing Redis from CSV files...")
            success = runner.data_sync_manager.sync_redis_from_csv()
            if success:
                print("✅ Redis sync from CSV completed")
            else:
                print("❌ Redis sync from CSV failed")

            # Sync CSV from Redis (backup)
            print("📤 Syncing CSV files from Redis...")
            success = runner.data_sync_manager.sync_csv_from_redis()
            if success:
                print("✅ CSV sync from Redis completed")
            else:
                print("❌ CSV sync from Redis failed")

            # Show sync status
            await runner.show_status()
            sys.exit(0)

        elif args.mode == 'cleanup':
            print("🧹 Cleaning up duplicate data...")

            cleanup_stats = runner.data_sync_manager.cleanup_duplicates()
            if 'error' in cleanup_stats:
                print(f"❌ Cleanup failed: {cleanup_stats['error']}")
                sys.exit(1)
            else:
                print(f"✅ Cleanup completed:")
                print(f"  CSV duplicates removed: {cleanup_stats['csv_duplicates']}")
                print(f"  JSON duplicates removed: {cleanup_stats['json_duplicates']}")

            # Show updated status
            await runner.show_status()
            sys.exit(0)
    
    except KeyboardInterrupt:
        print("\n🛑 Pipeline interrupted by user")
    except Exception as e:
        print(f"❌ Pipeline error: {e}")
        sys.exit(1)
    finally:
        await runner.cleanup()


if __name__ == '__main__':
    asyncio.run(main())
