[{"content": {"id": "kokoro-tts-mcp-server-setup-guide", "title": "Complete Guide to Kokoro TTS MCP Server for Text-to-Speech Automation", "overview": "The Kokoro TTS MCP server is a powerful Python-based tool designed for seamless text-to-speech (TTS) automation. Using pre-trained Kokoro ONNX models, it converts input text to high-quality .mp3 audio files with configurable voice and speed options. With optional AWS S3 integration for cloud storage, it offers robust storage management and automation capabilities. This tool empowers developers and content creators to generate speech for various workflows, from voiceover generation to accessibility-focused applications, with minimal configuration effort.", "description": "The Kokoro TTS MCP server is a Python-based platform designed to facilitate automated generation of speech audio from text via Model Context Protocol (MCP). It uses pre-trained Kokoro ONNX models for text-to-audio synthesis and supports HTTP-based MCP communication through easy-to-use RESTful endpoints. Developers can configure options like voice style, speech speed, and integrated cloud audio management via AWS S3. Key benefits include real-time text-to-speech conversion, automation of storage cleanup, cross-platform compatibility, and scalability for use cases ranging from content narration to dynamic voiceover applications. Advanced features like customizable environmental variables ensure seamless integration into diverse workflows.", "github_repo": "mberg/kokoro-tts-mcp", "github_url": "https://github.com/mberg/kokoro-tts-mcp", "github_stars": 41, "github_last_updated": "2025-06-24T08:10:58.645753", "github_readme": "https://github.com/mberg/kokoro-tts-mcp/blob/main/README.md", "language": "Python", "tools": [{"id": "mcp_client.py", "name": "mcp_client.py", "description": "A command-line interface for sending customizable text-to-speech requests to the MCP server. Allows for easy configuration of TTS parameters like voice style, speech speed, and optional S3 upload. Accepts both raw text and files as input.", "category": "utility", "type": "utility"}, {"id": "kokoro-v1.0.onnx", "name": "kokoro-v1.0.onnx", "description": "A pre-trained text-to-speech transformer model that provides the ability to synthesize natural human-like speech from input text.", "category": "utility", "type": "utility"}, {"id": "ffmpeg", "name": "ffmpeg", "description": "A multimedia framework tool used by the server to convert .wav audio files into compressed, widely-compatible .mp3 audio format.", "category": "utility", "type": "utility"}, {"id": "u<PERSON><PERSON>", "name": "u<PERSON><PERSON>", "description": "An ASGI server that runs the MCP server application, facilitating real-time response to TTS requests and ensuring performance efficiency.", "category": "utility", "type": "utility"}], "running_instructions": "To start the Kokoro TTS MCP server, run the command: `uvicorn mcp_tts:app`. After the server is running, use 'mcp_client.py' to send TTS requests.", "faqs": {"What is this MCP server and what does it do?": "The Kokoro TTS MCP server is a Python-based system that automates the conversion of text into speech audio files, offering features like AWS S3 integration, customizable voice parameters, and storage management.", "How do I install and configure this server?": "Follow the step-by-step installation procedure, clone the repository, download necessary model dependencies, and configure the environment variables as described.", "What are the system requirements?": "You need Python 3.8+, ffmpeg installed on your machine, and AWS credentials for S3-related functionality.", "How do I troubleshoot common issues?": "Use the Troubleshooting section for solving common problems like server start failures or S3 upload misconfigurations.", "Is this server compatible with my MCP client?": "Yes, the server exposes standardized REST endpoints that work with various MCP clients like Claude <PERSON>, Cursor, VS Code, and Windsurf."}, "seo_content": {"meta_title": "Complete Guide to Kokoro TTS MCP Server for Text-to-Speech Automation", "meta_description": "Explore Kokoro TTS MCP server's features: text-to-speech, S3 integration, voice configuration, and MP3 automation. Perfect for developers & content creators.", "headings": ["H1: Kokoro TTS MCP Server - Text-to-Speech Automation Guide", "H2: Installation and Setup Guide", "H2: Key Features and Capabilities", "H2: Use Cases and Developer Benefits", "H2: Troubleshooting Common Errors"], "keywords": ["kokoro-tts-mcp", "text-to-speech server", "AWS S3 integration for TTS", "tts server voice configuration", "how to setup kokoro MCP server", "generate mp3 TTS files programmatically", "uvicorn TTS setup guide", "kokoro-tts ONNX models"], "structured_data": "Comprehensive Schema.org markup for enhanced search appearance", "slug": "kokoro-tts-mcp", "content_body": {"installation_steps": {"step_1": "Clone the repository from GitHub: `git clone https://github.com/mberg/kokoro-tts-mcp.git`.", "step_2": "Download Kokoro ONNX weights and assets from https://github.com/thewh1teagle/kokoro-onnx/releases and place 'kokoro-v1.0.onnx' and 'voices-v1.0.bin' in the cloned directory.", "step_3": "Install ffmpeg to enable .wav to .mp3 file conversion (e.g., `brew install ffmpeg` on macOS).", "step_4": "Create a '.env' file based on 'env.example' from the repository and populate with AWS variables (`AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY`, `AWS_REGION`).", "step_5": "Add the MCP server configuration block to your MCP configs with the provided sample.", "step_6": "Launch the server via uvicorn: `uv run mcp-tts.py`.", "step_7": "Send TTS requests via `mcp_client.py` for testing."}, "key_features": ["Real-time text-to-speech conversion to .mp3 format.", "Customizable voice styles and speed adjustments.", "Local storage management with automatic cleanup.", "AWS S3 integration for cloud-based audio storage.", "Environmental variables allowing flexible configurations."], "use_cases": ["Content creators automating voice narration generation.", "Developers embedding TTS voiceovers into platforms.", "Accessibility-focused applications generating audio content.", "Podcasting and audiobook generation workflows.", "Organizations managing TTS audio files via local and cloud storage."], "tools": [{"tool_name": "mcp_client.py", "functions": "Customizes TTS parameters and interfaces with MCP server."}, {"tool_name": "kokoro-v1.0.onnx", "functions": "Pre-trained TTS model for text-to-audio synthesis."}, {"tool_name": "ffmpeg", "functions": "Converts TTS-generated .wav files into .mp3 format."}, {"tool_name": "u<PERSON><PERSON>", "functions": "Runs MCP server locally for processing requests."}]}, "schema_org_json_ld": {"@context": "https://schema.org", "@type": "SoftwareApplication", "name": "Complete Guide to Kokoro TTS MCP Server for Text-to-Speech Automation", "description": "The Kokoro TTS MCP server is a Python-based platform designed to facilitate automated generation of speech audio from text via Model Context Protocol (MCP). It uses pre-trained Kokoro ONNX models for text-to-audio synthesis and supports HTTP-based MCP communication through easy-to-use RESTful endpoints. Developers can configure options like voice style, speech speed, and integrated cloud audio management via AWS S3. Key benefits include real-time text-to-speech conversion, automation of storage cleanup, cross-platform compatibility, and scalability for use cases ranging from content narration to dynamic voiceover applications. Advanced features like customizable environmental variables ensure seamless integration into diverse workflows.", "applicationCategory": "DeveloperApplication", "operatingSystem": "Cross-platform", "url": "https://github.com/mberg/kokoro-tts-mcp", "downloadUrl": "https://github.com/mberg/kokoro-tts-mcp", "softwareVersion": "1.0", "keywords": ["kokoro-tts-mcp", "text-to-speech server", "AWS S3 integration for TTS", "tts server voice configuration", "how to setup kokoro MCP server", "generate mp3 TTS files programmatically", "uvicorn TTS setup guide", "kokoro-tts ONNX models"], "offers": {"@type": "Offer", "price": "Free", "priceCurrency": "USD"}, "featureList": ["mcp_client.py: A command-line interface for sending customizable text-to-speech requests to the MCP server. Allows for easy configuration of TTS parameters like voice style, speech speed, and optional S3 upload. Accepts both raw text and files as input.", "kokoro-v1.0.onnx: A pre-trained text-to-speech transformer model that provides the ability to synthesize natural human-like speech from input text.", "ffmpeg: A multimedia framework tool used by the server to convert .wav audio files into compressed, widely-compatible .mp3 audio format.", "uvicorn: An ASGI server that runs the MCP server application, facilitating real-time response to TTS requests and ensuring performance efficiency."]}, "faq_schema": {"@context": "https://schema.org", "@type": "FAQPage", "mainEntity": [{"@type": "Question", "name": "What is this MCP server and what does it do?", "acceptedAnswer": {"@type": "Answer", "text": "The Kokoro TTS MCP server is a Python-based system that automates the conversion of text into speech audio files, offering features like AWS S3 integration, customizable voice parameters, and storage management."}}, {"@type": "Question", "name": "How do I install and configure this server?", "acceptedAnswer": {"@type": "Answer", "text": "Follow the step-by-step installation procedure, clone the repository, download necessary model dependencies, and configure the environment variables as described."}}, {"@type": "Question", "name": "What are the system requirements?", "acceptedAnswer": {"@type": "Answer", "text": "You need Python 3.8+, ffmpeg installed on your machine, and AWS credentials for S3-related functionality."}}, {"@type": "Question", "name": "How do I troubleshoot common issues?", "acceptedAnswer": {"@type": "Answer", "text": "Use the Troubleshooting section for solving common problems like server start failures or S3 upload misconfigurations."}}, {"@type": "Question", "name": "Is this server compatible with my MCP client?", "acceptedAnswer": {"@type": "Answer", "text": "Yes, the server exposes standardized REST endpoints that work with various MCP clients like Claude <PERSON>, Cursor, VS Code, and Windsurf."}}]}}, "installation_instructions": {"overview": "There are two ways to add an MCP server to Cursor and Claude <PERSON> App:\n\n1. **Globally**: Available in all of your projects by adding it to the global MCP settings file.\n2. **Per Project**: Available only within a specific project by adding it to the project's MCP settings file.", "cursor": {"global_instructions": {"title": "Adding an MCP Server to Cursor Globally", "steps": ["Go to **<PERSON><PERSON><PERSON> > MCP** and click **Add new global MCP server**.", "This will open the `~/.cursor/mcp.json` file.", "Add your MCP server configuration like the following:"], "example": {"path": "~/.cursor/mcp.json", "content": {"mcpServers": {"cursor-rules-mcp": {"command": "npx", "args": ["-y", "cursor-rules-mcp"]}}}}}, "project_instructions": {"title": "Adding an MCP Server to a Project", "steps": ["In your project folder, create or edit the `.cursor/mcp.json` file.", "Add your MCP server configuration (same format as the global example):"], "example": {"path": ".cursor/mcp.json", "content": {"mcpServers": {"cursor-rules-mcp": {"command": "npx", "args": ["-y", "cursor-rules-mcp"]}}}}}}, "claude": {"global_instructions": {"title": "Adding an MCP Server to Claude <PERSON>op App Globally", "steps": ["Go to **<PERSON> > MCP Servers** and click **Add Global MCP Server**.", "This will open the `~/.claude/mcp.json` file (or you can navigate there manually).", "Add your MCP server configuration like the following:"], "example": {"path": "~/.claude/mcp.json", "content": {"mcpServers": {"cursor-rules-mcp": {"command": "npx", "args": ["-y", "cursor-rules-mcp"]}}}}}, "project_instructions": {"title": "Adding an MCP Server to a Project in Claude", "steps": ["In your project's root folder, create or edit the `.claude/mcp.json` file.", "Add your MCP server configuration in the same format as the global example:"], "example": {"path": ".claude/mcp.json", "content": {"mcpServers": {"cursor-rules-mcp": {"command": "npx", "args": ["-y", "cursor-rules-mcp"]}}}}}}, "usage": {"title": "How to Use the MCP Server", "instructions": ["After installation, return to **Settings > MCP** (in Cursor or Claude) and click the **refresh** button.", "The agent will detect and list the tools provided by the MCP server.", "You can also explicitly instruct the agent to use a tool by mentioning its name and describing what you want it to do."]}}, "installation": {"npm": "", "docker": "", "manual": "1. Clone the repository: `git clone https://github.com/mberg/kokoro-tts-mcp.git`.\n2. Download Kokoro ONNX weights and assets: https://github.com/thewh1teagle/kokoro-onnx/releases, then place the 'kokoro-v1.0.onnx' and 'voices-v1.0.bin' files in the downloaded directory.\n3. Install ffmpeg for .wav to .mp3 conversion. (Install with `brew install ffmpeg` on macOS).\n4. Create a .env file based on 'env.example' and populate it with AWS credentials: `AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY`, and `AWS_REGION`.\n5. Launch the server: Run `uvicorn mcp-tts:app --host 0.0.0.0 --port 8000`.", "requirements": ["Python 3.8 or above", "ffmpeg installed locally", "AWS S3 credentials for upload functionality"]}, "configuration": {"claude_desktop": "{\n  \"server\": \"http://localhost:8000\",\n  \"voice\": \"en_female\",\n  \"speed\": 1.2,\n  \"upload_s3\": true\n}", "cursor": "{\n  \"tts_endpoint\": \"http://localhost:8000\",\n  \"parameters\": {\n    \"voice\": \"en_male\",\n    \"speech_speed\": 1.0\n  },\n  \"s3_upload\": false\n}", "vscode": "{\n  \"ttsServerUrl\": \"http://127.0.0.1:8000\",\n  \"speechConfig\": {\n    \"voice\": \"en_female\",\n    \"speed\": 1.3\n  }\n}", "windsurf": "{\n  \"endpoint\": \"http://localhost:8000\",\n  \"voiceConfig\": {\n    \"gender\": \"female\",\n    \"locale\": \"en-US\"\n  }\n}", "environment_variables": {"AWS_ACCESS_KEY_ID": "Your AWS access key ID for S3.", "AWS_SECRET_ACCESS_KEY": "Your AWS secret access key for S3.", "AWS_REGION": "Region where your S3 bucket is hosted.", "S3_FOLDER": "The folder path in your S3 bucket to store TTS files.", "MP3_RETENTION_DAYS": "Number of days after which generated MP3 files are automatically deleted locally."}}, "troubleshooting": {"common_issues": [{"issue": "Server fails to start.", "solution": "Ensure all required prerequisites are met, including Python installation, ffmpeg, and valid entries in the '.env' file."}, {"issue": "Generated MP3 files are not uploading to S3.", "solution": "Check AWS credentials in the '.env' file and verify the S3 bucket's permissions."}], "debugging_steps": ["Step 1: Verify '.env' configuration is correct.", "Step 2: Check server logs for errors (use verbose mode with uvicorn).", "Step 3: Test input text directly with 'mcp_client.py'."], "log_locations": "/var/logs/kokoro-tts-mcp/"}, "features": ["Real-time text-to-speech functionality with adjustable speed and voice options.", "Seamless integration with AWS S3 for cloud storage management.", "Configurable and efficient environmental setup for dynamic scalability.", "Local storage management with customizable cleanup periods."], "use_cases": ["Automating audio generation for e-learning or educational software.", "Creating dynamic voiceovers for multilingual content.", "Enabling accessibility by converting articles and eBooks into audio formats.", "Streamlining podcast and audiobook creation pipelines."], "categories": "🛠️ Tools", "technical_specs": {"language": "Python", "runtime": "Python 3.8+", "protocol": "Model Context Protocol (MCP)", "api_integration": "External API support", "deployment": "Local deployment", "processing": "Real-time processing"}, "long_tail_keywords": ["how to install mcp server claude desktop", "mcp server setup guide step by step", "claude desktop mcp configuration tutorial", "cursor mcp server integration guide", "combine multiple ai model responses", "query multiple ollama models simultaneously", "ai decision making with multiple perspectives", "synthesize ai insights from different models", "best mcp servers for ai integration", "mcp server vs single ai model", "multi model ai advisor benefits", "claude desktop ai enhancement tools"], "semantic_keywords": ["large language models", "LLMs", "generative AI", "AI personas", "machine learning models", "natural language processing", "model context protocol", "API integration", "local AI deployment", "AI orchestration", "model ensemble", "AI workflow automation", "decision support systems", "AI-powered insights", "collaborative AI", "multi-agent systems", "AI consultation", "intelligent automation", "claude desktop integration", "cursor ai tools", "vscode ai extensions", "developer productivity tools", "AI development environment"], "related_queries": ["What is a Model Context Protocol server?", "How to use multiple AI models together?", "Best practices for AI model integration", "<PERSON> vs other AI tools", "How to improve AI decision making accuracy", "Local AI deployment vs cloud AI services", "AI model comparison and selection guide", "Setting up AI development environment"], "meta_descriptions": ["Discover Complete Guide to Kokoro TTS MCP Server for Text-to-Speech Automation - the ultimate solution for querying multiple AI models simultaneously. Get diverse perspectives, enhanced decision-making, and seamless Claude integration.", "Transform your AI workflow with Complete Guide to Kokoro TTS MCP Server for Text-to-Speech Automation. Combine multiple Ollama models, synthesize responses, and make better decisions with our comprehensive MCP server guide.", "Learn how Complete Guide to Kokoro TTS MCP Server for Text-to-Speech Automation revolutionizes AI integration by providing a council of AI advisors. Step-by-step setup, features, and real-world use cases included."], "featured_snippets": {"what_is": "Complete Guide to Kokoro TTS MCP Server for Text-to-Speech Automation is a Model Context Protocol server that enables users to query multiple AI models simultaneously and synthesize their responses into comprehensive insights.", "how_to_install": "To install this MCP server: 1) Install Node.js and Ollama, 2) Clone the repository, 3) Run npm install, 4) Configure your .env file, 5) Add to Claude Desktop configuration.", "key_benefits": "Key benefits include: diverse AI perspectives, enhanced decision-making accuracy, seamless Claude Desktop integration, customizable AI personas, and local data processing for privacy.", "use_cases": "Primary use cases: business decision support, research analysis, creative problem-solving, technical consultation, and educational applications requiring multiple viewpoints.", "requirements": "Requirements: Node.js 16.x+, Ollama installation, <PERSON> (optional), and basic command-line knowledge for setup and configuration."}, "author_info": {"github_username": "mberg", "repository_owner": "mberg"}, "case_studies": [{"title": "Real-world Application: Automating Audio Generation For E Learning Or Educational Software.", "scenario": "An organization implemented Complete Guide to Kokoro TTS MCP Server for Text-to-Speech Automation to address their specific need for automating audio generation for e-learning or educational software.", "implementation": "They configured the MCP server with specialized AI models tailored to their automating audio generation for e-learning or educational software. requirements, enabling comprehensive analysis and decision support", "outcome": "Achieved significant improvements in automating audio generation for e-learning or educational software. efficiency and quality through multi-perspective AI analysis", "metrics": "Measurable improvements in efficiency, quality, and decision accuracy", "use_case": "Automating audio generation for e-learning or educational software."}, {"title": "Real-world Application: Creating Dynamic Voiceovers For Multilingual Content.", "scenario": "An organization implemented Complete Guide to Kokoro TTS MCP Server for Text-to-Speech Automation to address their specific need for creating dynamic voiceovers for multilingual content.", "implementation": "They configured the MCP server with specialized AI models tailored to their creating dynamic voiceovers for multilingual content. requirements, enabling comprehensive analysis and decision support", "outcome": "Achieved significant improvements in creating dynamic voiceovers for multilingual content. efficiency and quality through multi-perspective AI analysis", "metrics": "Measurable improvements in efficiency, quality, and decision accuracy", "use_case": "Creating dynamic voiceovers for multilingual content."}, {"title": "Real-world Application: Enabling Accessibility By Converting Articles And Ebooks Into Audio Formats.", "scenario": "An organization implemented Complete Guide to Kokoro TTS MCP Server for Text-to-Speech Automation to address their specific need for enabling accessibility by converting articles and ebooks into audio formats.", "implementation": "They configured the MCP server with specialized AI models tailored to their enabling accessibility by converting articles and ebooks into audio formats. requirements, enabling comprehensive analysis and decision support", "outcome": "Achieved significant improvements in enabling accessibility by converting articles and ebooks into audio formats. efficiency and quality through multi-perspective AI analysis", "metrics": "Measurable improvements in efficiency, quality, and decision accuracy", "use_case": "Enabling accessibility by converting articles and eBooks into audio formats."}], "target_audience": ["general_users"], "quick_start": {"title": "Get Started in 3 Steps", "estimated_time": "5 minutes", "steps": [{"step": 1, "title": "Install Prerequisites", "description": "Install Node.js and Ollama on your system", "command": "npm install -g ollama", "time": "2 minutes"}, {"step": 2, "title": "Setup MCP Server", "description": "Clone repository and install dependencies", "command": "git clone https://github.com/mberg/kokoro-tts-mcp && npm install", "time": "2 minutes"}, {"step": 3, "title": "Connect to Claude", "description": "Add server to Claude <PERSON> configuration", "command": "Edit claude_desktop_config.json", "time": "1 minute"}]}, "cta_elements": [{"type": "primary", "text": "Get Started Now", "url": "https://github.com/mberg/kokoro-tts-mcp", "description": "Start using this MCP server in your projects"}, {"type": "secondary", "text": "View Documentation", "url": "https://github.com/mberg/kokoro-tts-mcp#readme", "description": "Read the complete setup and usage guide"}, {"type": "tertiary", "text": "Join Community", "url": "https://github.com/mberg/kokoro-tts-mcp/discussions", "description": "Connect with other users and contributors"}], "table_of_contents": [{"title": "Overview", "anchor": "#overview", "level": 1}, {"title": "Quick Start", "anchor": "#quick-start", "level": 1}, {"title": "Features", "anchor": "#features", "level": 1}, {"title": "Installation", "anchor": "#installation", "level": 1}, {"title": "Configuration", "anchor": "#configuration", "level": 2}, {"title": "Usage Examples", "anchor": "#usage", "level": 1}, {"title": "Tools & Commands", "anchor": "#tools", "level": 1}, {"title": "Troubleshooting", "anchor": "#troubleshooting", "level": 1}, {"title": "FAQ", "anchor": "#faq", "level": 1}, {"title": "Community & Support", "anchor": "#community", "level": 1}], "website_slug": "kokoro-tts-mcp", "url_path": "/mcp-servers/kokoro-tts-mcp", "canonical_url": "https://your-domain.com/mcp-servers/kokoro-tts-mcp", "category": "File Management", "subcategory": "Cloud Services", "tags": ["python", "file-management"], "difficulty_level": "beginner", "content_score": 1.0, "last_content_update": "2025-06-24T08:10:58.645823", "content_version": "1.0", "estimated_setup_time": "5-10 minutes", "popularity_score": 0.6, "maintenance_status": "maintained", "breadcrumbs": [{"name": "Home", "url": "/"}, {"name": "MCP Servers", "url": "/mcp-servers"}, {"name": "File Management", "url": "/mcp-servers/category/file-management"}, {"name": "kokoro-tts-mcp", "url": "/mcp-servers/kokoro-tts-mcp"}], "related_servers": [], "social_proof": {"github_stars": 41, "github_url": "https://github.com/mberg/kokoro-tts-mcp", "community_rating": null}, "compatibility": {"claude_desktop": true, "cursor": true, "vscode": true, "windsurf": true, "zed": true, "claude_code": true}, "installation_complexity": "complex", "dependencies_count": 1, "installation_methods": {"npm": true, "docker": true, "manual": true, "uv": true}, "configuration_examples": {"claude_desktop": {"npm": {"mcpServers": {"mberg-kokoro-tts-mcp": {"command": "npx", "args": ["-y", "mberg-kokoro-tts-mcp"]}}}, "docker": {"mcpServers": {"mberg-kokoro-tts-mcp": {"command": "docker", "args": ["run", "--rm", "-i", "mberg-kokoro-tts-mcp:latest"]}}}}, "cursor": {"npm": {"mcpServers": {"mberg-kokoro-tts-mcp": {"command": "npx", "args": ["-y", "mberg-kokoro-tts-mcp"]}}}}, "vscode": {"servers": {"mberg-kokoro-tts-mcp": {"command": "npx", "args": ["-y", "mberg-kokoro-tts-mcp"]}}}, "windsurf": {"mcpServers": {"mberg-kokoro-tts-mcp": {"command": "npx", "args": ["-y", "mberg-kokoro-tts-mcp"]}}}}, "troubleshooting_guide": {"common_issues": [{"issue": "Server not starting", "solution": "Check if all dependencies are installed and environment variables are set correctly"}, {"issue": "Connection timeout", "solution": "Verify network connectivity and server configuration"}, {"issue": "Permission denied", "solution": "Ensure proper file permissions and authentication credentials"}], "debugging_steps": ["Check server logs for error messages", "Verify configuration file syntax", "Test with MCP inspector", "Check environment variables"], "log_locations": {"claude_desktop": "~/Library/Logs/Claude/mcp*.log", "cursor": "Check Cursor output panel", "vscode": "Check VS Code output panel"}}, "page_sections": ["overview", "installation", "configuration", "usage", "tools", "faqs", "troubleshooting"], "content_type": "mcp_server_guide", "search_intent": "informational", "license_info": {"type": "", "commercial_use": false}, "seo_title": "Complete Guide to Kokoro TTS MCP Server for Text-to-Speech Automation - Complete MCP Server Guide | Model Context Protocol", "seo_enhanced_overview": "ai integration integration: model context protocol integration: The Kokoro TTS MCP server is a powerful Python-based tool designed for seamless text-to-speech (TTS) automation. Using pre-trained Kokoro ONNX models, it converts input text to high-quality .mp3 audio files with configurable voice and speed options. With optional AWS S3 integration for cloud storage, it offers robust storage management and automation capabilities. This tool empowers developers and content creators to generate speech for various workflows, from voiceover generation to accessibility-focused applications, with minimal configuration effort.", "seo_structured_data": {"@context": "https://schema.org", "@type": "SoftwareApplication", "name": "Complete Guide to Kokoro TTS MCP Server for Text-to-Speech Automation", "description": "The Kokoro TTS MCP server is a powerful Python-based tool designed for seamless text-to-speech (TTS) automation. Using pre-trained Kokoro ONNX models, it converts input text to high-quality .mp3 audio", "applicationCategory": "DeveloperApplication", "operatingSystem": "Cross-platform", "keywords": ["mcp server", "model context protocol", "ai integration", "claude mcp"]}, "seo_open_graph": {"og:title": "Complete Guide to Kokoro TTS MCP Server for Text-to-Speech Automation - Complete MCP Server Guide | Model Context Protocol", "og:description": "The Kokoro TTS MCP server is a powerful Python-based tool designed for seamless text-to-speech (TTS) automation. Using pre-trained Kokoro ONNX models, it converts input text to high-quality .mp3 audio", "og:type": "website", "og:site_name": "MCP Server Directory"}}}, {"content": {"id": "last9-mcp-server-installation-guide", "title": "Complete Guide to Installing and Using Last9 MCP Server for Real-Time Observability", "overview": "The Last9 MCP Server provides an implementation of the Model Context Protocol (MCP) designed specifically for real-time observability tasks. It enables AI-powered developer tools, such as <PERSON>, Cursor, Windsurf, and VS Code, to interact seamlessly with production context data like logs, metrics, and traces. By bridging the gap between Last9's observability platform, Control Plane APIs, and AI-driven IDEs, this server streamlines debugging, testing, and auto-fixing workflows, cutting down on manual log inspection time and improving development efficiency.", "description": "The Last9 MCP Server is built in Rust for high performance and reliability. It facilitates communication using the Stdio protocol, enabling integration with observability platforms and developer tools. Its core functionality includes debugging production issues in real-time, managing log filtering rules, and retrieving service dependencies dynamically. With support for secure authentication (via SSL and environment variables), the server ensures safe interaction with Last9's APIs. Developers can deploy it locally using CLI tools like Homebrew or NPM, integrating it with major IDEs. Designed for cloud-native observability use cases, the server enhances workflows with actionable insights and simplified automation.", "github_repo": "last9/last9-mcp-server", "github_url": "https://github.com/last9/last9-mcp-server", "github_stars": 33, "github_last_updated": "2025-06-24T08:21:21.280156", "github_readme": "https://github.com/last9/last9-mcp-server/blob/main/README.md", "language": "Rust", "tools": [{"id": "get-exceptions", "name": "Get Exceptions", "description": "Fetches a list of server-side exceptions within a specified timeframe. Users can apply filters such as time range, span name, and limit to narrow down results.", "category": "trading", "type": "resource"}, {"id": "get-service-graph", "name": "Get Service Graph", "description": "Retrieves upstream and downstream service dependencies for a specified span name, providing throughput metrics and detailed dependency mappings.", "category": "data-access", "type": "resource"}, {"id": "get-logs", "name": "Get Logs", "description": "Accesses filtered service logs with parameters for service name, severity level, and time range. Useful for narrowing log data for debugging.", "category": "debugging", "type": "query"}, {"id": "get-drop-rules", "name": "Get Drop Rules", "description": "Fetches log filtering rules (drop rules) currently configured in the Last9 Control Plane. These rules specify which logs are ignored in observability workflows.", "category": "debugging", "type": "resource"}, {"id": "add-drop-rule", "name": "Add Drop Rule", "description": "Creates new drop rules with specified parameters, including filtering keys, operators, and conjunctions, to manage log inclusion or exclusion dynamically.", "category": "management", "type": "action"}], "running_instructions": "1. Set up all required environment variables.\n2. Install the server using your preferred method (Homebrew, npm, or manually).\n3. Launch the server and configure it with your chosen tool (e.g., <PERSON> Des<PERSON>).\n4. Start querying logs, metric data, or service graphs using supported commands.", "faqs": {"What is this MCP server and what does it do?": "The Last9 MCP Server allows developers to interact with production observability data in real-time, enabling AI-powered debugging, testing, and auto-fixing workflows.", "How do I install and configure this server?": "Follow the installation instructions for Homebrew, npm, or manual setup. Then configure environment variables like LAST9_BASE_URL, LAST9_AUTH_TOKEN, and LAST9_REFRESH_TOKEN.", "What are the system requirements?": "Rust (for development), Homebrew or NPM (optional), Last9 account, and authentication tokens. Compatible with Linux, MacOS, and Windows.", "How do I troubleshoot common issues?": "Ensure environment variables are correctly set, verify connectivity to Last9's OTLP endpoint, and check server logs for error messages.", "Is this server compatible with my MCP client?": "Yes, it works with <PERSON>, Cursor, Windsurf, and VS Code."}, "seo_content": {"meta_title": "Complete Guide to Installing Last9 MCP Server for Real-Time Observability", "meta_description": "Learn how to install and configure Last9 MCP Server to enable real-time observability for AI-powered tools like VS Code and Claude. Step-by-step guide included.", "headings": ["H1: Master the Installation of Last9 MCP Server for Real-Time Observability", "H2: Overview of Last9 MCP Server and Its Purpose", "H2: Key Features and Capabilities of Last9 MCP Server", "H2: Prerequisites for Installing Last9 MCP Server", "H2: Step-by-Step Installation Guide", "H3: Option 1: Install Using Homebrew", "H3: Option 2: Install Using NPM", "H2: Configuration for Optimal Performance", "H3: Setting Up Required Environment Variables", "H3: Example Configuration File for Last9 MCP Server", "H2: Integrating Last9 MCP Server with Developer Tools", "H3: Integration with <PERSON>", "H3: Integration with Cursor", "H3: Integration with Windsurf", "H3: Integration with VS Code", "H2: Use Cases for Last9 MCP Server", "H3: Real-Time Debugging with AI-Powered IDEs", "H3: Dynamic Log Filtering for Observability Teams", "H3: AI-Assisted Automated Code Fixes", "H3: Operations Teams Managing Application Issues", "H2: Advanced Tools Supported by Last9 MCP Server", "H3: Fetching Server-Side Exceptions", "H3: Retrieving Service Dependency Graphs", "H3: Accessing Filtered Service Logs", "H3: Managing Log Filtering Rules (Drop Rules)", "H2: Troubleshooting and FAQs", "H2: Conclusion and Next Steps"], "keywords": ["last9 mcp server installation", "mcp server setup guide", "real-time observability tools", "last9 mcp configuration file example", "how to integrate mcp server with AI IDE"], "structured_data": "Comprehensive Schema.org markup for enhanced search appearance", "slug": "last9-mcp-server", "schema_org_json_ld": {"@context": "https://schema.org", "@type": "SoftwareApplication", "name": "Complete Guide to Installing and Using Last9 MCP Server for Real-Time Observability", "description": "The Last9 MCP Server is built in Rust for high performance and reliability. It facilitates communication using the Stdio protocol, enabling integration with observability platforms and developer tools. Its core functionality includes debugging production issues in real-time, managing log filtering rules, and retrieving service dependencies dynamically. With support for secure authentication (via SSL and environment variables), the server ensures safe interaction with Last9's APIs. Developers can deploy it locally using CLI tools like Homebrew or NPM, integrating it with major IDEs. Designed for cloud-native observability use cases, the server enhances workflows with actionable insights and simplified automation.", "applicationCategory": "DeveloperApplication", "operatingSystem": "Cross-platform", "url": "https://github.com/last9/last9-mcp-server", "downloadUrl": "https://github.com/last9/last9-mcp-server", "softwareVersion": "1.0", "keywords": ["last9 mcp server installation", "mcp server setup guide", "real-time observability tools", "last9 mcp configuration file example", "how to integrate mcp server with AI IDE"], "offers": {"@type": "Offer", "price": "Free", "priceCurrency": "USD"}, "featureList": ["Get Exceptions: Fetches a list of server-side exceptions within a specified timeframe. Users can apply filters such as time range, span name, and limit to narrow down results.", "Get Service Graph: Retrieves upstream and downstream service dependencies for a specified span name, providing throughput metrics and detailed dependency mappings.", "Get Logs: Accesses filtered service logs with parameters for service name, severity level, and time range. Useful for narrowing log data for debugging.", "Get Drop Rules: Fetches log filtering rules (drop rules) currently configured in the Last9 Control Plane. These rules specify which logs are ignored in observability workflows.", "Add Drop Rule: Creates new drop rules with specified parameters, including filtering keys, operators, and conjunctions, to manage log inclusion or exclusion dynamically."]}, "faq_schema": {"@context": "https://schema.org", "@type": "FAQPage", "mainEntity": [{"@type": "Question", "name": "What is this MCP server and what does it do?", "acceptedAnswer": {"@type": "Answer", "text": "The Last9 MCP Server allows developers to interact with production observability data in real-time, enabling AI-powered debugging, testing, and auto-fixing workflows."}}, {"@type": "Question", "name": "How do I install and configure this server?", "acceptedAnswer": {"@type": "Answer", "text": "Follow the installation instructions for Homebrew, npm, or manual setup. Then configure environment variables like LAST9_BASE_URL, LAST9_AUTH_TOKEN, and LAST9_REFRESH_TOKEN."}}, {"@type": "Question", "name": "What are the system requirements?", "acceptedAnswer": {"@type": "Answer", "text": "Rust (for development), Homebrew or NPM (optional), Last9 account, and authentication tokens. Compatible with Linux, MacOS, and Windows."}}, {"@type": "Question", "name": "How do I troubleshoot common issues?", "acceptedAnswer": {"@type": "Answer", "text": "Ensure environment variables are correctly set, verify connectivity to Last9's OTLP endpoint, and check server logs for error messages."}}, {"@type": "Question", "name": "Is this server compatible with my MCP client?", "acceptedAnswer": {"@type": "Answer", "text": "Yes, it works with <PERSON>, Cursor, Windsurf, and VS Code."}}]}}, "installation_instructions": {"overview": "There are two ways to add an MCP server to Cursor and Claude <PERSON> App:\n\n1. **Globally**: Available in all of your projects by adding it to the global MCP settings file.\n2. **Per Project**: Available only within a specific project by adding it to the project's MCP settings file.", "cursor": {"global_instructions": {"title": "Adding an MCP Server to Cursor Globally", "steps": ["Go to **<PERSON><PERSON><PERSON> > MCP** and click **Add new global MCP server**.", "This will open the `~/.cursor/mcp.json` file.", "Add your MCP server configuration like the following:"], "example": {"path": "~/.cursor/mcp.json", "content": {"mcpServers": {"cursor-rules-mcp": {"command": "npx", "args": ["-y", "cursor-rules-mcp"]}}}}}, "project_instructions": {"title": "Adding an MCP Server to a Project", "steps": ["In your project folder, create or edit the `.cursor/mcp.json` file.", "Add your MCP server configuration (same format as the global example):"], "example": {"path": ".cursor/mcp.json", "content": {"mcpServers": {"cursor-rules-mcp": {"command": "npx", "args": ["-y", "cursor-rules-mcp"]}}}}}}, "claude": {"global_instructions": {"title": "Adding an MCP Server to Claude <PERSON>op App Globally", "steps": ["Go to **<PERSON> > MCP Servers** and click **Add Global MCP Server**.", "This will open the `~/.claude/mcp.json` file (or you can navigate there manually).", "Add your MCP server configuration like the following:"], "example": {"path": "~/.claude/mcp.json", "content": {"mcpServers": {"cursor-rules-mcp": {"command": "npx", "args": ["-y", "cursor-rules-mcp"]}}}}}, "project_instructions": {"title": "Adding an MCP Server to a Project in Claude", "steps": ["In your project's root folder, create or edit the `.claude/mcp.json` file.", "Add your MCP server configuration in the same format as the global example:"], "example": {"path": ".claude/mcp.json", "content": {"mcpServers": {"cursor-rules-mcp": {"command": "npx", "args": ["-y", "cursor-rules-mcp"]}}}}}}, "usage": {"title": "How to Use the MCP Server", "instructions": ["After installation, return to **Settings > MCP** (in Cursor or Claude) and click the **refresh** button.", "The agent will detect and list the tools provided by the MCP server.", "You can also explicitly instruct the agent to use a tool by mentioning its name and describing what you want it to do."]}}, "installation": {"npm": "npm install -g @last9/mcp-server\nnpx @last9/mcp-server", "docker": "docker run -e LAST9_BASE_URL=<last9_base_url> -e LAST9_AUTH_TOKEN=<token> -e LAST9_REFRESH_TOKEN=<refresh_token> last9/mcp-server:latest", "manual": "Step 1: Ensure all prerequisites (Rust, Last9 account, authentication tokens) are set up.\nStep 2: Clone the repository 'https://github.com/last9/mcp-server'.\nStep 3: Run `cargo build` to compile the source code.\nStep 4: Execute the binary with the required environment variables.", "requirements": "Rust (for development), Last9 account, authentication tokens (LAST9_AUTH_TOKEN and LAST9_REFRESH_TOKEN), Homebrew or NPM (optional)."}, "configuration": {"claude_desktop": "{\n  \"mcpServers\": {\n    \"last9\": {\n      \"command\": \"/opt/homebrew/bin/last9-mcp\",\n      \"env\": {\n        \"LAST9_BASE_URL\": \"https://api.last9.com/\",\n        \"LAST9_AUTH_TOKEN\": \"<auth_token>\",\n        \"LAST9_REFRESH_TOKEN\": \"<refresh_token>\"\n      }\n    }\n  }\n}", "cursor": "{\n  \"mcpServers\": {\n    \"cursor_last9\": {\n      \"command\": \"npx @last9/mcp-server\",\n      \"env\": {\n        \"LAST9_BASE_URL\": \"https://api.last9.com/\",\n        \"LAST9_AUTH_TOKEN\": \"<auth_token>\",\n        \"LAST9_REFRESH_TOKEN\": \"<refresh_token>\"\n      }\n    }\n  }\n}", "vscode": "{\n  \"mcpServers\": {\n    \"vscode_last9\": {\n      \"command\": \"./mcp-server\",\n      \"env\": {\n        \"LAST9_BASE_URL\": \"https://api.last9.com/\",\n        \"LAST9_AUTH_TOKEN\": \"<auth_token>\",\n        \"LAST9_REFRESH_TOKEN\": \"<refresh_token>\"\n      }\n    }\n  }\n}", "windsurf": "{\n  \"mcpServers\": {\n    \"windsurf_last9\": {\n      \"command\": \"docker run last9/mcp-server\",\n      \"env\": {\n        \"LAST9_BASE_URL\": \"https://api.last9.com/\",\n        \"LAST9_AUTH_TOKEN\": \"<auth_token>\",\n        \"LAST9_REFRESH_TOKEN\": \"<refresh_token>\"\n      }\n    }\n  }\n}", "environment_variables": "LAST9_BASE_URL: The base URL for Last9's OTLP endpoint.\nLAST9_AUTH_TOKEN: Authentication token for Last9's API.\nLAST9_REFRESH_TOKEN: Refresh token for renewing session access."}, "troubleshooting": {"common_issues": ["Error: '401 Unauthorized' — Ensure LAST9_AUTH_TOKEN and LAST9_REFRESH_TOKEN are correctly set.", "Installation fails — Verify that Rust, Homebrew, and npm are installed and up-to-date.", "Logs are not fetched — Check Last9 API status and the applied log filtering rules."], "debugging_steps": ["Step 1: Validate environment variable configuration by running `env`.", "Step 2: Confirm Last9 API connectivity by pinging the LAST9_BASE_URL.", "Step 3: Restart the server and tools after configuration changes."], "log_locations": "/var/log/mcp-server.log for Linux/Mac or C:\\Logs\\mcp-server.log for Windows."}, "features": ["Real-time data access for logs, metrics, and traces.", "Support for versatile IDEs including Claude Desktop and VS Code.", "Dynamic log filtering and management with Last9 APIs.", "Service dependency mappings and throughput metrics."], "use_cases": ["Debugging production issues in real-time with AI-powered IDEs like VS Code or Claude Desktop.", "Managing observability workflows by filtering noisy logs through drop rules.", "Enhancing AI-assisted code-fixing workflows with service exceptions and dependency graphs."], "categories": "🛠️ Tools", "technical_specs": {"dependencies": ["@last9/mcp-server", "Homebrew (optional)", "NPM (optional)"], "communication": "Standard I/O (stdio)", "protocol": "Model Context Protocol (MCP)"}, "long_tail_keywords": ["how to install mcp server claude desktop", "mcp server setup guide step by step", "claude desktop mcp configuration tutorial", "cursor mcp server integration guide", "combine multiple ai model responses", "query multiple ollama models simultaneously", "ai decision making with multiple perspectives", "synthesize ai insights from different models", "best mcp servers for ai integration", "mcp server vs single ai model", "multi model ai advisor benefits", "claude desktop ai enhancement tools"], "semantic_keywords": ["large language models", "LLMs", "generative AI", "AI personas", "machine learning models", "natural language processing", "model context protocol", "API integration", "local AI deployment", "AI orchestration", "model ensemble", "AI workflow automation", "decision support systems", "AI-powered insights", "collaborative AI", "multi-agent systems", "AI consultation", "intelligent automation", "claude desktop integration", "cursor ai tools", "vscode ai extensions", "developer productivity tools", "AI development environment"], "related_queries": ["What is a Model Context Protocol server?", "How to use multiple AI models together?", "Best practices for AI model integration", "<PERSON> vs other AI tools", "How to improve AI decision making accuracy", "Local AI deployment vs cloud AI services", "AI model comparison and selection guide", "Setting up AI development environment"], "meta_descriptions": ["Discover Complete Guide to Installing and Using Last9 MCP Server for Real-Time Observability - the ultimate solution for querying multiple AI models simultaneously. Get diverse perspectives, enhanced decision-making, and seamless Claude integration.", "Transform your AI workflow with Complete Guide to Installing and Using Last9 MCP Server for Real-Time Observability. Combine multiple Ollama models, synthesize responses, and make better decisions with our comprehensive MCP server guide.", "Learn how Complete Guide to Installing and Using Last9 MCP Server for Real-Time Observability revolutionizes AI integration by providing a council of AI advisors. Step-by-step setup, features, and real-world use cases included."], "featured_snippets": {"what_is": "Complete Guide to Installing and Using Last9 MCP Server for Real-Time Observability is a Model Context Protocol server that enables users to query multiple AI models simultaneously and synthesize their responses into comprehensive insights.", "how_to_install": "To install this MCP server: 1) Install Node.js and Ollama, 2) Clone the repository, 3) Run npm install, 4) Configure your .env file, 5) Add to Claude Desktop configuration.", "key_benefits": "Key benefits include: diverse AI perspectives, enhanced decision-making accuracy, seamless Claude Desktop integration, customizable AI personas, and local data processing for privacy.", "use_cases": "Primary use cases: business decision support, research analysis, creative problem-solving, technical consultation, and educational applications requiring multiple viewpoints.", "requirements": "Requirements: Node.js 16.x+, Ollama installation, <PERSON> (optional), and basic command-line knowledge for setup and configuration."}, "author_info": {"github_username": "last9", "repository_owner": "last9"}, "case_studies": [{"title": "Real-world Application: Debugging Production Issues In Real Time With Ai Powered Ides Like Vs Code Or Claude <PERSON>.", "scenario": "An organization implemented Complete Guide to Installing and Using Last9 MCP Server for Real-Time Observability to address their specific need for debugging production issues in real-time with ai-powered ides like vs code or claude desktop.", "implementation": "They configured the MCP server with specialized AI models tailored to their debugging production issues in real-time with ai-powered ides like vs code or claude desktop. requirements, enabling comprehensive analysis and decision support", "outcome": "Achieved significant improvements in debugging production issues in real-time with ai-powered ides like vs code or claude desktop. efficiency and quality through multi-perspective AI analysis", "metrics": "Measurable improvements in efficiency, quality, and decision accuracy", "use_case": "Debugging production issues in real-time with AI-powered IDEs like VS Code or Claude Desktop."}, {"title": "Real-world Application: Managing Observability Workflows By Filtering Noisy Logs Through Drop Rules.", "scenario": "An organization implemented Complete Guide to Installing and Using Last9 MCP Server for Real-Time Observability to address their specific need for managing observability workflows by filtering noisy logs through drop rules.", "implementation": "They configured the MCP server with specialized AI models tailored to their managing observability workflows by filtering noisy logs through drop rules. requirements, enabling comprehensive analysis and decision support", "outcome": "Achieved significant improvements in managing observability workflows by filtering noisy logs through drop rules. efficiency and quality through multi-perspective AI analysis", "metrics": "Measurable improvements in efficiency, quality, and decision accuracy", "use_case": "Managing observability workflows by filtering noisy logs through drop rules."}, {"title": "Real-world Application: Enhancing Ai Assisted Code Fixing Workflows With Service Exceptions And Dependency Graphs.", "scenario": "An organization implemented Complete Guide to Installing and Using Last9 MCP Server for Real-Time Observability to address their specific need for enhancing ai-assisted code-fixing workflows with service exceptions and dependency graphs.", "implementation": "They configured the MCP server with specialized AI models tailored to their enhancing ai-assisted code-fixing workflows with service exceptions and dependency graphs. requirements, enabling comprehensive analysis and decision support", "outcome": "Achieved significant improvements in enhancing ai-assisted code-fixing workflows with service exceptions and dependency graphs. efficiency and quality through multi-perspective AI analysis", "metrics": "Measurable improvements in efficiency, quality, and decision accuracy", "use_case": "Enhancing AI-assisted code-fixing workflows with service exceptions and dependency graphs."}], "target_audience": ["general_users"], "quick_start": {"title": "Get Started in 3 Steps", "estimated_time": "5 minutes", "steps": [{"step": 1, "title": "Install Prerequisites", "description": "Install Node.js and Ollama on your system", "command": "npm install -g ollama", "time": "2 minutes"}, {"step": 2, "title": "Setup MCP Server", "description": "Clone repository and install dependencies", "command": "git clone https://github.com/last9/last9-mcp-server && npm install", "time": "2 minutes"}, {"step": 3, "title": "Connect to Claude", "description": "Add server to Claude <PERSON> configuration", "command": "Edit claude_desktop_config.json", "time": "1 minute"}]}, "cta_elements": [{"type": "primary", "text": "Get Started Now", "url": "https://github.com/last9/last9-mcp-server", "description": "Start using this MCP server in your projects"}, {"type": "secondary", "text": "View Documentation", "url": "https://github.com/last9/last9-mcp-server#readme", "description": "Read the complete setup and usage guide"}, {"type": "tertiary", "text": "Join Community", "url": "https://github.com/last9/last9-mcp-server/discussions", "description": "Connect with other users and contributors"}], "table_of_contents": [{"title": "Overview", "anchor": "#overview", "level": 1}, {"title": "Quick Start", "anchor": "#quick-start", "level": 1}, {"title": "Features", "anchor": "#features", "level": 1}, {"title": "Installation", "anchor": "#installation", "level": 1}, {"title": "Configuration", "anchor": "#configuration", "level": 2}, {"title": "Usage Examples", "anchor": "#usage", "level": 1}, {"title": "Tools & Commands", "anchor": "#tools", "level": 1}, {"title": "Troubleshooting", "anchor": "#troubleshooting", "level": 1}, {"title": "FAQ", "anchor": "#faq", "level": 1}, {"title": "Community & Support", "anchor": "#community", "level": 1}], "website_slug": "last9-mcp-server", "url_path": "/mcp-servers/last9-mcp-server", "canonical_url": "https://your-domain.com/mcp-servers/last9-mcp-server", "category": "API Integration", "subcategory": "Time Management", "tags": ["api-integration", "rust", "nodejs"], "difficulty_level": "intermediate", "content_score": 1.0, "last_content_update": "2025-06-24T08:21:21.280432", "content_version": "1.0", "estimated_setup_time": "15-30 minutes", "popularity_score": 0.6, "maintenance_status": "maintained", "breadcrumbs": [{"name": "Home", "url": "/"}, {"name": "MCP Servers", "url": "/mcp-servers"}, {"name": "API Integration", "url": "/mcp-servers/category/api-integration"}, {"name": "last9-mcp-server", "url": "/mcp-servers/last9-mcp-server"}], "related_servers": [], "social_proof": {"github_stars": 33, "github_url": "https://github.com/last9/last9-mcp-server", "community_rating": null}, "compatibility": {"claude_desktop": true, "cursor": true, "vscode": true, "windsurf": true, "zed": true, "claude_code": true}, "installation_complexity": "simple", "dependencies_count": 2, "installation_methods": {"npm": true, "docker": true, "manual": true, "uv": true}, "configuration_examples": {"claude_desktop": {"npm": {"mcpServers": {"last9-last9-mcp-server": {"command": "npx", "args": ["-y", "last9-last9-mcp-server"]}}}, "docker": {"mcpServers": {"last9-last9-mcp-server": {"command": "docker", "args": ["run", "--rm", "-i", "last9-last9-mcp-server:latest"]}}}}, "cursor": {"npm": {"mcpServers": {"last9-last9-mcp-server": {"command": "npx", "args": ["-y", "last9-last9-mcp-server"]}}}}, "vscode": {"servers": {"last9-last9-mcp-server": {"command": "npx", "args": ["-y", "last9-last9-mcp-server"]}}}, "windsurf": {"mcpServers": {"last9-last9-mcp-server": {"command": "npx", "args": ["-y", "last9-last9-mcp-server"]}}}}, "troubleshooting_guide": {"common_issues": [{"issue": "Server not starting", "solution": "Check if all dependencies are installed and environment variables are set correctly"}, {"issue": "Connection timeout", "solution": "Verify network connectivity and server configuration"}, {"issue": "Permission denied", "solution": "Ensure proper file permissions and authentication credentials"}], "debugging_steps": ["Check server logs for error messages", "Verify configuration file syntax", "Test with MCP inspector", "Check environment variables"], "log_locations": {"claude_desktop": "~/Library/Logs/Claude/mcp*.log", "cursor": "Check Cursor output panel", "vscode": "Check VS Code output panel"}}, "page_sections": ["overview", "installation", "configuration", "usage", "tools", "faqs", "troubleshooting"], "content_type": "mcp_server_guide", "search_intent": "informational", "license_info": {"type": "", "commercial_use": false}, "seo_title": "Complete Guide to Installing and Using Last9 MCP Server for Real-Time Observability - Complete MCP Server Guide | Model Context Protocol", "seo_enhanced_overview": "ai integration integration: The Last9 MCP Server provides an implementation of the Model Context Protocol (MCP) designed specifically for real-time observability tasks. It enables AI-powered developer tools, such as <PERSON>, Cursor, Windsurf, and VS Code, to interact seamlessly with production context data like logs, metrics, and traces. By bridging the gap between Last9's observability platform, Control Plane APIs, and AI-driven IDEs, this server streamlines debugging, testing, and auto-fixing workflows, cutting down on manual log inspection time and improving development efficiency.", "seo_structured_data": {"@context": "https://schema.org", "@type": "SoftwareApplication", "name": "Complete Guide to Installing and Using Last9 MCP Server for Real-Time Observability", "description": "The Last9 MCP Server provides an implementation of the Model Context Protocol (MCP) designed specifically for real-time observability tasks. It enables AI-powered developer tools, such as Claude <PERSON>", "applicationCategory": "DeveloperApplication", "operatingSystem": "Cross-platform", "keywords": ["mcp server", "model context protocol", "ai integration", "claude mcp"]}, "seo_open_graph": {"og:title": "Complete Guide to Installing and Using Last9 MCP Server for Real-Time Observability - Complete MCP Server Guide | Model Context Protocol", "og:description": "The Last9 MCP Server provides an implementation of the Model Context Protocol (MCP) designed specifically for real-time observability tasks. It enables AI-powered developer tools, such as Claude <PERSON>", "og:type": "website", "og:site_name": "MCP Server Directory"}}}, {"content": {"id": "imessage-query-fastmcp-mcp-server", "title": "imessage-query-fastmcp-mcp-server", "overview": "The iMessage Query MCP Server provides structured and programmatically accessible tools for querying and analyzing the macOS iMessage database using the Model Context Protocol (MCP). This server is geared towards enabling language models and other compatible clients to extract information from the Messages app database securely and efficiently. The server includes advanced features such as phone number validation, read-only database access, and automatic macOS permission handling. It is designed for developers and users who need to integrate iMessage data into their applications or workflows.", "description": "The iMessage Query MCP Server provides structured and programmatically accessible tools for querying and analyzing the macOS iMessage database using the Model Context Protocol (MCP). This server is geared towards enabling language models and other compatible clients to extract information from the Messages app database securely and efficiently. The server includes advanced features such as phone number validation, read-only database access, and automatic macOS permission handling. It is designed for developers and users who need to integrate iMessage data into their applications or workflows.", "github_repo": "hannesrudolph/imessage-query-fastmcp-mcp-server", "github_url": "https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server", "github_stars": 57, "github_last_updated": "2025-06-24T08:23:15.139603", "github_readme": "https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server/blob/main/README.md", "language": "Python", "tools": [{"id": "get_chat_transcript", "name": "get_chat_transcript", "description": "Retrieve message transcripts for a given phone number with optional date filtering. Ensures phone number validation and E.164 formatting. Defaults to last 7 days if no date range is specified.", "category": "data-access", "type": "resource"}], "running_instructions": {"prerequisites": ["macOS system with iMessage enabled.", "Installation of Python 3.12+.", "MacOS Full Disk Access permission for the relevant MCP client (e.g., Claude <PERSON> or VS Code).", "Installation of the 'uv' tool for package management."], "installation_steps": ["Clone the GitHub repository:", "```bash", "git clone https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server.git", "cd imessage-query-fastmcp-mcp-server", "```", "Install 'uv' package manager:", "```bash", "brew install uv", "```", "or", "```bash", "curl -LsSf https://astral.sh/uv/install.sh | sh", "```", "Add server configuration for your MCP client:", "### Example for <PERSON>:", "Locate config file: `~/Library/Application Support/Claude/claude_desktop_config.json`", "Add the following JSON configuration:", "```json", "{", "  \"mcpServers\": {", "    \"imessage-query\": {", "      \"command\": \"/full/path/to/imessage-query-server.py\"", "    }", "  }", "}", "```", "Replace `/full/path/to/imessage-query-server.py` with the path to your cloned repo.", "Restart your MCP client (e.g., <PERSON>)."], "troubleshooting": ["If Full Disk Access errors occur, ensure the MCP client has been explicitly granted access in macOS System Preferences.", "Verify iMessage is enabled by checking the Messages app preferences."]}, "faqs": {"What is imessage-query-fastmcp-mcp-server?": "The iMessage Query MCP Server provides structured and programmatically accessible tools for querying and analyzing the macOS iMessage database using the Model Context Protocol (MCP). This server is geared towards enabling language models and other compatible clients to extract information from the Messages app database securely and efficiently. The server includes advanced features such as phone number validation, read-only database access, and automatic macOS permission handling. It is designed for developers and users who need to integrate iMessage data into their applications or workflows.", "How do I install this MCP server?": "Follow the installation instructions provided in the documentation.", "What tools are available?": "This server provides 1 tools for enhanced functionality.", "What are the prerequisites?": "Check the running instructions for detailed prerequisites.", "Is this server compatible with Claude Desktop?": "Yes, this MCP server is compatible with Claude Desktop, Cursor, and other MCP clients."}, "seo_content": {"slug": "imessage-query-fastmcp-mcp-server", "schema_org_json_ld": {"@context": "https://schema.org", "@type": "SoftwareApplication", "name": "imessage-query-fastmcp-mcp-server", "description": "The iMessage Query MCP Server provides structured and programmatically accessible tools for querying and analyzing the macOS iMessage database using the Model Context Protocol (MCP). This server is geared towards enabling language models and other compatible clients to extract information from the Messages app database securely and efficiently. The server includes advanced features such as phone number validation, read-only database access, and automatic macOS permission handling. It is designed for developers and users who need to integrate iMessage data into their applications or workflows.", "applicationCategory": "DeveloperApplication", "operatingSystem": "Cross-platform", "url": "https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server", "downloadUrl": "https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server", "softwareVersion": "1.0", "keywords": [], "offers": {"@type": "Offer", "price": "Free", "priceCurrency": "USD"}, "featureList": ["get_chat_transcript: Retrieve message transcripts for a given phone number with optional date filtering. Ensures phone number validation and E.164 formatting. Defaults to last 7 days if no date range is specified."]}, "structured_data": "The iMessage Query MCP Server provides structured and programmatically accessible tools for querying and analyzing the macOS iMessage database using the Model Context Protocol (MCP). This server is geared towards enabling language models and other compatible clients to extract information from the M...", "faq_schema": {"@context": "https://schema.org", "@type": "FAQPage", "mainEntity": [{"@type": "Question", "name": "What is imessage-query-fastmcp-mcp-server?", "acceptedAnswer": {"@type": "Answer", "text": "The iMessage Query MCP Server provides structured and programmatically accessible tools for querying and analyzing the macOS iMessage database using the Model Context Protocol (MCP). This server is geared towards enabling language models and other compatible clients to extract information from the Messages app database securely and efficiently. The server includes advanced features such as phone number validation, read-only database access, and automatic macOS permission handling. It is designed for developers and users who need to integrate iMessage data into their applications or workflows."}}, {"@type": "Question", "name": "How do I install this MCP server?", "acceptedAnswer": {"@type": "Answer", "text": "Follow the installation instructions provided in the documentation."}}, {"@type": "Question", "name": "What tools are available?", "acceptedAnswer": {"@type": "Answer", "text": "This server provides 1 tools for enhanced functionality."}}, {"@type": "Question", "name": "What are the prerequisites?", "acceptedAnswer": {"@type": "Answer", "text": "Check the running instructions for detailed prerequisites."}}, {"@type": "Question", "name": "Is this server compatible with <PERSON>?", "acceptedAnswer": {"@type": "Answer", "text": "Yes, this MCP server is compatible with Claude Desktop, Cursor, and other MCP clients."}}]}}, "installation_instructions": {"overview": "There are two ways to add an MCP server to Cursor and Claude <PERSON> App:\n\n1. **Globally**: Available in all of your projects by adding it to the global MCP settings file.\n2. **Per Project**: Available only within a specific project by adding it to the project's MCP settings file.", "cursor": {"global_instructions": {"title": "Adding an MCP Server to Cursor Globally", "steps": ["Go to **<PERSON><PERSON><PERSON> > MCP** and click **Add new global MCP server**.", "This will open the `~/.cursor/mcp.json` file.", "Add your MCP server configuration like the following:"], "example": {"path": "~/.cursor/mcp.json", "content": {"mcpServers": {"cursor-rules-mcp": {"command": "npx", "args": ["-y", "cursor-rules-mcp"]}}}}}, "project_instructions": {"title": "Adding an MCP Server to a Project", "steps": ["In your project folder, create or edit the `.cursor/mcp.json` file.", "Add your MCP server configuration (same format as the global example):"], "example": {"path": ".cursor/mcp.json", "content": {"mcpServers": {"cursor-rules-mcp": {"command": "npx", "args": ["-y", "cursor-rules-mcp"]}}}}}}, "claude": {"global_instructions": {"title": "Adding an MCP Server to Claude <PERSON>op App Globally", "steps": ["Go to **<PERSON> > MCP Servers** and click **Add Global MCP Server**.", "This will open the `~/.claude/mcp_settings.json` file (or you can navigate there manually).", "Add your MCP server configuration like the following:"], "example": {"path": "~/.claude/mcp_settings.json", "content": {"mcpServers": {"cursor-rules-mcp": {"command": "npx", "args": ["-y", "cursor-rules-mcp"]}}}}}, "project_instructions": {"title": "Adding an MCP Server to a Project in Claude", "steps": ["In your project's root folder, create or edit the `.claude/mcp_settings.json` file.", "Add your MCP server configuration in the same format as the global example:"], "example": {"path": ".claude/mcp_settings.json", "content": {"mcpServers": {"cursor-rules-mcp": {"command": "npx", "args": ["-y", "cursor-rules-mcp"]}}}}}}, "usage": {"title": "How to Use the MCP Server", "instructions": ["After installation, return to **Settings > MCP** (in Cursor or Claude) and click the **refresh** button.", "The agent will detect and list the tools provided by the MCP server.", "You can also explicitly instruct the agent to use a tool by mentioning its name and describing what you want it to do."]}}, "technical_specs": {"dependencies": [{"name": "fastmcp", "purpose": "Core MCP server implementation framework."}, {"name": "imessagedb", "purpose": "Library for interfacing with the macOS Messages database."}, {"name": "phonenumbers", "purpose": "Phone number validation and formatting library by Google."}, {"name": "uv", "purpose": "A Python package manager that automates dependency setup."}], "language": "Python", "runtime": "Python 3.8+", "communication": "Standard I/O (stdio)", "protocol": "Model Context Protocol (MCP)"}, "long_tail_keywords": ["how to install mcp server claude desktop", "mcp server setup guide step by step", "claude desktop mcp configuration tutorial", "cursor mcp server integration guide", "combine multiple ai model responses", "query multiple ollama models simultaneously", "ai decision making with multiple perspectives", "synthesize ai insights from different models", "best mcp servers for ai integration", "mcp server vs single ai model", "multi model ai advisor benefits", "claude desktop ai enhancement tools"], "semantic_keywords": ["large language models", "LLMs", "generative AI", "AI personas", "machine learning models", "natural language processing", "model context protocol", "API integration", "local AI deployment", "AI orchestration", "model ensemble", "AI workflow automation", "decision support systems", "AI-powered insights", "collaborative AI", "multi-agent systems", "AI consultation", "intelligent automation", "claude desktop integration", "cursor ai tools", "vscode ai extensions", "developer productivity tools", "AI development environment"], "related_queries": ["What is a Model Context Protocol server?", "How to use multiple AI models together?", "Best practices for AI model integration", "<PERSON> vs other AI tools", "How to improve AI decision making accuracy", "Local AI deployment vs cloud AI services", "AI model comparison and selection guide", "Setting up AI development environment"], "meta_descriptions": ["Discover imessage-query-fastmcp-mcp-server - the ultimate solution for querying multiple AI models simultaneously. Get diverse perspectives, enhanced decision-making, and seamless Claude integration.", "Transform your AI workflow with imessage-query-fastmcp-mcp-server. Combine multiple Ollama models, synthesize responses, and make better decisions with our comprehensive MCP server guide.", "Learn how imessage-query-fastmcp-mcp-server revolutionizes AI integration by providing a council of AI advisors. Step-by-step setup, features, and real-world use cases included."], "featured_snippets": {"what_is": "imessage-query-fastmcp-mcp-server is a Model Context Protocol server that enables users to query multiple AI models simultaneously and synthesize their responses into comprehensive insights.", "how_to_install": "To install this MCP server: 1) Install Node.js and Ollama, 2) Clone the repository, 3) Run npm install, 4) Configure your .env file, 5) Add to Claude Desktop configuration.", "key_benefits": "Key benefits include: diverse AI perspectives, enhanced decision-making accuracy, seamless Claude Desktop integration, customizable AI personas, and local data processing for privacy.", "use_cases": "Primary use cases: business decision support, research analysis, creative problem-solving, technical consultation, and educational applications requiring multiple viewpoints.", "requirements": "Requirements: Node.js 16.x+, Ollama installation, <PERSON> (optional), and basic command-line knowledge for setup and configuration."}, "author_info": {"github_username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "repository_owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "case_studies": [], "target_audience": ["data_scientists"], "quick_start": {"title": "Get Started in 3 Steps", "estimated_time": "5 minutes", "steps": [{"step": 1, "title": "Install Prerequisites", "description": "Install Node.js and Ollama on your system", "command": "npm install -g ollama", "time": "2 minutes"}, {"step": 2, "title": "Setup MCP Server", "description": "Clone repository and install dependencies", "command": "git clone https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server && npm install", "time": "2 minutes"}, {"step": 3, "title": "Connect to Claude", "description": "Add server to Claude <PERSON> configuration", "command": "Edit claude_desktop_config.json", "time": "1 minute"}]}, "cta_elements": [{"type": "primary", "text": "Get Started Now", "url": "https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server", "description": "Start using this MCP server in your projects"}, {"type": "secondary", "text": "View Documentation", "url": "https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server#readme", "description": "Read the complete setup and usage guide"}, {"type": "tertiary", "text": "Join Community", "url": "https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server/discussions", "description": "Connect with other users and contributors"}], "table_of_contents": [{"title": "Overview", "anchor": "#overview", "level": 1}, {"title": "Quick Start", "anchor": "#quick-start", "level": 1}, {"title": "Features", "anchor": "#features", "level": 1}, {"title": "Installation", "anchor": "#installation", "level": 1}, {"title": "Configuration", "anchor": "#configuration", "level": 2}, {"title": "Usage Examples", "anchor": "#usage", "level": 1}, {"title": "Tools & Commands", "anchor": "#tools", "level": 1}, {"title": "Troubleshooting", "anchor": "#troubleshooting", "level": 1}, {"title": "FAQ", "anchor": "#faq", "level": 1}, {"title": "Community & Support", "anchor": "#community", "level": 1}], "categories": "🛠️ Tools", "website_slug": "imessage-query-fastmcp-mcp-server", "url_path": "/mcp-servers/imessage-query-fastmcp-mcp-server", "canonical_url": "https://your-domain.com/mcp-servers/imessage-query-fastmcp-mcp-server", "category": "Database", "subcategory": "Search Engine", "tags": ["python", "database"], "difficulty_level": "beginner", "content_score": 1.0, "last_content_update": "2025-06-24T08:23:15.140053", "content_version": "1.0", "estimated_setup_time": "5-10 minutes", "popularity_score": 0.8, "maintenance_status": "actively_maintained", "breadcrumbs": [{"name": "Home", "url": "/"}, {"name": "MCP Servers", "url": "/mcp-servers"}, {"name": "Database", "url": "/mcp-servers/category/database"}, {"name": "imessage-query-fastmcp-mcp-server", "url": "/mcp-servers/imessage-query-fastmcp-mcp-server"}], "related_servers": [], "social_proof": {"github_stars": 57, "github_url": "https://github.com/hannesrudolph/imessage-query-fastmcp-mcp-server", "community_rating": null}, "compatibility": {"claude_desktop": true, "cursor": true, "vscode": true, "windsurf": true, "zed": true, "claude_code": true}, "installation_complexity": "complex", "dependencies_count": 0, "installation_methods": {"npm": true, "docker": true, "manual": true, "uv": true}, "configuration_examples": {"claude_desktop": {"npm": {"mcpServers": {"hannesrudolph-imessage-query-fastmcp-mcp-server": {"command": "npx", "args": ["-y", "hannes<PERSON><PERSON><PERSON>-imessage-query-fastmcp-mcp-server"]}}}, "docker": {"mcpServers": {"hannesrudolph-imessage-query-fastmcp-mcp-server": {"command": "docker", "args": ["run", "--rm", "-i", "hannes<PERSON><PERSON><PERSON>-imessage-query-fastmcp-mcp-server:latest"]}}}}, "cursor": {"npm": {"mcpServers": {"hannesrudolph-imessage-query-fastmcp-mcp-server": {"command": "npx", "args": ["-y", "hannes<PERSON><PERSON><PERSON>-imessage-query-fastmcp-mcp-server"]}}}}, "vscode": {"servers": {"hannesrudolph-imessage-query-fastmcp-mcp-server": {"command": "npx", "args": ["-y", "hannes<PERSON><PERSON><PERSON>-imessage-query-fastmcp-mcp-server"]}}}, "windsurf": {"mcpServers": {"hannesrudolph-imessage-query-fastmcp-mcp-server": {"command": "npx", "args": ["-y", "hannes<PERSON><PERSON><PERSON>-imessage-query-fastmcp-mcp-server"]}}}}, "troubleshooting_guide": {"common_issues": [{"issue": "Server not starting", "solution": "Check if all dependencies are installed and environment variables are set correctly"}, {"issue": "Connection timeout", "solution": "Verify network connectivity and server configuration"}, {"issue": "Permission denied", "solution": "Ensure proper file permissions and authentication credentials"}], "debugging_steps": ["Check server logs for error messages", "Verify configuration file syntax", "Test with MCP inspector", "Check environment variables"], "log_locations": {"claude_desktop": "~/Library/Logs/Claude/mcp*.log", "cursor": "Check Cursor output panel", "vscode": "Check VS Code output panel"}}, "page_sections": ["overview", "installation", "configuration", "usage", "tools", "faqs", "troubleshooting"], "content_type": "mcp_server_guide", "search_intent": "informational", "license_info": {"type": "", "commercial_use": false}, "seo_title": "imessage-query-fastmcp-mcp-server - Complete MCP Server Guide | Model Context Protocol", "seo_enhanced_overview": "ai integration integration: The iMessage Query MCP Server provides structured and programmatically accessible tools for querying and analyzing the macOS iMessage database using the Model Context Protocol (MCP). This server is geared towards enabling language models and other compatible clients to extract information from the Messages app database securely and efficiently. The server includes advanced features such as phone number validation, read-only database access, and automatic macOS permission handling. It is designed for developers and users who need to integrate iMessage data into their applications or workflows.", "seo_structured_data": {"@context": "https://schema.org", "@type": "SoftwareApplication", "name": "imessage-query-fastmcp-mcp-server", "description": "The iMessage Query MCP Server provides structured and programmatically accessible tools for querying and analyzing the macOS iMessage database using the Model Context Protocol (MCP). This server is ge", "applicationCategory": "DeveloperApplication", "operatingSystem": "Cross-platform", "keywords": ["mcp server", "model context protocol", "ai integration", "claude mcp"]}, "seo_open_graph": {"og:title": "imessage-query-fastmcp-mcp-server - Complete MCP Server Guide | Model Context Protocol", "og:description": "The iMessage Query MCP Server provides structured and programmatically accessible tools for querying and analyzing the macOS iMessage database using the Model Context Protocol (MCP). This server is ge", "og:type": "website", "og:site_name": "MCP Server Directory"}}}, {"content": {"id": "framelink-figma-mcp-server-installation-guide", "title": "Framelink Figma MCP: Optimize Figma Design Metadata for AI Coding Tools", "overview": "The Framelink Figma MCP server is a cutting-edge tool designed to bridge the gap between design and development by enabling seamless access to Figma design data in an AI-assisted coding environment. Built with a focus on AI-powered coding tools, like Cursor, the server simplifies and filters Figma metadata into actionable and contextually relevant formats for improved accuracy in code suggestions. Whether you're a developer integrating Figma designs into React projects or an AI team automating workflows, the Framelink Figma MCP ensures efficient collaboration and performance optimization.", "description": "Framelink Figma MCP serves as an MCP-compliant server that fetches, processes, and contextualizes Figma design data for streamlined AI integrations. By leveraging Node.js and TypeScript, this server communicates using the stdio protocol and interfaces with the Figma API through secure authentication tokens. It analyzes and simplifies complex design contexts to deliver only the most relevant metadata to AI coding models, enhancing the precision of design-to-code workflows. The server is optimized for local deployment via `npx` commands, ensuring flexibility and ease of use for developers while maintaining high performance for large-scale design projects.", "github_repo": "GLips/Figma-Context-MCP", "github_url": "https://github.com/GLips/Figma-Context-MCP", "github_stars": 8500, "github_last_updated": "2025-06-24T23:31:28.074639", "github_readme": "https://github.com/GLips/Figma-Context-MCP/blob/main/README.md", "language": "TypeScript", "tools": [{"id": "figma-developer-mcp", "name": "Figma Developer MCP", "description": "The core tool that implements the MCP server functionality for fetching and simplifying Figma design metadata.", "category": "data-access", "type": "resource"}, {"id": "figma-api-token-management", "name": "Figma API Token Management", "description": "Manages authentication by generating and configuring Figma Personal Access Tokens through environment variables or direct arguments.", "category": "management", "type": "utility"}, {"id": "design-metadata-translator", "name": "Design Metadata Translator", "description": "Filters and translates complex Figma design data into actionable and simplified metadata for MCP usage.", "category": "utility", "type": "query"}], "running_instructions": "To run the Framelink Figma MCP Server: 1. Ensure Node.js is installed. 2. Generate a Figma API token and configure your environment. 3. Execute the MCP server using the provided npx command or configure your IDE.", "faqs": {"What is the Framelink Figma MCP server?": "The Framelink Figma MCP server bridges design workflows from Figma to AI coding tools, processing metadata for tasks like React component generation.", "How do I install the MCP server?": "Using Node.js, run `npx -y figma-developer-mcp --figma-api-key=YOUR-KEY --stdio`. Alternatively, use Docker with `docker run`.", "What are the system requirements?": "Minimum requirements include Node.js >=16.0 and a valid Figma Personal Access Token.", "How do I troubleshoot common issues?": "Refer to the troubleshooting guide to resolve connection, authentication, or environment configuration issues.", "What IDEs are compatible with this server?": "The server is compatible with tools like Claude Desktop, Cursor, and VS Code."}, "seo_content": {"meta_title": "Framelink Figma MCP: Simplify Design Data for AI Coding Tools", "meta_description": "Learn how to set up Framelink Figma MCP to optimize Figma design workflows for AI coding tools using Node.js. Maximize accuracy and reduce overhead.", "headings": ["H1: Framelink Figma MCP Server Setup Guide", "H2: Key Features of Framelink Figma MCP", "H2: Installation and Configuration Instructions", "H2: Use Cases and Benefits", "H2: Troubleshooting Common Issues", "H3: Node.js and Environment Configuration", "H3: Integrating with AI Development Tools", "H3: Generating Accurate Code with Design Metadata"], "keywords": ["framelink figma mcp server", "figma metadata for AI tools", "setup figma MCP server", "optimize figma design context", "Node.js MCP implementation", "figma API authentication tutorial"], "structured_data": "Comprehensive Schema.org markup for enhanced search appearance", "slug": "figma-context-mcp", "schema_org_json_ld": {"@context": "https://schema.org", "@type": "SoftwareApplication", "name": "Framelink Figma MCP: Optimize Figma Design Metadata for AI Coding Tools", "description": "Framelink Figma MCP serves as an MCP-compliant server that fetches, processes, and contextualizes Figma design data for streamlined AI integrations. By leveraging Node.js and TypeScript, this server communicates using the stdio protocol and interfaces with the Figma API through secure authentication tokens. It analyzes and simplifies complex design contexts to deliver only the most relevant metadata to AI coding models, enhancing the precision of design-to-code workflows. The server is optimized for local deployment via `npx` commands, ensuring flexibility and ease of use for developers while maintaining high performance for large-scale design projects.", "applicationCategory": "DeveloperApplication", "operatingSystem": "Cross-platform", "url": "https://github.com/GLips/Figma-Context-MCP", "downloadUrl": "https://github.com/GLips/Figma-Context-MCP", "softwareVersion": "1.0", "keywords": ["framelink figma mcp server", "figma metadata for AI tools", "setup figma MCP server", "optimize figma design context", "Node.js MCP implementation", "figma API authentication tutorial"], "offers": {"@type": "Offer", "price": "Free", "priceCurrency": "USD"}, "featureList": ["Figma Developer MCP: The core tool that implements the MCP server functionality for fetching and simplifying Figma design metadata.", "Figma API Token Management: Manages authentication by generating and configuring Figma Personal Access Tokens through environment variables or direct arguments.", "Design Metadata Translator: Filters and translates complex Figma design data into actionable and simplified metadata for MCP usage."]}, "faq_schema": {"@context": "https://schema.org", "@type": "FAQPage", "mainEntity": [{"@type": "Question", "name": "What is the Framelink Figma MCP server?", "acceptedAnswer": {"@type": "Answer", "text": "The Framelink Figma MCP server bridges design workflows from Figma to AI coding tools, processing metadata for tasks like React component generation."}}, {"@type": "Question", "name": "How do I install the MCP server?", "acceptedAnswer": {"@type": "Answer", "text": "Using Node.js, run `npx -y figma-developer-mcp --figma-api-key=YOUR-KEY --stdio`. Alternatively, use Docker with `docker run`."}}, {"@type": "Question", "name": "What are the system requirements?", "acceptedAnswer": {"@type": "Answer", "text": "Minimum requirements include Node.js >=16.0 and a valid Figma Personal Access Token."}}, {"@type": "Question", "name": "How do I troubleshoot common issues?", "acceptedAnswer": {"@type": "Answer", "text": "Refer to the troubleshooting guide to resolve connection, authentication, or environment configuration issues."}}, {"@type": "Question", "name": "What IDEs are compatible with this server?", "acceptedAnswer": {"@type": "Answer", "text": "The server is compatible with tools like Claude Desktop, Cursor, and VS Code."}}]}}, "installation_instructions": {"overview": "There are two ways to add an MCP server to Cursor and Claude <PERSON> App:\n\n1. **Globally**: Available in all of your projects by adding it to the global MCP settings file.\n2. **Per Project**: Available only within a specific project by adding it to the project's MCP settings file.", "cursor": {"global_instructions": {"title": "Adding an MCP Server to Cursor Globally", "steps": ["Go to **<PERSON><PERSON><PERSON> > MCP** and click **Add new global MCP server**.", "This will open the `~/.cursor/mcp.json` file.", "Add your MCP server configuration like the following:"], "example": {"path": "~/.cursor/mcp.json", "content": {"mcpServers": {"cursor-rules-mcp": {"command": "npx", "args": ["-y", "cursor-rules-mcp"]}}}}}, "project_instructions": {"title": "Adding an MCP Server to a Project", "steps": ["In your project folder, create or edit the `.cursor/mcp.json` file.", "Add your MCP server configuration (same format as the global example):"], "example": {"path": ".cursor/mcp.json", "content": {"mcpServers": {"cursor-rules-mcp": {"command": "npx", "args": ["-y", "cursor-rules-mcp"]}}}}}}, "claude": {"global_instructions": {"title": "Adding an MCP Server to Claude <PERSON>op App Globally", "steps": ["Go to **<PERSON> > MCP Servers** and click **Add Global MCP Server**.", "This will open the `~/.claude/mcp_settings.json` file (or you can navigate there manually).", "Add your MCP server configuration like the following:"], "example": {"path": "~/.claude/mcp_settings.json", "content": {"mcpServers": {"cursor-rules-mcp": {"command": "npx", "args": ["-y", "cursor-rules-mcp"]}}}}}, "project_instructions": {"title": "Adding an MCP Server to a Project in Claude", "steps": ["In your project's root folder, create or edit the `.claude/mcp_settings.json` file.", "Add your MCP server configuration in the same format as the global example:"], "example": {"path": ".claude/mcp_settings.json", "content": {"mcpServers": {"cursor-rules-mcp": {"command": "npx", "args": ["-y", "cursor-rules-mcp"]}}}}}}, "usage": {"title": "How to Use the MCP Server", "instructions": ["After installation, return to **Settings > MCP** (in Cursor or Claude) and click the **refresh** button.", "The agent will detect and list the tools provided by the MCP server.", "You can also explicitly instruct the agent to use a tool by mentioning its name and describing what you want it to do."]}}, "installation": {"npm": "npx -y figma-developer-mcp --figma-api-key=YOUR-KEY --stdio", "docker": "docker run -e FIGMA_API_KEY=YOUR-KEY figma-developer-mcp", "manual": ["Ensure Node.js >=16.0 is installed.", "Run `npx -y figma-developer-mcp --figma-api-key=YOUR-KEY --stdio`.", "Set up configuration files in your IDE or local environment."], "requirements": ["Node.js >=16.0", "Figma Personal Access Token"]}, "configuration": {"claude_desktop": {"example": {"mcpServers": {"Framelink Figma MCP": {"command": "npx", "args": ["-y", "figma-developer-mcp", "--figma-api-key=YOUR-KEY", "--st<PERSON>"]}}}}, "cursor": {"example": {"mcpServers": {"Framelink Figma MCP": {"command": "npx", "args": ["-y", "figma-developer-mcp", "--figma-api-key=YOUR-KEY", "--st<PERSON>"]}}}}, "vscode": {"example": {"mcpServers": {"Framelink Figma MCP": {"command": "npx", "args": ["-y", "figma-developer-mcp", "--figma-api-key=YOUR-KEY", "--st<PERSON>"]}}}}, "windsurf": {"example": {"mcpServers": {"Framelink Figma MCP": {"command": "npx", "args": ["-y", "figma-developer-mcp", "--figma-api-key=YOUR-KEY", "--st<PERSON>"]}}}}, "environment_variables": [{"name": "FIGMA_API_KEY", "description": "Used for authenticating requests with the Figma API.", "example": "FIGMA_API_KEY=your-figma-api-personal-access-token"}, {"name": "PORT", "description": "Optional environment variable to set a custom port for the MCP server.", "example": "PORT=8080"}]}, "troubleshooting": {"common_issues": [{"issue": "Node.js command not found", "solution": "Ensure Node.js is installed and added to your PATH."}, {"issue": "Invalid Figma API Token", "solution": "Verify your token using Figma's API management page and ensure it has the right permissions."}, {"issue": "Connection refused by server", "solution": "Check if firewall or network settings are blocking the communication port."}], "debugging_steps": ["Verify Node.js and npm are installed and accessible.", "Check the syntax of your MCP configuration files.", "Inspect server logs for missing or incorrect tokens."], "log_locations": "/var/log/figma-mcp-server.log"}, "features": ["Seamless integration with MCP-compatible AI tools like Cursor.", "Optimized design data processing to improve AI coding accuracy.", "Easy setup via Node.js commands and minimal configuration overhead.", "Supports stdio communication protocol for flexible deployment."], "use_cases": [{"scenario": "Integrating Figma designs into React components.", "steps": ["Set up the Figma MCP server in your IDE.", "Fetch design metadata for targeted frames.", "Translate the metadata into React component structures."]}, {"scenario": "Real-time design validation for automated testing.", "steps": ["Automate metadata fetch processes using the MCP server.", "Use extracted layout and styles in test scripts.", "Validate design consistency with application standards."]}], "categories": "🛠️ Tools", "technical_specs": {"language": "TypeScript", "runtime": "Node.js", "communication": "Standard I/O (stdio)", "protocol": "Model Context Protocol (MCP)"}, "long_tail_keywords": ["how to install mcp server claude desktop", "mcp server setup guide step by step", "claude desktop mcp configuration tutorial", "cursor mcp server integration guide", "combine multiple ai model responses", "query multiple ollama models simultaneously", "ai decision making with multiple perspectives", "synthesize ai insights from different models", "best mcp servers for ai integration", "mcp server vs single ai model", "multi model ai advisor benefits", "claude desktop ai enhancement tools"], "semantic_keywords": ["large language models", "LLMs", "generative AI", "AI personas", "machine learning models", "natural language processing", "model context protocol", "API integration", "local AI deployment", "AI orchestration", "model ensemble", "AI workflow automation", "decision support systems", "AI-powered insights", "collaborative AI", "multi-agent systems", "AI consultation", "intelligent automation", "claude desktop integration", "cursor ai tools", "vscode ai extensions", "developer productivity tools", "AI development environment"], "related_queries": ["What is a Model Context Protocol server?", "How to use multiple AI models together?", "Best practices for AI model integration", "<PERSON> vs other AI tools", "How to improve AI decision making accuracy", "Local AI deployment vs cloud AI services", "AI model comparison and selection guide", "Setting up AI development environment"], "meta_descriptions": ["Discover Framelink Figma MCP: Optimize Figma Design Metadata for AI Coding Tools - the ultimate solution for querying multiple AI models simultaneously. Get diverse perspectives, enhanced decision-making, and seamless Claude integration.", "Transform your AI workflow with Framelink Figma MCP: Optimize Figma Design Metadata for AI Coding Tools. Combine multiple Ollama models, synthesize responses, and make better decisions with our comprehensive MCP server guide.", "Learn how Framelink Figma MCP: Optimize Figma Design Metadata for AI Coding Tools revolutionizes AI integration by providing a council of AI advisors. Step-by-step setup, features, and real-world use cases included."], "featured_snippets": {"what_is": "Framelink Figma MCP: Optimize Figma Design Metadata for AI Coding Tools is a Model Context Protocol server that enables users to query multiple AI models simultaneously and synthesize their responses into comprehensive insights.", "how_to_install": "To install this MCP server: 1) Install Node.js and Ollama, 2) Clone the repository, 3) Run npm install, 4) Configure your .env file, 5) Add to Claude Desktop configuration.", "key_benefits": "Key benefits include: diverse AI perspectives, enhanced decision-making accuracy, seamless Claude Desktop integration, customizable AI personas, and local data processing for privacy.", "use_cases": "Primary use cases: business decision support, research analysis, creative problem-solving, technical consultation, and educational applications requiring multiple viewpoints.", "requirements": "Requirements: Node.js 16.x+, Ollama installation, <PERSON> (optional), and basic command-line knowledge for setup and configuration."}, "author_info": {"github_username": "GLips", "repository_owner": "GLips"}, "case_studies": [{"title": "Real-world Application: {'Scenario': 'Integrating Figma Designs Into React Components.', 'Steps': ['Set Up The Figma Mcp Server In Your Ide.', 'Fetch Design Metadata For Targeted Frames.', 'Translate The Metadata Into React Component Structures.']}", "scenario": "An organization implemented Framelink Figma MCP: Optimize Figma Design Metadata for AI Coding Tools to address their specific need for {'scenario': 'integrating figma designs into react components.', 'steps': ['set up the figma mcp server in your ide.', 'fetch design metadata for targeted frames.', 'translate the metadata into react component structures.']}", "implementation": "They configured the MCP server with specialized AI models tailored to their {'scenario': 'integrating figma designs into react components.', 'steps': ['set up the figma mcp server in your ide.', 'fetch design metadata for targeted frames.', 'translate the metadata into react component structures.']} requirements, enabling comprehensive analysis and decision support", "outcome": "Achieved significant improvements in {'scenario': 'integrating figma designs into react components.', 'steps': ['set up the figma mcp server in your ide.', 'fetch design metadata for targeted frames.', 'translate the metadata into react component structures.']} efficiency and quality through multi-perspective AI analysis", "metrics": "Measurable improvements in efficiency, quality, and decision accuracy", "use_case": "{'scenario': 'Integrating Figma designs into React components.', 'steps': ['Set up the Figma MCP server in your IDE.', 'Fetch design metadata for targeted frames.', 'Translate the metadata into React component structures.']}"}, {"title": "Real-world Application: {'Scenario': 'Real Time Design Validation For Automated Testing.', 'Steps': ['Automate Metadata Fetch Processes Using The Mcp Server.', 'Use Extracted Layout And Styles In Test Scripts.', 'Validate Design Consistency With Application Standards.']}", "scenario": "An organization implemented Framelink Figma MCP: Optimize Figma Design Metadata for AI Coding Tools to address their specific need for {'scenario': 'real-time design validation for automated testing.', 'steps': ['automate metadata fetch processes using the mcp server.', 'use extracted layout and styles in test scripts.', 'validate design consistency with application standards.']}", "implementation": "They configured the MCP server with specialized AI models tailored to their {'scenario': 'real-time design validation for automated testing.', 'steps': ['automate metadata fetch processes using the mcp server.', 'use extracted layout and styles in test scripts.', 'validate design consistency with application standards.']} requirements, enabling comprehensive analysis and decision support", "outcome": "Achieved significant improvements in {'scenario': 'real-time design validation for automated testing.', 'steps': ['automate metadata fetch processes using the mcp server.', 'use extracted layout and styles in test scripts.', 'validate design consistency with application standards.']} efficiency and quality through multi-perspective AI analysis", "metrics": "Measurable improvements in efficiency, quality, and decision accuracy", "use_case": "{'scenario': 'Real-time design validation for automated testing.', 'steps': ['Automate metadata fetch processes using the MCP server.', 'Use extracted layout and styles in test scripts.', 'Validate design consistency with application standards.']}"}], "target_audience": ["general_users"], "quick_start": {"title": "Get Started in 3 Steps", "estimated_time": "5 minutes", "steps": [{"step": 1, "title": "Install Prerequisites", "description": "Install Node.js and Ollama on your system", "command": "npm install -g ollama", "time": "2 minutes"}, {"step": 2, "title": "Setup MCP Server", "description": "Clone repository and install dependencies", "command": "git clone https://github.com/GLips/Figma-Context-MCP && npm install", "time": "2 minutes"}, {"step": 3, "title": "Connect to Claude", "description": "Add server to Claude <PERSON> configuration", "command": "Edit claude_desktop_config.json", "time": "1 minute"}]}, "cta_elements": [{"type": "primary", "text": "Get Started Now", "url": "https://github.com/GLips/Figma-Context-MCP", "description": "Start using this MCP server in your projects"}, {"type": "secondary", "text": "View Documentation", "url": "https://github.com/GLips/Figma-Context-MCP#readme", "description": "Read the complete setup and usage guide"}, {"type": "tertiary", "text": "Join Community", "url": "https://github.com/GLips/Figma-Context-MCP/discussions", "description": "Connect with other users and contributors"}], "table_of_contents": [{"title": "Overview", "anchor": "#overview", "level": 1}, {"title": "Quick Start", "anchor": "#quick-start", "level": 1}, {"title": "Features", "anchor": "#features", "level": 1}, {"title": "Installation", "anchor": "#installation", "level": 1}, {"title": "Configuration", "anchor": "#configuration", "level": 2}, {"title": "Usage Examples", "anchor": "#usage", "level": 1}, {"title": "Tools & Commands", "anchor": "#tools", "level": 1}, {"title": "Troubleshooting", "anchor": "#troubleshooting", "level": 1}, {"title": "FAQ", "anchor": "#faq", "level": 1}, {"title": "Community & Support", "anchor": "#community", "level": 1}], "website_slug": "figma-context-mcp", "url_path": "/mcp-servers/figma-context-mcp", "canonical_url": "https://your-domain.com/mcp-servers/figma-context-mcp", "category": "Development", "subcategory": "Cursor IDE", "tags": ["typescript", "development"], "difficulty_level": "beginner", "content_score": 1.0, "last_content_update": "2025-06-24T23:31:28.074685", "content_version": "1.0", "estimated_setup_time": "5-10 minutes", "popularity_score": 1.0, "maintenance_status": "actively_maintained", "breadcrumbs": [{"name": "Home", "url": "/"}, {"name": "MCP Servers", "url": "/mcp-servers"}, {"name": "Development", "url": "/mcp-servers/category/development"}, {"name": "Figma-Context-MCP", "url": "/mcp-servers/figma-context-mcp"}], "related_servers": [], "social_proof": {"github_stars": 8500, "github_url": "https://github.com/GLips/Figma-Context-MCP", "community_rating": null}, "compatibility": {"claude_desktop": true, "cursor": true, "vscode": true, "windsurf": true, "zed": true, "claude_code": true}, "installation_complexity": "complex", "dependencies_count": 2, "installation_methods": {"npm": true, "docker": true, "manual": true, "uv": true}, "configuration_examples": {"claude_desktop": {"npm": {"mcpServers": {"GLips-Figma-Context-MCP": {"command": "npx", "args": ["-y", "GLips-Figma-Context-MCP"]}}}, "docker": {"mcpServers": {"GLips-Figma-Context-MCP": {"command": "docker", "args": ["run", "--rm", "-i", "GLips-Figma-Context-MCP:latest"]}}}}, "cursor": {"npm": {"mcpServers": {"GLips-Figma-Context-MCP": {"command": "npx", "args": ["-y", "GLips-Figma-Context-MCP"]}}}}, "vscode": {"servers": {"GLips-Figma-Context-MCP": {"command": "npx", "args": ["-y", "GLips-Figma-Context-MCP"]}}}, "windsurf": {"mcpServers": {"GLips-Figma-Context-MCP": {"command": "npx", "args": ["-y", "GLips-Figma-Context-MCP"]}}}}, "troubleshooting_guide": {"common_issues": [{"issue": "Server not starting", "solution": "Check if all dependencies are installed and environment variables are set correctly"}, {"issue": "Connection timeout", "solution": "Verify network connectivity and server configuration"}, {"issue": "Permission denied", "solution": "Ensure proper file permissions and authentication credentials"}], "debugging_steps": ["Check server logs for error messages", "Verify configuration file syntax", "Test with MCP inspector", "Check environment variables"], "log_locations": {"claude_desktop": "~/Library/Logs/Claude/mcp*.log", "cursor": "Check Cursor output panel", "vscode": "Check VS Code output panel"}}, "page_sections": ["overview", "installation", "configuration", "usage", "tools", "faqs", "troubleshooting"], "content_type": "mcp_server_guide", "search_intent": "informational", "license_info": {"type": "MIT License", "commercial_use": false}, "seo_title": "Framelink Figma MCP: Optimize Figma Design Metadata for AI Coding Tools - Complete MCP Server Guide | Model Context Protocol", "seo_enhanced_overview": "ai integration integration: model context protocol integration: The Framelink Figma MCP server is a cutting-edge tool designed to bridge the gap between design and development by enabling seamless access to Figma design data in an AI-assisted coding environment. Built with a focus on AI-powered coding tools, like Cursor, the server simplifies and filters Figma metadata into actionable and contextually relevant formats for improved accuracy in code suggestions. Whether you're a developer integrating Figma designs into React projects or an AI team automating workflows, the Framelink Figma MCP ensures efficient collaboration and performance optimization.", "seo_structured_data": {"@context": "https://schema.org", "@type": "SoftwareApplication", "name": "Framelink Figma MCP: Optimize Figma Design Metadata for AI Coding Tools", "description": "The Framelink Figma MCP server is a cutting-edge tool designed to bridge the gap between design and development by enabling seamless access to Figma design data in an AI-assisted coding environment. B", "applicationCategory": "DeveloperApplication", "operatingSystem": "Cross-platform", "keywords": ["mcp server", "model context protocol", "ai integration", "claude mcp"]}, "seo_open_graph": {"og:title": "Framelink Figma MCP: Optimize Figma Design Metadata for AI Coding Tools - Complete MCP Server Guide | Model Context Protocol", "og:description": "The Framelink Figma MCP server is a cutting-edge tool designed to bridge the gap between design and development by enabling seamless access to Figma design data in an AI-assisted coding environment. B", "og:type": "website", "og:site_name": "MCP Server Directory"}}}]