# Enhanced MCP Pipeline - Admin Guide (June 2024)

## 🚀 Quick Start for Administrators

### **Instant Setup (Recommended)**
```bash
# One-command startup with interactive menu
python start_enhanced_pipeline.py

# Quick start with web dashboard
python start_enhanced_pipeline.py quick
```

### **Web Dashboard Access**
- **Main Dashboard**: http://localhost:9000
- **Pipeline Management**: http://localhost:9000/pipelines
- **API Status**: http://localhost:9000/api/enhanced/status

---

## 📊 **Web Dashboard Features**

### **1. System Overview Card**
- **Total Pipelines**: Shows all active and completed pipelines
- **Running/Completed/Failed**: Real-time status counts
- **System Status Indicator**: <PERSON> (running), <PERSON> (failed), <PERSON> (completed)
- **Refresh Button**: Manual refresh of all data
- **Cleanup Button**: Remove old completed pipelines

### **2. Scraping Pipelines Card**
- **Pipeline List**: All scraping operations with progress bars
- **Progress Tracking**: X/Y tasks completed with percentage
- **Control Buttons**: 
  - ▶️ Start/Resume
  - ⏸️ Pause
  - ⏹️ Stop
- **Create New**: ➕ Button to create new scraping pipeline

### **3. Content Generation Card**
- **Generation Status**: Current content generation pipelines
- **Progress Monitoring**: Real-time completion tracking
- **Pipeline Controls**: Start, pause, resume, stop operations
- **Create New**: ➕ Button for new content generation

### **4. Progress Chart**
- **Visual Overview**: Doughnut chart showing pipeline distribution
- **Color Coding**: 
  - Green: Completed
  - Blue: Running
  - Red: Failed
  - Gray: Pending

---

## 🎮 **How to Use - Step by Step**

### **Daily Operations Workflow**

#### **Option A: Web Interface (Recommended)**
1. **Start the System**:
   ```bash
   python start_enhanced_pipeline.py quick
   ```

2. **Open Dashboard**: http://localhost:9000

3. **Create Daily Pipeline**:
   - Click "➕ New Scraping Pipeline"
   - Select "Web Scraping" type
   - Leave URLs empty (uses master data automatically)
   - Set batch size: 10-20
   - Set delay: 2.0 seconds
   - Click "Create Pipeline"

4. **Monitor Progress**:
   - Watch real-time progress bars
   - Check error statistics
   - Use pause/resume as needed

5. **Generate Content**:
   - After scraping completes, click "➕ New Content Pipeline"
   - Select "Content Generation" type
   - Set batch size: 3-5 (content generation is slower)
   - Click "Create Pipeline"

#### **Option B: Command Line Interface**
```bash
# Full pipeline (scraping + content generation)
python pipeline_runner.py --mode full

# Scraping only with custom URLs
python pipeline_runner.py --mode scrape --urls https://github.com/user/repo1 https://github.com/user/repo2

# Content generation only
python pipeline_runner.py --mode generate

# Resume from last checkpoint
python pipeline_runner.py --mode full --resume
```

---

## 🔄 **Resume & Recovery Features**

### **Automatic Resume**
The system automatically saves checkpoints every 100 operations. If interrupted:

1. **Web Interface**: 
   - Pipelines show "Resume" button
   - Click ▶️ to continue from last checkpoint

2. **Command Line**:
   ```bash
   python pipeline_runner.py --mode full --resume
   ```

### **What Gets Saved**
- ✅ **URL Processing Status**: Which URLs completed/failed
- ✅ **Content Generation Progress**: Which items were processed
- ✅ **Error Information**: Failed items for retry
- ✅ **Statistics**: Counts, timing, performance metrics

### **Recovery Scenarios**
- **Power Outage**: Resume from last checkpoint
- **Network Issues**: Automatic retry with backoff
- **System Restart**: Continue where left off
- **Manual Stop**: Resume at any time

---

## 📈 **Monitoring & Troubleshooting**

### **Real-time Monitoring**
1. **Dashboard Metrics**:
   - Progress percentages
   - Success/failure rates
   - Processing speed
   - Error categories

2. **Log Files** (in `logs/` directory):
   - `pipeline_YYYYMMDD.log`: Main operations
   - `errors_YYYYMMDD.log`: Error details

### **Common Issues & Solutions**

#### **"No URLs to Process"**
- **Cause**: Empty master data file
- **Solution**: Add URLs to `data/master_data.csv` or specify URLs manually

#### **"Redis Connection Failed"**
- **Cause**: Redis server not running
- **Solution**: System automatically falls back to CSV-only mode (still works!)

#### **"Pipeline Stuck"**
- **Cause**: Network issues or rate limiting
- **Solution**: Use pause/resume buttons or restart with `--resume`

#### **"Content Generation Fails"**
- **Cause**: No scraped data available
- **Solution**: Run scraping first, then content generation

---

## ⏰ **Automated Daily Operations**

### **Setup Daily Automation**
```bash
# Setup daily automation (runs at 2 AM)
python pipeline_runner.py --mode daily
```

### **What Daily Automation Does**
1. **Auto-discover** new URLs from sources
2. **Scrape** all URLs (new and existing)
3. **Generate** content from scraped data
4. **Cleanup** old data (30+ days)
5. **Generate** daily reports
6. **Handle** errors automatically

### **Monitoring Daily Runs**
- Check `logs/` for daily execution logs
- Review `data/reports/` for daily summaries
- Use web dashboard to see automation status

---

## 🛠️ **Advanced Configuration**

### **Performance Tuning**
Edit `config/pipeline_config.json`:

```json
{
  "batch_size": 10,           // Items per batch
  "concurrent_workers": 3,    // Parallel workers
  "delay_between_requests": 2.0, // Rate limiting
  "checkpoint_interval": 100  // Save frequency
}
```

### **Error Handling Settings**
```json
{
  "max_retries": 3,          // Retry attempts
  "retry_delay": 5,          // Delay between retries
  "error_handling": {
    "network": 5,            // Network error retries
    "rate_limit": 3,         // Rate limit retries
    "parsing": 2             // Parsing error retries
  }
}
```

---

## 📊 **Data Management**

### **Input Data**
- **Master URLs**: `data/master_data.csv`
- **Configuration**: `config/pipeline_config.json`

### **Output Data**
- **Scraped Data**: `data/completed_data.csv`
- **Generated Content**: `data/generated_content.json`
- **Pipeline States**: `data/state_*.json`
- **Error Logs**: `data/error_log.json`

### **Backup & Cleanup**
```bash
# Manual cleanup of old data
python pipeline_runner.py --mode status  # Check what's running
# Use web interface "Cleanup" button for old pipelines
```

---

## 🔧 **Troubleshooting Commands**

### **System Diagnostics**
```bash
# Check system health
python start_enhanced_pipeline.py diagnostics
```

### **Pipeline Status**
```bash
# Check all pipeline status
python pipeline_runner.py --mode status
```

### **Reset Everything**
```bash
# Stop all processes
# Delete data/state_*.json files
# Restart with fresh state
```

---

## 🎯 **Best Practices**

### **Daily Operations**
1. **Morning Check**: Review dashboard for overnight runs
2. **Monitor Progress**: Check progress bars during active runs
3. **Handle Errors**: Use pause/resume for network issues
4. **Review Logs**: Check error logs for patterns

### **Performance Optimization**
1. **Batch Size**: Start with 10, increase if stable
2. **Delay**: Use 2.0s for GitHub, adjust for other sites
3. **Workers**: 3 workers good for most systems
4. **Monitoring**: Watch memory usage during large runs

### **Error Management**
1. **Let It Retry**: System handles most errors automatically
2. **Use Resume**: Don't restart from scratch after interruptions
3. **Check Logs**: Review error patterns for systematic issues
4. **Adjust Settings**: Increase delays if hitting rate limits

---

## 📞 **Support & Maintenance**

### **Regular Maintenance**
- **Weekly**: Review error logs and cleanup old data
- **Monthly**: Check disk space and log rotation
- **Quarterly**: Update configuration based on performance

### **Getting Help**
1. **Check Logs**: `logs/` directory for detailed information
2. **Web Dashboard**: Error statistics and trends
3. **Diagnostics**: Run system diagnostics command
4. **Status Check**: Use status command for current state

### **Emergency Procedures**
1. **Stop Everything**: Ctrl+C or web interface stop buttons
2. **Check Status**: Use status command to see what's running
3. **Resume Safely**: Use resume mode to continue safely
4. **Reset if Needed**: Delete state files for fresh start

---

## 🎉 **Success Indicators**

### **Healthy System**
- ✅ Progress bars moving steadily
- ✅ Low error rates (<5%)
- ✅ Successful daily automation
- ✅ Regular content generation

### **System Working Well When**
- Pipelines complete without manual intervention
- Resume functionality works after interruptions
- Error rates remain low and stable
- Daily automation runs successfully
- Web dashboard shows green status indicators

**The enhanced pipeline system is designed to be robust and self-managing. Most operations should work automatically with minimal intervention required.**

---

## 🎬 **Real-World Usage Examples**

### **Example 1: Daily GitHub Repository Scraping**
```bash
# Morning routine (9 AM)
python start_enhanced_pipeline.py quick

# Web interface:
# 1. Open http://localhost:9000
# 2. Click "New Scraping Pipeline"
# 3. Leave URLs empty (uses master_data.csv)
# 4. Set batch size: 15
# 5. Set delay: 2.0 seconds
# 6. Click "Create Pipeline"
# 7. Monitor progress in real-time
# 8. When complete, click "New Content Pipeline"
# 9. Set batch size: 3
# 10. Click "Create Pipeline"
```

### **Example 2: Resume After Network Interruption**
```bash
# If pipeline was interrupted:
python pipeline_runner.py --mode full --resume

# Or via web interface:
# 1. Open dashboard
# 2. Find paused pipeline
# 3. Click ▶️ Resume button
# 4. Monitor progress continuation
```

### **Example 3: Process Specific URLs**
```bash
# Command line with specific URLs
python pipeline_runner.py --mode scrape \
  --urls https://github.com/microsoft/vscode \
         https://github.com/facebook/react \
         https://github.com/google/tensorflow \
  --batch-size 5 --delay 3.0

# Web interface:
# 1. Click "New Scraping Pipeline"
# 2. Paste URLs in text area (one per line)
# 3. Adjust settings
# 4. Create and monitor
```

### **Example 4: Weekend Batch Processing**
```bash
# Friday evening - setup large batch
python pipeline_runner.py --mode full \
  --batch-size 20 \
  --delay 1.5

# Monday morning - check results
python pipeline_runner.py --mode status
```

---

## 🚨 **Emergency Procedures**

### **Pipeline Stuck or Frozen**
1. **Immediate Action**:
   ```bash
   # Check what's running
   python pipeline_runner.py --mode status

   # If web interface available
   # Use Stop buttons on dashboard
   ```

2. **Force Stop**:
   ```bash
   # Kill all processes
   pkill -f "pipeline"

   # Or use Ctrl+C in terminal
   ```

3. **Safe Recovery**:
   ```bash
   # Resume from last checkpoint
   python pipeline_runner.py --mode full --resume
   ```

### **System Resource Issues**
1. **High Memory Usage**:
   - Reduce batch size in config
   - Restart with smaller batches
   - Monitor system resources

2. **Disk Space Full**:
   ```bash
   # Clean old logs
   rm logs/*.log.old

   # Clean old data
   # Use web interface Cleanup button
   ```

### **Redis Connection Issues**
- **System automatically falls back to CSV mode**
- **No action needed - pipeline continues**
- **Performance may be slower but data is safe**

---

## 📋 **Maintenance Checklists**

### **Daily Checklist**
- [ ] Check dashboard for overnight pipeline status
- [ ] Review error rates (should be <5%)
- [ ] Verify daily automation completed
- [ ] Check disk space usage
- [ ] Review any failed URLs for patterns

### **Weekly Checklist**
- [ ] Review error logs for systematic issues
- [ ] Clean up old completed pipelines
- [ ] Check performance metrics
- [ ] Verify backup data integrity
- [ ] Update master URL list if needed

### **Monthly Checklist**
- [ ] Review and optimize configuration settings
- [ ] Archive old log files
- [ ] Check system resource usage trends
- [ ] Update documentation if processes changed
- [ ] Test resume functionality

---

## 🔍 **Monitoring Dashboard Guide**

### **Understanding the Interface**

#### **System Overview Card**
- **Green Indicator**: All systems operational
- **Yellow Indicator**: Some warnings (check logs)
- **Red Indicator**: Critical issues need attention
- **Numbers**: Real-time counts of pipeline states

#### **Pipeline Cards**
- **Progress Bars**:
  - Green: Successful operations
  - Blue: Currently processing
  - Red: Failed operations
  - Gray: Pending/queued

#### **Control Buttons**
- **▶️ Start**: Begin new pipeline
- **⏸️ Pause**: Temporary stop (can resume)
- **⏹️ Stop**: Complete stop (can restart)
- **🔄 Resume**: Continue from checkpoint

#### **Progress Chart**
- **Real-time Updates**: Refreshes every 30 seconds
- **Interactive**: Click segments for details
- **Color Legend**: Matches pipeline status colors

---

## 🎯 **Performance Optimization Guide**

### **Optimal Settings by Use Case**

#### **Fast Processing (Good Network)**
```json
{
  "batch_size": 20,
  "concurrent_workers": 5,
  "delay_between_requests": 1.0,
  "checkpoint_interval": 50
}
```

#### **Stable Processing (Standard)**
```json
{
  "batch_size": 10,
  "concurrent_workers": 3,
  "delay_between_requests": 2.0,
  "checkpoint_interval": 100
}
```

#### **Conservative Processing (Slow Network)**
```json
{
  "batch_size": 5,
  "concurrent_workers": 2,
  "delay_between_requests": 5.0,
  "checkpoint_interval": 25
}
```

### **Monitoring Performance**
1. **Watch Success Rates**: Should be >95%
2. **Monitor Processing Speed**: URLs per minute
3. **Check Error Patterns**: Network vs parsing errors
4. **Resource Usage**: Memory and CPU utilization

---

## 🎓 **Training Guide for New Users**

### **First Time Setup (15 minutes)**
1. **Install and Test**:
   ```bash
   python start_enhanced_pipeline.py diagnostics
   ```

2. **Quick Demo**:
   ```bash
   python start_enhanced_pipeline.py quick
   ```

3. **Create Test Pipeline**:
   - Use 2-3 test URLs
   - Small batch size (3)
   - Monitor completion

### **Learning Path**
1. **Week 1**: Use web interface for all operations
2. **Week 2**: Try command line for specific tasks
3. **Week 3**: Setup daily automation
4. **Week 4**: Handle interruptions and resume

### **Common New User Mistakes**
- ❌ **Starting too large**: Begin with small batches
- ❌ **Ignoring errors**: Check error logs regularly
- ❌ **Not using resume**: Always resume after interruptions
- ❌ **Wrong batch sizes**: Content generation needs smaller batches

---

## 📞 **Quick Reference Commands**

### **Essential Commands**
```bash
# Start everything with menu
python start_enhanced_pipeline.py

# Quick web dashboard
python start_enhanced_pipeline.py quick

# Full pipeline with resume
python pipeline_runner.py --mode full --resume

# Check system status
python pipeline_runner.py --mode status

# System diagnostics
python start_enhanced_pipeline.py diagnostics
```

### **Web Interface URLs**
- **Main Dashboard**: http://localhost:9000
- **API Status**: http://localhost:9000/api/enhanced/status
- **Create Pipeline**: http://localhost:9000 (use buttons)

### **Important File Locations**
- **Configuration**: `config/pipeline_config.json`
- **Input URLs**: `data/master_data.csv`
- **Scraped Data**: `data/completed_data.csv`
- **Generated Content**: `data/generated_content.json`
- **Logs**: `logs/pipeline_YYYYMMDD.log`
- **Error Logs**: `logs/errors_YYYYMMDD.log`

---

## 🎉 **Success Stories & Tips**

### **What Good Performance Looks Like**
- ✅ **95%+ Success Rate**: Most URLs process successfully
- ✅ **Consistent Speed**: Steady progress without stalls
- ✅ **Low Error Rate**: <5% errors, mostly temporary network issues
- ✅ **Successful Resumes**: Interruptions don't cause data loss
- ✅ **Daily Automation**: Runs without manual intervention

### **Pro Tips from Experience**
1. **Start Small**: Test with 10-20 URLs before large batches
2. **Monitor First Hour**: Watch for patterns in first hour of operation
3. **Use Resume Liberally**: Don't restart from scratch unnecessarily
4. **Check Logs Weekly**: Spot trends before they become problems
5. **Adjust Delays**: Increase delays if seeing rate limit errors

**Remember: The system is designed to be resilient. When in doubt, use the resume feature rather than starting over!**
