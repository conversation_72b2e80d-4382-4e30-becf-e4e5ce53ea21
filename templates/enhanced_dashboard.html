<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced MCP Pipeline Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .card-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2d3748;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        
        .status-running { background-color: #48bb78; }
        .status-paused { background-color: #ed8936; }
        .status-completed { background-color: #4299e1; }
        .status-failed { background-color: #f56565; }
        .status-idle { background-color: #a0aec0; }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-label {
            color: #718096;
            font-size: 0.9rem;
        }
        
        .metric-value {
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4299e1, #48bb78);
            transition: width 0.3s ease;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background-color: #4299e1;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #3182ce;
        }
        
        .btn-secondary {
            background-color: #e2e8f0;
            color: #4a5568;
        }
        
        .btn-secondary:hover {
            background-color: #cbd5e0;
        }
        
        .btn-danger {
            background-color: #f56565;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #e53e3e;
        }
        
        .btn-success {
            background-color: #48bb78;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #38a169;
        }
        
        .chart-container {
            position: relative;
            height: 200px;
            margin-top: 15px;
        }
        
        .pipeline-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .pipeline-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            margin-bottom: 8px;
        }
        
        .pipeline-info {
            flex: 1;
        }
        
        .pipeline-name {
            font-weight: 600;
            color: #2d3748;
        }
        
        .pipeline-meta {
            font-size: 0.8rem;
            color: #718096;
            margin-top: 2px;
        }
        
        .pipeline-actions {
            display: flex;
            gap: 5px;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #2d3748;
        }
        
        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 0.9rem;
        }
        
        .form-textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 0.9rem;
            min-height: 100px;
            resize: vertical;
        }
        
        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 15px;
        }
        
        .alert-success {
            background-color: #f0fff4;
            border: 1px solid #9ae6b4;
            color: #22543d;
        }
        
        .alert-error {
            background-color: #fed7d7;
            border: 1px solid #feb2b2;
            color: #742a2a;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4299e1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .timestamp {
            font-size: 0.8rem;
            color: #718096;
            text-align: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Enhanced MCP Pipeline Dashboard</h1>
            <p>Automated Web Scraping & Content Generation with Resumability</p>
        </div>
        
        <div class="dashboard-grid">
            <!-- System Overview -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">System Overview</h3>
                    <div class="status-indicator status-running" id="system-status"></div>
                </div>
                <div class="metric">
                    <span class="metric-label">Total Pipelines</span>
                    <span class="metric-value" id="total-pipelines">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Running</span>
                    <span class="metric-value" id="running-pipelines">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Completed</span>
                    <span class="metric-value" id="completed-pipelines">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Failed</span>
                    <span class="metric-value" id="failed-pipelines">0</span>
                </div>
                <div class="controls">
                    <button class="btn btn-primary" onclick="refreshDashboard()">
                        <span id="refresh-icon">🔄</span> Refresh
                    </button>
                    <button class="btn btn-secondary" onclick="showCleanupModal()">
                        🧹 Cleanup
                    </button>
                </div>
            </div>
            
            <!-- Scraping Pipelines -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Scraping Pipelines</h3>
                    <div class="status-indicator status-idle" id="scraping-status"></div>
                </div>
                <div class="pipeline-list" id="scraping-pipelines">
                    <div class="pipeline-item">
                        <div class="pipeline-info">
                            <div class="pipeline-name">No active pipelines</div>
                            <div class="pipeline-meta">Create a new scraping pipeline to get started</div>
                        </div>
                    </div>
                </div>
                <div class="controls">
                    <button class="btn btn-success" onclick="showCreatePipelineModal('scrape')">
                        ➕ New Scraping Pipeline
                    </button>
                </div>
            </div>
            
            <!-- Content Generation Pipelines -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Content Generation</h3>
                    <div class="status-indicator status-idle" id="generation-status"></div>
                </div>
                <div class="pipeline-list" id="generation-pipelines">
                    <div class="pipeline-item">
                        <div class="pipeline-info">
                            <div class="pipeline-name">No active pipelines</div>
                            <div class="pipeline-meta">Create a new content generation pipeline</div>
                        </div>
                    </div>
                </div>
                <div class="controls">
                    <button class="btn btn-success" onclick="showCreatePipelineModal('generate')">
                        ➕ New Content Pipeline
                    </button>
                </div>
            </div>
            
            <!-- Progress Chart -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Progress Overview</h3>
                </div>
                <div class="chart-container">
                    <canvas id="progressChart"></canvas>
                </div>
            </div>
        </div>
        
        <div class="timestamp" id="last-updated">
            Last updated: Never
        </div>
    </div>
    
    <!-- Create Pipeline Modal -->
    <div id="createPipelineModal" class="modal">
        <div class="modal-content">
            <h3>Create New Pipeline</h3>
            <div id="modal-alerts"></div>
            
            <form id="createPipelineForm">
                <div class="form-group">
                    <label class="form-label">Pipeline Type</label>
                    <select class="form-input" id="pipelineType" onchange="togglePipelineFields()">
                        <option value="scrape">Web Scraping</option>
                        <option value="generate">Content Generation</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Pipeline ID (optional)</label>
                    <input type="text" class="form-input" id="pipelineId" placeholder="Auto-generated if empty">
                </div>
                
                <div class="form-group" id="urlsGroup">
                    <label class="form-label">URLs to Scrape (one per line)</label>
                    <textarea class="form-textarea" id="urls" placeholder="https://github.com/user/repo1&#10;https://github.com/user/repo2"></textarea>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Batch Size</label>
                    <input type="number" class="form-input" id="batchSize" value="10" min="1" max="50">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Delay Between Requests (seconds)</label>
                    <input type="number" class="form-input" id="delay" value="2" min="0" step="0.5">
                </div>
                
                <div class="controls">
                    <button type="submit" class="btn btn-primary">
                        <span id="create-loading" style="display: none;" class="loading"></span>
                        Create Pipeline
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('createPipelineModal')">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Cleanup Modal -->
    <div id="cleanupModal" class="modal">
        <div class="modal-content">
            <h3>Cleanup Old Pipelines</h3>
            <div id="cleanup-alerts"></div>
            
            <form id="cleanupForm">
                <div class="form-group">
                    <label class="form-label">Remove pipelines older than (days)</label>
                    <input type="number" class="form-input" id="cleanupDays" value="7" min="1">
                </div>
                
                <div class="controls">
                    <button type="submit" class="btn btn-danger">
                        <span id="cleanup-loading" style="display: none;" class="loading"></span>
                        Cleanup
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('cleanupModal')">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        let progressChart = null;
        let refreshInterval = null;
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeChart();
            refreshDashboard();
            startAutoRefresh();
        });
        
        function initializeChart() {
            const ctx = document.getElementById('progressChart').getContext('2d');
            progressChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Completed', 'Running', 'Failed', 'Pending'],
                    datasets: [{
                        data: [0, 0, 0, 0],
                        backgroundColor: ['#48bb78', '#4299e1', '#f56565', '#e2e8f0'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
        
        async function refreshDashboard() {
            const refreshIcon = document.getElementById('refresh-icon');
            refreshIcon.style.animation = 'spin 1s linear infinite';
            
            try {
                const response = await fetch('/api/enhanced/status');
                const data = await response.json();
                
                if (data.error) {
                    console.error('Dashboard error:', data.error);
                    return;
                }
                
                updateSystemOverview(data.pipelines.summary);
                updateScrapingPipelines(data.pipelines.scraping);
                updateGenerationPipelines(data.pipelines.content_generation);
                updateProgressChart(data.pipelines.summary);
                
                document.getElementById('last-updated').textContent = 
                    `Last updated: ${new Date().toLocaleTimeString()}`;
                
            } catch (error) {
                console.error('Failed to refresh dashboard:', error);
            } finally {
                refreshIcon.style.animation = '';
            }
        }
        
        function updateSystemOverview(summary) {
            document.getElementById('total-pipelines').textContent = summary.total || 0;
            document.getElementById('running-pipelines').textContent = summary.running || 0;
            document.getElementById('completed-pipelines').textContent = summary.completed || 0;
            document.getElementById('failed-pipelines').textContent = summary.failed || 0;
            
            // Update system status indicator
            const systemStatus = document.getElementById('system-status');
            if (summary.running > 0) {
                systemStatus.className = 'status-indicator status-running';
            } else if (summary.failed > 0) {
                systemStatus.className = 'status-indicator status-failed';
            } else if (summary.completed > 0) {
                systemStatus.className = 'status-indicator status-completed';
            } else {
                systemStatus.className = 'status-indicator status-idle';
            }
        }
        
        function updateScrapingPipelines(pipelines) {
            const container = document.getElementById('scraping-pipelines');
            container.innerHTML = '';
            
            if (Object.keys(pipelines).length === 0) {
                container.innerHTML = `
                    <div class="pipeline-item">
                        <div class="pipeline-info">
                            <div class="pipeline-name">No active pipelines</div>
                            <div class="pipeline-meta">Create a new scraping pipeline to get started</div>
                        </div>
                    </div>
                `;
                return;
            }
            
            Object.entries(pipelines).forEach(([id, pipeline]) => {
                const item = createPipelineItem(id, pipeline, 'scrape');
                container.appendChild(item);
            });
        }
        
        function updateGenerationPipelines(pipelines) {
            const container = document.getElementById('generation-pipelines');
            container.innerHTML = '';
            
            if (Object.keys(pipelines).length === 0) {
                container.innerHTML = `
                    <div class="pipeline-item">
                        <div class="pipeline-info">
                            <div class="pipeline-name">No active pipelines</div>
                            <div class="pipeline-meta">Create a new content generation pipeline</div>
                        </div>
                    </div>
                `;
                return;
            }
            
            Object.entries(pipelines).forEach(([id, pipeline]) => {
                const item = createPipelineItem(id, pipeline, 'generate');
                container.appendChild(item);
            });
        }
        
        function createPipelineItem(id, pipeline, type) {
            const item = document.createElement('div');
            item.className = 'pipeline-item';
            
            const progress = pipeline.progress_percentage || 0;
            const statusClass = `status-${pipeline.status}`;
            
            item.innerHTML = `
                <div class="pipeline-info">
                    <div class="pipeline-name">${id}</div>
                    <div class="pipeline-meta">
                        ${pipeline.completed_tasks || 0}/${pipeline.total_tasks || 0} tasks • 
                        ${progress.toFixed(1)}% complete
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${progress}%"></div>
                    </div>
                </div>
                <div class="pipeline-actions">
                    ${getPipelineActionButtons(id, pipeline.status, type)}
                </div>
            `;
            
            return item;
        }
        
        function getPipelineActionButtons(id, status, type) {
            if (status === 'running') {
                return `
                    <button class="btn btn-secondary" onclick="pausePipeline('${id}', '${type}')">⏸️</button>
                    <button class="btn btn-danger" onclick="stopPipeline('${id}', '${type}')">⏹️</button>
                `;
            } else if (status === 'paused') {
                return `
                    <button class="btn btn-primary" onclick="resumePipeline('${id}', '${type}')">▶️</button>
                    <button class="btn btn-danger" onclick="stopPipeline('${id}', '${type}')">⏹️</button>
                `;
            } else if (status === 'idle' || status === 'failed') {
                return `
                    <button class="btn btn-success" onclick="startPipeline('${id}', '${type}')">▶️</button>
                    <button class="btn btn-primary" onclick="resumePipeline('${id}', '${type}')">🔄</button>
                `;
            } else {
                return `<button class="btn btn-secondary" disabled>✅</button>`;
            }
        }
        
        function updateProgressChart(summary) {
            if (progressChart) {
                progressChart.data.datasets[0].data = [
                    summary.completed || 0,
                    summary.running || 0,
                    summary.failed || 0,
                    summary.paused || 0
                ];
                progressChart.update();
            }
        }
        
        // Pipeline control functions
        async function startPipeline(id, type) {
            await pipelineAction(id, 'start', type);
        }
        
        async function pausePipeline(id, type) {
            await pipelineAction(id, 'pause', type);
        }
        
        async function resumePipeline(id, type) {
            await pipelineAction(id, 'resume', type);
        }
        
        async function stopPipeline(id, type) {
            await pipelineAction(id, 'stop', type);
        }
        
        async function pipelineAction(id, action, type) {
            try {
                const response = await fetch(`/api/enhanced/pipeline/${id}/${action}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ type: type })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    refreshDashboard();
                } else {
                    alert(`Failed to ${action} pipeline: ${result.error}`);
                }
            } catch (error) {
                alert(`Error: ${error.message}`);
            }
        }
        
        // Modal functions
        function showCreatePipelineModal(type) {
            document.getElementById('pipelineType').value = type;
            togglePipelineFields();
            document.getElementById('createPipelineModal').style.display = 'block';
        }
        
        function showCleanupModal() {
            document.getElementById('cleanupModal').style.display = 'block';
        }
        
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }
        
        function togglePipelineFields() {
            const type = document.getElementById('pipelineType').value;
            const urlsGroup = document.getElementById('urlsGroup');
            
            if (type === 'scrape') {
                urlsGroup.style.display = 'block';
            } else {
                urlsGroup.style.display = 'none';
            }
        }
        
        // Form handlers
        document.getElementById('createPipelineForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const loading = document.getElementById('create-loading');
            loading.style.display = 'inline-block';
            
            try {
                const formData = {
                    type: document.getElementById('pipelineType').value,
                    pipeline_id: document.getElementById('pipelineId').value || undefined,
                    config: {
                        batch_size: parseInt(document.getElementById('batchSize').value),
                        delay: parseFloat(document.getElementById('delay').value)
                    }
                };
                
                if (formData.type === 'scrape') {
                    const urlsText = document.getElementById('urls').value.trim();
                    formData.urls = urlsText.split('\n').filter(url => url.trim());
                    
                    if (formData.urls.length === 0) {
                        throw new Error('Please provide at least one URL');
                    }
                }
                
                const response = await fetch('/api/enhanced/pipeline/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    closeModal('createPipelineModal');
                    refreshDashboard();
                    document.getElementById('createPipelineForm').reset();
                } else {
                    showAlert('modal-alerts', result.error, 'error');
                }
            } catch (error) {
                showAlert('modal-alerts', error.message, 'error');
            } finally {
                loading.style.display = 'none';
            }
        });
        
        document.getElementById('cleanupForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const loading = document.getElementById('cleanup-loading');
            loading.style.display = 'inline-block';
            
            try {
                const days = parseInt(document.getElementById('cleanupDays').value);
                
                const response = await fetch('/api/enhanced/cleanup', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ days_old: days })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showAlert('cleanup-alerts', `Cleaned up ${result.cleaned_count} old pipelines`, 'success');
                    setTimeout(() => {
                        closeModal('cleanupModal');
                        refreshDashboard();
                    }, 2000);
                } else {
                    showAlert('cleanup-alerts', result.error, 'error');
                }
            } catch (error) {
                showAlert('cleanup-alerts', error.message, 'error');
            } finally {
                loading.style.display = 'none';
            }
        });
        
        function showAlert(containerId, message, type) {
            const container = document.getElementById(containerId);
            container.innerHTML = `
                <div class="alert alert-${type}">
                    ${message}
                </div>
            `;
        }
        
        function startAutoRefresh() {
            refreshInterval = setInterval(refreshDashboard, 30000); // Refresh every 30 seconds
        }
        
        // Close modals when clicking outside
        window.addEventListener('click', function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
