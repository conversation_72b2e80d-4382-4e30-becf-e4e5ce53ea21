<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Content Management - MCP Pipeline Admin Panel</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: #f5f7fa;
        color: #333;
        line-height: 1.6;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem 2rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .header h1 {
        font-size: 1.8rem;
        font-weight: 600;
      }

      .header .subtitle {
        opacity: 0.9;
        margin-top: 0.25rem;
      }

      .nav-menu {
        display: flex;
        gap: 1rem;
        margin-top: 1rem;
      }

      .nav-link {
        color: white;
        text-decoration: none;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        background: rgba(255, 255, 255, 0.1);
        transition: background 0.3s;
      }

      .nav-link:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      .nav-link.active {
        background: rgba(255, 255, 255, 0.3);
      }

      .container {
        max-width: 1400px;
        margin: 2rem auto;
        padding: 0 1rem;
      }

      .controls {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        align-items: center;
      }

      .search-box {
        flex: 1;
        min-width: 300px;
        padding: 0.75rem;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 1rem;
      }

      .filter-select {
        padding: 0.75rem;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 1rem;
        min-width: 150px;
      }

      .btn {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 1rem;
        font-weight: 500;
        text-decoration: none;
        display: inline-block;
        text-align: center;
        transition: all 0.3s;
      }

      .btn-primary {
        background: #667eea;
        color: white;
      }

      .btn-success {
        background: #27ae60;
        color: white;
      }

      .btn-warning {
        background: #f39c12;
        color: white;
      }

      .btn-danger {
        background: #e74c3c;
        color: white;
      }

      .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      }

      .content-table {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      #contentTableContainer {
        overflow-x: auto;
        max-width: 100%;
      }

      .table-header {
        background: #f8f9fa;
        padding: 1rem;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .table-title {
        font-size: 1.2rem;
        font-weight: 600;
      }

      .table-stats {
        color: #666;
        font-size: 0.9rem;
      }

      table {
        width: 100%;
        border-collapse: collapse;
        min-width: 1400px;
      }

      th,
      td {
        padding: 0.3rem;
        text-align: left;
        border-bottom: 0.5px solid #eee;
      }

      th {
        background: #f8f9fa;
        font-weight: 500;
        color: #555;
      }

      .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.85rem;
        font-weight: 500;
      }

      .status-generated {
        background: #d4edda;
        color: #155724;
      }

      .status-scraped {
        background: #fff3cd;
        color: #856404;
      }

      .status-unknown {
        background: #f8d7da;
        color: #721c24;
      }

      .category-badge {
        background: #667eea;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
      }

      .compatibility-badges {
        display: flex;
        flex-wrap: wrap;
        gap: 0.25rem;
      }

      .compat-badge {
        background: #e9ecef;
        color: #495057;
        padding: 0.2rem 0.4rem;
        border-radius: 8px;
        font-size: 0.7rem;
        font-weight: 500;
        text-transform: capitalize;
      }

      .action-buttons {
        display: flex;
        gap: 0.5rem;
        align-items: center;
      }

      .action-buttons .btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
      }

      .stars-count {
        font-weight: 600;
        color: #f39c12;
      }

      .repo-link {
        color: #667eea;
        text-decoration: none;
      }

      .repo-link:hover {
        text-decoration: underline;
      }

      .action-buttons {
        display: flex;
        gap: 0.5rem;
      }

      .btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
      }

      .loading {
        text-align: center;
        padding: 2rem;
        color: #666;
      }

      .error {
        background: #f8d7da;
        color: #721c24;
        padding: 1rem;
        border-radius: 5px;
        margin: 1rem 0;
      }

      .checkbox {
        margin-right: 0.5rem;
      }

      .bulk-actions {
        background: #e9ecef;
        padding: 1rem;
        border-bottom: 1px solid #ddd;
        display: none;
      }

      .bulk-actions.show {
        display: block;
      }

      @media (max-width: 768px) {
        .container {
          padding: 0 0.5rem;
        }

        .controls {
          flex-direction: column;
          align-items: stretch;
        }

        .search-box,
        .filter-select {
          min-width: auto;
        }

        table {
          font-size: 0.9rem;
        }

        th,
        td {
          padding: 0.75rem 0.5rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>📝 Content Management</h1>
      <div class="subtitle">Manage scraped and generated content</div>
      <div class="nav-menu">
        <a href="/" class="nav-link">📊 Dashboard</a>
        <a href="/content" class="nav-link active">📝 Content Management</a>
        <a href="/config-generator" class="nav-link">🔧 Config Generator</a>
      </div>
    </div>

    <div class="container">
      <!-- URL Input Section -->
      <div class="controls">
        <div
          style="
            display: flex;
            gap: 1rem;
            align-items: center;
            margin-bottom: 1rem;
          "
        >
          <input
            type="text"
            class="search-box"
            id="githubUrlInput"
            placeholder="Enter GitHub URL (e.g., https://github.com/owner/repo)"
            style="flex: 2"
          />
          <button class="btn btn-success" onclick="scrapeAndGenerate()">
            🚀 Scrape & Generate
          </button>
          <button class="btn btn-warning" onclick="showBulkOperationsModal()">
            ⚙️ Bulk Operations
          </button>
        </div>

        <div
          style="display: flex; gap: 1rem; align-items: center; flex-wrap: wrap"
        >
          <input
            type="text"
            class="search-box"
            id="searchBox"
            placeholder="Search by title, repository, or author..."
            style="flex: 1; min-width: 300px"
          />
          <select class="filter-select" id="statusFilter">
            <option value="">All Status</option>
            <option value="generated">Generated</option>
            <option value="scraped">Scraped Only</option>
            <option value="unknown">Unknown</option>
          </select>
          <select class="filter-select" id="languageFilter">
            <option value="">All Languages</option>
          </select>
          <button class="btn btn-primary" onclick="refreshContent()">
            🔄 Refresh
          </button>
        </div>
      </div>

      <!-- Content Table -->
      <div class="content-table">
        <div class="table-header">
          <div class="table-title">📋 Content Items</div>
          <div class="table-stats" id="tableStats">Loading...</div>
        </div>

        <div class="bulk-actions" id="bulkActions">
          <strong>Selected: <span id="selectedCount">0</span> items</strong>
          <button class="btn btn-warning btn-sm" onclick="generateSelected()">
            🚀 Generate Selected
          </button>
          <button class="btn btn-success btn-sm" onclick="regenerateSelected()">
            🔄 Regenerate Selected
          </button>
          <button class="btn btn-secondary btn-sm" onclick="clearSelection()">
            ❌ Clear Selection
          </button>
        </div>

        <div id="contentTableContainer">
          <div class="loading">Loading content data...</div>
        </div>
      </div>
    </div>

    <script>
      let contentData = [];
      let filteredData = [];
      let selectedItems = new Set();

      // Initialize page
      document.addEventListener("DOMContentLoaded", function () {
        loadContent();
        setupEventListeners();
      });

      function setupEventListeners() {
        document
          .getElementById("searchBox")
          .addEventListener("input", filterContent);
        document
          .getElementById("statusFilter")
          .addEventListener("change", filterContent);
        document
          .getElementById("languageFilter")
          .addEventListener("change", filterContent);
      }

      async function loadContent() {
        try {
          const response = await fetch("/api/content");
          const data = await response.json();
          contentData = data.content || [];

          populateLanguageFilter();
          filterContent();
          updateStats();
        } catch (error) {
          document.getElementById("contentTableContainer").innerHTML =
            '<div class="error">Error loading content: ' +
            error.message +
            "</div>";
        }
      }

      function populateLanguageFilter() {
        const languages = [
          ...new Set(contentData.map((item) => item.language)),
        ].sort();
        const languageFilter = document.getElementById("languageFilter");

        // Clear existing options except "All Languages"
        languageFilter.innerHTML = '<option value="">All Languages</option>';

        languages.forEach((lang) => {
          if (lang && lang !== "Unknown") {
            const option = document.createElement("option");
            option.value = lang;
            option.textContent = lang;
            languageFilter.appendChild(option);
          }
        });
      }

      function filterContent() {
        const searchTerm = document
          .getElementById("searchBox")
          .value.toLowerCase();
        const statusFilter = document.getElementById("statusFilter").value;
        const languageFilter = document.getElementById("languageFilter").value;

        filteredData = contentData.filter((item) => {
          const matchesSearch =
            !searchTerm ||
            (item.title || "").toLowerCase().includes(searchTerm) ||
            (item.slug || "").toLowerCase().includes(searchTerm) ||
            (item.author || "").toLowerCase().includes(searchTerm);

          const matchesStatus =
            !statusFilter || (item.status || "unknown") === statusFilter;
          const matchesLanguage =
            !languageFilter || (item.language || "Unknown") === languageFilter;

          return matchesSearch && matchesStatus && matchesLanguage;
        });

        renderTable();
        updateStats();
      }

      function renderTable() {
        const container = document.getElementById("contentTableContainer");

        if (filteredData.length === 0) {
          container.innerHTML =
            '<div class="loading">No content items found.</div>';
          return;
        }

        // Debug: Log first item to see data structure
        console.log("First item data:", filteredData[0]);

        const tableHTML = `
                <table>
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"></th>
                            <th>Repository</th>
                            <th>Title</th>
                            <th>Language</th>
                            <th>Stars</th>
                            <th>Category</th>
                            <th>Status</th>
                            <th>Last Updated</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${filteredData
                          .map(
                            (item) => `
                            <tr>
                                <td><input type="checkbox" class="item-checkbox" value="${
                                  item.content_id || ""
                                }" onchange="updateSelection()"></td>
                                <td><a href="${
                                  item.repo_url || "#"
                                }" target="_blank" class="repo-link">${
                              item.slug || "Unknown"
                            }</a></td>
                                <td>${item.title || "Untitled"}</td>
                                <td>${item.language || "Unknown"}</td>
                                <td class="stars-count">⭐ ${
                                  item.stars || 0
                                }</td>
                                <td>
                                    <span class="category-badge">${
                                      item.category ||
                                      item.categories ||
                                      "Other"
                                    }</span>
                                </td>
                               
                                <td><span class="status-badge status-${
                                  item.status || "unknown"
                                }">${
                              item.status
                                ? item.status.charAt(0).toUpperCase() +
                                  item.status.slice(1)
                                : "Unknown"
                            }</span></td>
                                <td>${
                                  item.generated_at ||
                                  item.scraped_at ||
                                  "Never"
                                }</td>
                                 <td>
                                    <div class="action-buttons">
                                        <a href="/content/${
                                          item.content_id
                                        }" class="btn btn-primary btn-sm">👁️ View</a>
                                        ${
                                          item.has_generated
                                            ? `<button class="btn btn-success btn-sm" onclick="regenerateItem('${item.content_id}')">🔄 Regenerate</button>`
                                            : `<button class="btn btn-warning btn-sm" onclick="generateItem('${item.content_id}')">🚀 Generate</button>`
                                        }
                                    </div>
                                </td>
                              
                            </tr>
                        `
                          )
                          .join("")}
                    </tbody>
                </table>
            `;

        // Debug: Log table HTML
        //  console.log('Table HTML length:', tableHTML.length);
        console.log("Table HTML preview:", tableHTML.substring(0, 500));

        container.innerHTML = tableHTML;
      }

      function updateStats() {
        const total = filteredData.length;
        const generated = filteredData.filter(
          (item) => item.status === "generated"
        ).length;
        const scraped = filteredData.filter(
          (item) => item.status === "scraped"
        ).length;

        document.getElementById(
          "tableStats"
        ).textContent = `${total} items (${generated} generated, ${scraped} scraped only)`;
      }

      function toggleSelectAll() {
        const selectAll = document.getElementById("selectAll");
        const checkboxes = document.querySelectorAll(".item-checkbox");

        checkboxes.forEach((checkbox) => {
          checkbox.checked = selectAll.checked;
          if (selectAll.checked) {
            selectedItems.add(checkbox.value);
          } else {
            selectedItems.delete(checkbox.value);
          }
        });

        updateSelectionUI();
      }

      function updateSelection() {
        const checkboxes = document.querySelectorAll(".item-checkbox");
        selectedItems.clear();

        checkboxes.forEach((checkbox) => {
          if (checkbox.checked) {
            selectedItems.add(checkbox.value);
          }
        });

        updateSelectionUI();
      }

      function updateSelectionUI() {
        const count = selectedItems.size;
        document.getElementById("selectedCount").textContent = count;
        document
          .getElementById("bulkActions")
          .classList.toggle("show", count > 0);
      }

      function clearSelection() {
        selectedItems.clear();
        document
          .querySelectorAll(".item-checkbox")
          .forEach((cb) => (cb.checked = false));
        document.getElementById("selectAll").checked = false;
        updateSelectionUI();
      }

      async function generateItem(contentId) {
        if (
          !confirm(
            "Generate content for this repository?\n\nThis will create new content from the scraped data."
          )
        )
          return;

        // Show loading state
        const button = event.target;
        const originalText = button.textContent;
        button.textContent = "⏳ Generating...";
        button.disabled = true;

        try {
          const response = await fetch("/api/process_repositories", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              content_ids: [contentId],
              mode: "regenerate",
              batch_size: 1,
            }),
          });

          const result = await response.json();
          if (result.success) {
            if (result.background) {
              alert(
                `✅ Content generation started!\n\nRepository: ${
                  result.repositories[0]
                }\nMode: ${
                  result.mode
                }\n\nBackground processes:\n${result.background_processes
                  .map((p) => `- ${p.step}: PID ${p.process_id}`)
                  .join("\n")}\n\nCheck the dashboard logs for progress.`
              );
            } else {
              alert(
                `✅ Content generation completed!\n\nRepository: ${result.repositories[0]}\nMode: ${result.mode}`
              );
            }
            setTimeout(loadContent, 3000);
          } else {
            alert("❌ Error: " + result.error);
          }
        } catch (error) {
          alert("❌ Network Error: " + error.message);
        } finally {
          button.textContent = originalText;
          button.disabled = false;
        }
      }

      async function regenerateItem(contentId) {
        if (!confirm("Are you sure you want to regenerate this content?"))
          return;

        // Show loading state
        const button = event.target;
        const originalText = button.textContent;
        button.textContent = "⏳ Processing...";
        button.disabled = true;

        try {
          const response = await fetch("/api/regenerate_content", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              content_ids: [contentId],
              max_repos: 2,
              batch_size: 1,
            }),
          });

          const result = await response.json();
          if (result.success) {
            if (result.background) {
              alert(
                `✅ Content regeneration started in background!\n\nCommand: ${result.command_run}\nProcess ID: ${result.process_id}\n\nCheck the logs for progress updates.`
              );
            } else {
              alert(`✅ Content regeneration completed!\n\n${result.message}`);
            }
            setTimeout(loadContent, 3000); // Refresh after 3 seconds
          } else {
            alert("❌ Error: " + result.error);
          }
        } catch (error) {
          alert("❌ Network Error: " + error.message);
        } finally {
          // Restore button state
          button.textContent = originalText;
          button.disabled = false;
        }
      }

      async function generateSelected() {
        if (selectedItems.size === 0) {
          alert("Please select items to generate.");
          return;
        }

        if (
          !confirm(
            `Generate content for ${selectedItems.size} selected items?\n\nThis will create new content from scraped data and may take several minutes.`
          )
        )
          return;

        // Show loading state
        const button = event.target;
        const originalText = button.textContent;
        button.textContent = "⏳ Generating...";
        button.disabled = true;

        try {
          const response = await fetch("/api/process_repositories", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              content_ids: Array.from(selectedItems),
              mode: "regenerate",
              batch_size: Math.min(3, selectedItems.size),
              background: true,
            }),
          });

          const result = await response.json();
          if (result.success) {
            if (result.background) {
              alert(
                `✅ Bulk content generation started!\n\n${
                  result.message
                }\n\nRepositories: ${result.repositories.length}\nMode: ${
                  result.mode
                }\n\nBackground processes:\n${result.background_processes
                  .map((p) => `- ${p.step}: PID ${p.process_id}`)
                  .join(
                    "\n"
                  )}\n\nCheck the dashboard logs for progress updates.`
              );
            } else {
              alert(
                `✅ Bulk content generation completed!\n\n${result.message}`
              );
            }
            clearSelection();
            setTimeout(loadContent, 5000);
          } else {
            alert("❌ Error: " + result.error);
          }
        } catch (error) {
          alert("❌ Network Error: " + error.message);
        } finally {
          button.textContent = originalText;
          button.disabled = false;
        }
      }

      async function regenerateSelected() {
        if (selectedItems.size === 0) {
          alert("Please select items to regenerate.");
          return;
        }

        if (
          !confirm(
            `Are you sure you want to regenerate ${selectedItems.size} selected items?\n\nThis may take several minutes and will run in the background.`
          )
        )
          return;

        // Show loading state
        const button = event.target;
        const originalText = button.textContent;
        button.textContent = "⏳ Processing...";
        button.disabled = true;

        try {
          const response = await fetch("/api/regenerate_content", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              content_ids: Array.from(selectedItems),
              max_repos: selectedItems.size,
              batch_size: Math.min(3, selectedItems.size),
              background: true,
            }),
          });

          const result = await response.json();
          if (result.success) {
            if (result.background) {
              alert(
                `✅ Bulk regeneration started in background!\n\n${result.message}\n\nCommand: ${result.command_run}\nProcess ID: ${result.process_id}\n\nCheck the dashboard logs for progress updates.`
              );
            } else {
              alert(`✅ Bulk regeneration completed!\n\n${result.message}`);
            }
            clearSelection();
            setTimeout(loadContent, 5000); // Refresh after 5 seconds
          } else {
            alert("❌ Error: " + result.error);
          }
        } catch (error) {
          alert("❌ Network Error: " + error.message);
        } finally {
          // Restore button state
          button.textContent = originalText;
          button.disabled = false;
        }
      }

      function refreshContent() {
        loadContent();
      }

      async function scrapeAndGenerate() {
        const urlInput = document.getElementById("githubUrlInput");
        const githubUrl = urlInput.value.trim();

        if (!githubUrl) {
          alert("Please enter a GitHub URL");
          return;
        }

        // Validate GitHub URL
        if (!githubUrl.includes("github.com")) {
          alert(
            "Please enter a valid GitHub URL (e.g., https://github.com/owner/repo)"
          );
          return;
        }

        if (
          !confirm(
            `Scrape and generate content for:\n${githubUrl}\n\nThis will:\n1. Scrape repository data\n2. Generate comprehensive content\n3. Handle duplicates automatically\n\nProceed?`
          )
        ) {
          return;
        }

        // Show loading state
        const button = event.target;
        const originalText = button.textContent;
        button.textContent = "⏳ Processing...";
        button.disabled = true;

        try {
          const response = await fetch("/api/scrape_and_generate", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              github_urls: [githubUrl],
              batch_size: 1,
              background: true,
            }),
          });

          const result = await response.json();
          if (result.success) {
            if (result.background) {
              alert(
                `✅ Scraping and content generation started!\n\nRepository: ${githubUrl}\nMode: ${
                  result.mode
                }\n\nBackground processes:\n${result.background_processes
                  .map((p) => `- ${p.step}: PID ${p.process_id}`)
                  .join(
                    "\n"
                  )}\n\nCheck the dashboard logs for progress updates.`
              );
            } else {
              alert(
                `✅ Scraping and content generation completed!\n\nRepository: ${githubUrl}\nMode: ${result.mode}`
              );
            }
            urlInput.value = "";
            setTimeout(loadContent, 3000);
          } else {
            alert("❌ Error: " + result.error);
          }
        } catch (error) {
          alert("❌ Network Error: " + error.message);
        } finally {
          button.textContent = originalText;
          button.disabled = false;
        }
      }

      function showBulkOperationsModal() {
        const operations = [
          {
            value: "regenerate_all",
            label: "🔄 Regenerate All Content",
            desc: "Regenerate content for all existing repositories",
          },
          {
            value: "resume",
            label: "▶️ Resume Processing",
            desc: "Only process repositories without generated content",
          },
          {
            value: "fresh_start",
            label: "🆕 Fresh Start",
            desc: "Re-scrape and regenerate everything from scratch",
          },
        ];

        let modalHtml = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;" id="bulkModal">
                    <div style="background: white; padding: 2rem; border-radius: 10px; max-width: 500px; width: 90%;">
                        <h3>🔧 Bulk Operations</h3>
                        <p>Choose an operation to perform on your content pipeline:</p>
                        <div style="margin: 1rem 0;">
            `;

        operations.forEach((op) => {
          modalHtml += `
                    <div style="margin: 1rem 0; padding: 1rem; border: 1px solid #ddd; border-radius: 5px; cursor: pointer;" onclick="executeBulkOperation('${op.value}')">
                        <strong>${op.label}</strong><br>
                        <small style="color: #666;">${op.desc}</small>
                    </div>
                `;
        });

        modalHtml += `
                        </div>
                        <div style="text-align: right; margin-top: 2rem;">
                            <button class="btn btn-primary" onclick="closeBulkModal()">Cancel</button>
                        </div>
                    </div>
                </div>
            `;

        document.body.insertAdjacentHTML("beforeend", modalHtml);
      }

      function closeBulkModal() {
        const modal = document.getElementById("bulkModal");
        if (modal) {
          modal.remove();
        }
      }

      async function executeBulkOperation(operation) {
        closeBulkModal();

        const operationNames = {
          regenerate_all: "Regenerate All Content",
          resume: "Resume Processing",
          fresh_start: "Fresh Start (Re-scrape Everything)",
        };

        const operationName = operationNames[operation] || operation;

        if (
          !confirm(
            `Execute: ${operationName}\n\nThis operation will process multiple repositories and may take significant time.\n\nProceed?`
          )
        ) {
          return;
        }

        try {
          const response = await fetch("/api/bulk_operations", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              operation: operation,
              background: true,
            }),
          });

          const result = await response.json();
          if (result.success) {
            if (result.background_processes) {
              alert(
                `✅ ${operationName} started!\n\nRepositories: ${
                  result.repositories.length
                }\nMode: ${
                  result.mode
                }\n\nBackground processes:\n${result.background_processes
                  .map((p) => `- ${p.step}: PID ${p.process_id}`)
                  .join(
                    "\n"
                  )}\n\nCheck the dashboard logs for progress updates.`
              );
            } else {
              alert(
                `✅ ${operationName} completed!\n\nRepositories processed: ${result.repositories.length}`
              );
            }
            setTimeout(loadContent, 5000);
          } else {
            alert("❌ Error: " + result.error);
          }
        } catch (error) {
          alert("❌ Network Error: " + error.message);
        }
      }
    </script>
  </body>
</html>
