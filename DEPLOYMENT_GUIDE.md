# Enhanced MCP Pipeline - Deployment Guide

## 🚀 Quick Deployment

### **Option 1: Automated Setup (Recommended)**
```bash
# Clone or navigate to project directory
cd mcp_automated_pipeline

# Run automated setup
python deploy/setup.py --env development

# Start the pipeline
./deploy/scripts/start_pipeline.sh
```

### **Option 2: Docker Deployment**
```bash
# Build and start with Docker Compose
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f pipeline
```

### **Option 3: Manual Setup**
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Start the pipeline
python start_enhanced_pipeline.py quick
```

---

## 📋 Environment-Specific Deployment

### **Development Environment**
```bash
# Setup development environment
python deploy/setup.py --env development

# Configuration: config/config_development.json
# Features:
# - Debug mode enabled
# - Verbose logging
# - No authentication
# - Fast processing settings
```

### **Staging Environment**
```bash
# Setup staging environment
python deploy/setup.py --env staging

# Configuration: config/config_staging.json
# Features:
# - Production-like settings
# - Authentication enabled
# - Moderate logging
# - Performance monitoring
```

### **Production Environment**
```bash
# Setup production environment
python deploy/setup.py --env production

# Configuration: config/config_production.json
# Features:
# - Optimized performance
# - Security enabled
# - Comprehensive monitoring
# - Error handling
```

---

## 🔧 System Requirements

### **Minimum Requirements**
- **OS**: Linux, macOS, or Windows 10+
- **Python**: 3.8 or higher
- **RAM**: 2GB available
- **Disk**: 5GB free space
- **Network**: Internet connection for scraping

### **Recommended Requirements**
- **OS**: Linux (Ubuntu 20.04+ or CentOS 8+)
- **Python**: 3.9 or higher
- **RAM**: 4GB+ available
- **Disk**: 20GB+ free space
- **CPU**: 2+ cores
- **Redis**: For optimal performance

### **Optional Components**
- **Redis Server**: For enhanced performance and state management
- **Docker**: For containerized deployment
- **Nginx**: For reverse proxy in production
- **SSL Certificates**: For HTTPS in production

---

## 🐳 Docker Deployment

### **Single Container**
```bash
# Build image
docker build -t mcp-pipeline .

# Run container
docker run -d \
  --name mcp-pipeline \
  -p 9000:9000 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  -e PIPELINE_ENV=production \
  mcp-pipeline
```

### **Docker Compose (Recommended)**
```bash
# Start all services
docker-compose up -d

# Scale pipeline workers
docker-compose up -d --scale pipeline=2

# Update configuration
docker-compose restart pipeline

# Stop all services
docker-compose down
```

### **Docker Environment Variables**
```bash
# Core settings
PIPELINE_ENV=production
DATA_DIR=/app/data
LOG_LEVEL=INFO

# Redis settings
REDIS_URL=redis://redis:6379
REDIS_DB=0

# Admin panel settings
ADMIN_HOST=0.0.0.0
ADMIN_PORT=9000
SECRET_KEY=your-secret-key

# Security settings
ENABLE_SSL=false
API_KEY_REQUIRED=false
```

---

## 🔒 Production Security Setup

### **1. SSL/HTTPS Configuration**
```bash
# Generate SSL certificates (Let's Encrypt example)
sudo certbot certonly --standalone -d your-domain.com

# Update configuration
{
  "security": {
    "enable_ssl": true,
    "ssl_cert_path": "/etc/letsencrypt/live/your-domain.com/fullchain.pem",
    "ssl_key_path": "/etc/letsencrypt/live/your-domain.com/privkey.pem"
  }
}
```

### **2. Authentication Setup**
```bash
# Enable authentication in config
{
  "admin_panel": {
    "enable_authentication": true,
    "secret_key": "your-strong-secret-key-here"
  }
}
```

### **3. API Key Protection**
```bash
# Generate API keys
{
  "security": {
    "api_key_required": true,
    "api_keys": ["your-api-key-1", "your-api-key-2"]
  }
}
```

### **4. Firewall Configuration**
```bash
# Ubuntu/Debian
sudo ufw allow 22/tcp      # SSH
sudo ufw allow 9000/tcp    # Admin panel
sudo ufw allow 6379/tcp    # Redis (if external)
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=9000/tcp
sudo firewall-cmd --reload
```

---

## 🔄 Service Management

### **Systemd Service (Linux)**
```bash
# Install service
sudo cp deploy/mcp-pipeline.service /etc/systemd/system/
sudo systemctl daemon-reload

# Enable and start
sudo systemctl enable mcp-pipeline
sudo systemctl start mcp-pipeline

# Check status
sudo systemctl status mcp-pipeline

# View logs
sudo journalctl -u mcp-pipeline -f
```

### **Windows Service**
```bash
# Install as Windows service (requires additional setup)
# Use NSSM (Non-Sucking Service Manager) or similar tool
nssm install "MCP Pipeline" "C:\path\to\venv\Scripts\python.exe"
nssm set "MCP Pipeline" Arguments "C:\path\to\start_enhanced_pipeline.py quick"
nssm set "MCP Pipeline" AppDirectory "C:\path\to\project"
nssm start "MCP Pipeline"
```

---

## 📊 Monitoring and Maintenance

### **Health Checks**
```bash
# Check system status
curl http://localhost:9000/api/enhanced/status

# Check pipeline status
curl http://localhost:9000/api/enhanced/pipelines

# Check logs
curl http://localhost:9000/api/enhanced/logs?lines=100
```

### **Log Management**
```bash
# View real-time logs
tail -f logs/pipeline.log

# Search logs
grep "ERROR" logs/pipeline.log

# Rotate logs manually
python -c "from pipeline.monitoring.enhanced_logger import EnhancedLogger; logger = EnhancedLogger(); logger.cleanup_old_logs(7)"
```

### **Performance Monitoring**
```bash
# System metrics
curl http://localhost:9000/api/enhanced/metrics

# Performance report
curl http://localhost:9000/api/enhanced/performance

# Resource usage
htop  # or top on basic systems
```

---

## 🔧 Configuration Management

### **Environment Variables**
```bash
# Set environment
export PIPELINE_ENV=production
export DATA_DIR=/opt/mcp-pipeline/data
export LOG_LEVEL=INFO
export REDIS_URL=redis://localhost:6379
```

### **Configuration Updates**
```bash
# Update configuration via API
curl -X POST http://localhost:9000/api/config/update \
  -H "Content-Type: application/json" \
  -d '{"scraping": {"batch_size": 15}}'

# Reload configuration
curl -X POST http://localhost:9000/api/config/reload
```

### **Backup Configuration**
```bash
# Export current config
curl http://localhost:9000/api/config/export > config_backup.json

# Import configuration
curl -X POST http://localhost:9000/api/config/import \
  -H "Content-Type: application/json" \
  -d @config_backup.json
```

---

## 🚨 Troubleshooting

### **Common Issues**

#### **Pipeline Won't Start**
```bash
# Check Python version
python --version  # Should be 3.8+

# Check dependencies
pip list | grep -E "(redis|flask|pandas)"

# Check permissions
ls -la data/ logs/ config/

# Check ports
netstat -tlnp | grep 9000
```

#### **Redis Connection Issues**
```bash
# Check Redis status
redis-cli ping

# Check Redis configuration
redis-cli config get "*"

# Test connection
python -c "import redis; r=redis.Redis(); print(r.ping())"
```

#### **High Memory Usage**
```bash
# Reduce batch sizes in config
{
  "scraping": {"batch_size": 5},
  "content_generation": {"batch_size": 2}
}

# Enable memory monitoring
{
  "monitoring": {
    "alert_thresholds": {"memory_percent": 80.0}
  }
}
```

#### **Slow Performance**
```bash
# Increase workers (if CPU allows)
{
  "scraping": {"concurrent_workers": 4}
}

# Reduce delays
{
  "scraping": {"delay_between_requests": 1.0}
}

# Check network connectivity
ping github.com
```

### **Log Analysis**
```bash
# Find errors
grep -i error logs/pipeline.log | tail -20

# Check performance
grep "duration" logs/pipeline.log | tail -10

# Monitor progress
grep "progress" logs/pipeline.log | tail -5
```

---

## 📈 Scaling and Optimization

### **Horizontal Scaling**
```bash
# Multiple pipeline instances
docker-compose up -d --scale pipeline=3

# Load balancer configuration (Nginx example)
upstream pipeline_backend {
    server 127.0.0.1:9000;
    server 127.0.0.1:9001;
    server 127.0.0.1:9002;
}
```

### **Performance Tuning**
```bash
# Optimize for CPU-bound tasks
{
  "scraping": {
    "concurrent_workers": 4,
    "batch_size": 20
  }
}

# Optimize for memory-constrained systems
{
  "scraping": {
    "concurrent_workers": 2,
    "batch_size": 5
  }
}
```

### **Database Integration** (Future)
```bash
# Enable database support
{
  "database": {
    "enabled": true,
    "url": "postgresql://user:pass@localhost/pipeline"
  }
}
```

---

## 🎯 Best Practices

### **Security**
- Use strong secret keys in production
- Enable SSL/HTTPS for public deployments
- Regularly update dependencies
- Monitor access logs
- Use API keys for external access

### **Performance**
- Monitor system resources
- Adjust batch sizes based on available memory
- Use Redis for better performance
- Implement proper error handling
- Regular log cleanup

### **Maintenance**
- Regular backups of data and configuration
- Monitor disk space usage
- Update dependencies regularly
- Test configuration changes in staging
- Document any custom modifications

### **Monitoring**
- Set up alerts for critical metrics
- Monitor error rates and performance
- Regular health checks
- Log analysis and retention
- Performance trend analysis

---

## 📞 Support and Resources

### **Documentation**
- **Admin Guide**: `ADMIN_README_Jun24.md`
- **API Documentation**: Available at `/api/docs` endpoint
- **Configuration Reference**: `pipeline/config/config_manager.py`

### **Logs and Debugging**
- **Main Logs**: `logs/pipeline.log`
- **Error Logs**: `logs/errors.log`
- **Component Logs**: `logs/scraper.log`, `logs/generator.log`

### **Health Endpoints**
- **System Status**: `GET /api/enhanced/status`
- **Pipeline Status**: `GET /api/enhanced/pipelines`
- **Metrics**: `GET /api/enhanced/metrics`
- **Configuration**: `GET /api/config/summary`

**For additional support, check the logs and monitoring dashboards first, then refer to the troubleshooting section above.**
