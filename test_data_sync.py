#!/usr/bin/env python3
"""
Test script for Data Sync Manager
Tests duplicate detection and synchronization functionality.
"""

import sys
from pathlib import Path

# Add pipeline to path
sys.path.append(str(Path(__file__).parent))

from pipeline.core.data_sync_manager import DataSync<PERSON>anager


def test_data_sync_manager():
    """Test the data sync manager functionality"""
    print("🧪 Testing Data Sync Manager...")
    
    # Initialize data sync manager
    try:
        sync_manager = DataSyncManager()
        print("✅ Data Sync Manager initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize Data Sync Manager: {e}")
        return False
    
    # Test sync status
    try:
        sync_status = sync_manager.get_sync_status()
        print("📊 Sync Status:")
        print(f"  Redis Available: {sync_status.get('redis_available', False)}")
        print(f"  CSV Scraped Count: {sync_status.get('csv_scraped_count', 0)}")
        print(f"  CSV Content Count: {sync_status.get('csv_content_count', 0)}")
        
        if sync_status.get('redis_available'):
            print(f"  Redis Scraped Count: {sync_status.get('redis_scraped_count', 0)}")
            print(f"  Redis Content Count: {sync_status.get('redis_content_count', 0)}")
            print(f"  Sync Needed: {sync_status.get('sync_needed', False)}")
        
        print("✅ Sync status check passed")
    except Exception as e:
        print(f"❌ Sync status check failed: {e}")
        return False
    
    # Test duplicate detection
    test_url = "https://github.com/microsoft/vscode"
    
    try:
        is_scraped, scraped_data = sync_manager.is_repo_scraped(test_url)
        print(f"🔍 Duplicate Check for {test_url}:")
        print(f"  Is Scraped: {is_scraped}")
        if is_scraped and scraped_data:
            print(f"  Title: {scraped_data.get('title', 'N/A')}")
            print(f"  Language: {scraped_data.get('language', 'N/A')}")
        
        is_generated, content_data = sync_manager.is_content_generated(test_url)
        print(f"  Has Content: {is_generated}")
        if is_generated and content_data:
            print(f"  Content ID: {content_data.get('id', 'N/A')}")
        
        print("✅ Duplicate detection test passed")
    except Exception as e:
        print(f"❌ Duplicate detection test failed: {e}")
        return False
    
    # Test getting repos without content
    try:
        repos_without_content = sync_manager.get_repos_without_content()
        print(f"📝 Repositories needing content generation: {len(repos_without_content)}")
        
        if repos_without_content:
            print("  Sample repositories:")
            for i, repo in enumerate(repos_without_content[:3]):
                print(f"    {i+1}. {repo.get('repo_url', 'N/A')} - {repo.get('title', 'N/A')}")
        
        print("✅ Repos without content check passed")
    except Exception as e:
        print(f"❌ Repos without content check failed: {e}")
        return False
    
    # Test cleanup (dry run)
    try:
        cleanup_stats = sync_manager.cleanup_duplicates()
        if 'error' in cleanup_stats:
            print(f"⚠️  Cleanup returned error: {cleanup_stats['error']}")
        else:
            print("🧹 Cleanup Results:")
            print(f"  CSV duplicates removed: {cleanup_stats.get('csv_duplicates', 0)}")
            print(f"  JSON duplicates removed: {cleanup_stats.get('json_duplicates', 0)}")
        
        print("✅ Cleanup test passed")
    except Exception as e:
        print(f"❌ Cleanup test failed: {e}")
        return False
    
    print("\n🎉 All Data Sync Manager tests passed!")
    return True


def test_sample_data_operations():
    """Test with sample data operations"""
    print("\n🧪 Testing Sample Data Operations...")
    
    try:
        sync_manager = DataSyncManager()
        
        # Test adding sample scraped data
        sample_repo_url = "https://github.com/test/sample-repo"
        sample_scraped_data = {
            'slug': 'test/sample-repo',
            'title': 'Sample Test Repository',
            'description': 'A test repository for data sync testing',
            'description_short': 'Test repo',
            'language': 'Python',
            'stars': '42',
            'forks': '7',
            'author': 'test',
            'category': 'Testing'
        }
        
        # Check if already exists
        is_scraped, existing_data = sync_manager.is_repo_scraped(sample_repo_url)
        print(f"📋 Sample repo already scraped: {is_scraped}")
        
        # Update scraped data (force update for testing)
        success = sync_manager.update_scraped_data(sample_repo_url, sample_scraped_data, force_update=True)
        if success:
            print("✅ Sample scraped data updated successfully")
        else:
            print("❌ Failed to update sample scraped data")
            return False
        
        # Verify the data was stored
        is_scraped_after, scraped_data_after = sync_manager.is_repo_scraped(sample_repo_url)
        if is_scraped_after:
            print("✅ Sample scraped data verified in storage")
            print(f"  Title: {scraped_data_after.get('title', 'N/A')}")
        else:
            print("❌ Sample scraped data not found after update")
            return False
        
        # Test adding sample content data
        sample_content_data = {
            'id': 'test_content_123',
            'title': 'Sample Test Repository Content',
            'content': 'This is generated content for the sample test repository.',
            'language': 'Python',
            'github_stars': 42,
            'github_repo': 'test/sample-repo'
        }
        
        # Update content data
        success = sync_manager.update_generated_content(sample_repo_url, sample_content_data, force_update=True)
        if success:
            print("✅ Sample content data updated successfully")
        else:
            print("❌ Failed to update sample content data")
            return False
        
        # Verify the content was stored
        is_generated_after, content_data_after = sync_manager.is_content_generated(sample_repo_url)
        if is_generated_after:
            print("✅ Sample content data verified in storage")
            print(f"  Content ID: {content_data_after.get('id', 'N/A')}")
        else:
            print("❌ Sample content data not found after update")
            return False
        
        print("🎉 Sample data operations test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Sample data operations test failed: {e}")
        return False


if __name__ == '__main__':
    print("🚀 Starting Data Sync Manager Tests\n")
    
    # Run basic tests
    basic_success = test_data_sync_manager()
    
    # Run sample data tests
    sample_success = test_sample_data_operations()
    
    if basic_success and sample_success:
        print("\n✅ All tests passed! Data Sync Manager is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Check the output above for details.")
        sys.exit(1)
