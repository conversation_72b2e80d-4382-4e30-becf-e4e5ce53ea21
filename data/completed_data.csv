repo_url,slug,readme,stars,stored_at
https://github.com/ananddtyagi/webpage-screenshot-mcp,ananddtyagi/webpage-screenshot-mcp,"# Webpage Screenshot MCP Server

An MCP (Model Context Protocol) server that captures screenshots of web pages using Puppeteer. This server allows AI agents to visually verify web applications and see their progress when generating web apps.

![Screen Recording May 27 2025 (2)](https://github.com/user-attachments/assets/9f186ec4-5a5c-449b-9a30-a5ec0cdba695)


## Features

- **Full page screenshots**: Capture entire web pages or just the viewport
- **Element screenshots**: Target specific elements using CSS selectors
- **Multiple formats**: Support for PNG, JPEG, and WebP formats
- **Customizable options**: Set viewport size, image quality, wait conditions, and delays
- **Base64 encoding**: Returns screenshots as base64 encoded images for easy integration
- **Authentication support**: Manual login and cookie persistence
- **Default browser integration**: Use your system's default browser for a more natural experience
- **Session persistence**: Keep browser sessions open for multi-step workflows

## Installation

To install and build the MCP:

```bash
# Clone the repository (if you haven't already)
git clone https://github.com/ananddtyagi/webpage-screenshot-mcp.git
cd webpage-screenshot-mcp

# Install dependencies
npm install

# Build the project
npm run build
```

The MCP server is built using TypeScript and compiled to JavaScript. The `dist` folder contains the compiled JavaScript files. 

### Adding to Claude or Cursor

To add this MCP to Claude Desktop or Cursor:

1. **Claude Desktop**:
   - Go to Settings > Developer
   - Click ""Edit Config""
   - Add the following:

   ```json
    ""webpage-screenshot"": {
      ""command"": ""node"",
      ""args"": [
        ""~/path/to/webpage-screenshot-mcp/dist/index.js""
      ]
    }
   ```
   - Save and reload Claude

2. **Cursor**:
   - Open Cursor and go to Cursor Settings > MCP
   - Click ""Add new global MCP server""
   - Add the following:
  
  ```json
    ""webpage-screenshot"": {
      ""command"": ""node"",
      ""args"": [""~/path/to/webpage-screenshot-mcp/dist/index.js""]
    }
   ```

   - Save and reload Cursor

## Usage

### Tools

This MCP server provides several tools:

#### 1. login-and-wait

Opens a webpage in a visible browser window for manual login, waits for user to complete login, then saves cookies.

```json
{
  ""url"": ""https://example.com/login"",
  ""waitMinutes"": 5,
  ""successIndicator"": "".dashboard-welcome"",
  ""useDefaultBrowser"": true
}
```

- `url` (required): The URL of the login page
- `waitMinutes` (optional): Maximum minutes to wait for login (default: 5)
- `successIndicator` (optional): CSS selector or URL pattern that indicates successful login
- `useDefaultBrowser` (optional): Whether to use the system's default browser (default: true)

#### 2. screenshot-page

Captures a screenshot of a given URL and returns it as base64 encoded image.

```json
{
  ""url"": ""https://example.com/dashboard"",
  ""fullPage"": true,
  ""width"": 1920,
  ""height"": 1080,
  ""format"": ""png"",
  ""quality"": 80,
  ""waitFor"": ""networkidle2"",
  ""delay"": 500,
  ""useSavedAuth"": true,
  ""reuseAuthPage"": true,
  ""useDefaultBrowser"": true,
  ""visibleBrowser"": true
}
```

- `url` (required): The URL of the webpage to screenshot
- `fullPage` (optional): Whether to capture the full page or just the viewport (default: true)
- `width` (optional): Viewport width in pixels (default: 1920)
- `height` (optional): Viewport height in pixels (default: 1080)
- `format` (optional): Image format - ""png"", ""jpeg"", or ""webp"" (default: ""png"")
- `quality` (optional): Quality of the image (0-100), only applicable for jpeg and webp
- `waitFor` (optional): When to consider page loaded - ""load"", ""domcontentloaded"", ""networkidle0"", or ""networkidle2"" (default: ""networkidle2"")
- `delay` (optional): Additional delay in milliseconds after page load (default: 0)
- `useSavedAuth` (optional): Whether to use saved cookies from previous login (default: true)
- `reuseAuthPage` (optional): Whether to use the existing authenticated page (default: false)
- `useDefaultBrowser` (optional): Whether to use the system's default browser (default: false)
- `visibleBrowser` (optional): Whether to show the browser window (default: false)

#### 3. screenshot-element

Captures a screenshot of a specific element on a webpage using a CSS selector.

```json
{
  ""url"": ""https://example.com/dashboard"",
  ""selector"": "".user-profile"",
  ""waitForSelector"": true,
  ""format"": ""png"",
  ""quality"": 80,
  ""padding"": 10,
  ""useSavedAuth"": true,
  ""useDefaultBrowser"": true,
  ""visibleBrowser"": true
}
```

- `url` (required): The URL of the webpage
- `selector` (required): CSS selector for the element to screenshot
- `waitForSelector` (optional): Whether to wait for the selector to appear (default: true)
- `format` (optional): Image format - ""png"", ""jpeg"", or ""webp"" (default: ""png"")
- `quality` (optional): Quality of the image (0-100), only applicable for jpeg and webp
- `padding` (optional): Padding around the element in pixels (default: 0)
- `useSavedAuth` (optional): Whether to use saved cookies from previous login (default: true)
- `useDefaultBrowser` (optional): Whether to use the system's default browser (default: false)
- `visibleBrowser` (optional): Whether to show the browser window (default: false)

#### 4. clear-auth-cookies

Clears saved authentication cookies for a specific domain or all domains.

```json
{
  ""url"": ""https://example.com""
}
```

- `url` (optional): URL of the domain to clear cookies for. If not provided, clears all cookies.

## Default Browser Mode

The default browser mode allows you to use your system's regular browser (Chrome, Edge, etc.) instead of Puppeteer's bundled Chromium. This is useful for:

1. Using your existing browser sessions and extensions
2. Manually logging in to websites with your saved credentials
3. Having a more natural browsing experience for multi-step workflows
4. Testing with the same browser environment as your users

To enable default browser mode, set `useDefaultBrowser: true` and `visibleBrowser: true` in your tool parameters.

### How Default Browser Mode Works

When you enable default browser mode:

1. The tool will attempt to locate your system's default browser (Chrome, Edge, etc.)
2. It launches your browser with remote debugging enabled on a random port
3. Puppeteer connects to this browser instance instead of launching its own
4. Your existing profiles, extensions, and cookies are available during the session
5. The browser window remains visible so you can interact with it manually

This mode is particularly useful for workflows that require authentication or complex user interactions.

## Browser Persistence

The MCP server can maintain a persistent browser session across multiple tool calls:

1. When you use `login-and-wait`, the browser session is kept open
2. Subsequent calls to `screenshot-page` or `screenshot-element` with `reuseAuthPage: true` will use the same page
3. This allows for multi-step workflows without having to re-authenticate

## Cookie Management

Cookies are automatically saved for each domain you visit:

1. After using `login-and-wait`, cookies are saved to the `.mcp-screenshot-cookies` directory in your home folder
2. These cookies are automatically loaded when visiting the same domain again with `useSavedAuth: true`
3. You can clear cookies using the `clear-auth-cookies` tool

## Example Workflow: Protected Page Screenshots

Here's an example workflow for taking screenshots of pages that require authentication:

1. **Manual Login Phase**

```json
{
  ""name"": ""login-and-wait"",
  ""parameters"": {
    ""url"": ""https://example.com/login"",
    ""waitMinutes"": 3,
    ""successIndicator"": "".dashboard-welcome"",
    ""useDefaultBrowser"": true
  }
}
```

This will open your default browser with the login page. You can manually log in, and once complete (either by detecting the success indicator or after navigating away from the login page), the session cookies will be saved.

2. **Take Screenshots Using Saved Session**

```json
{
  ""name"": ""screenshot-page"",
  ""parameters"": {
    ""url"": ""https://example.com/account"",
    ""fullPage"": true,
    ""useSavedAuth"": true,
    ""reuseAuthPage"": true,
    ""useDefaultBrowser"": true,
    ""visibleBrowser"": true
  }
}
```

This will take a screenshot of the account page using your saved authentication cookies in the same browser window.

3. **Take Screenshots of Specific Elements**

```json
{
  ""name"": ""screenshot-element"",
  ""parameters"": {
    ""url"": ""https://example.com/dashboard"",
    ""selector"": "".user-profile-section"",
    ""useSavedAuth"": true,
    ""useDefaultBrowser"": true,
    ""visibleBrowser"": true
  }
}
```

4. **Clear Cookies When Done**

```json
{
  ""name"": ""clear-auth-cookies"",
  ""parameters"": {
    ""url"": ""https://example.com""
  }
}
```

This workflow allows you to interact with protected pages as if you were a regular user, completing the full authentication flow in your default browser.

## Headless vs. Visible Mode

- **Headless mode** (`visibleBrowser: false`): Faster and more suitable for automated workflows where no user interaction is needed.
- **Visible mode** (`visibleBrowser: true`): Shows the browser window, allowing for user interaction and manual verification. Required for `useDefaultBrowser: true`.

## Platform Support

The default browser detection works on:

- **macOS**: Detects Chrome, Edge, and Safari
- **Windows**: Detects Chrome and Edge via registry or common installation paths
- **Linux**: Detects Chrome and Chromium via system commands

## Troubleshooting

### Common Issues

1. **Default browser not found**: If the system can't find your default browser, it will fall back to Puppeteer's bundled Chromium.
2. **Connection issues**: If there are problems connecting to the browser's debugging port, check if another instance is already using that port.
3. **Cookie issues**: If authentication isn't working, try clearing cookies with the `clear-auth-cookies` tool.

### Debugging

The MCP server logs helpful error messages to the console when issues occur. Check these messages for troubleshooting information.
","Star
 17",2025-06-11 11:33:49.800930
https://github.com/ipfred/aiwen-mcp-server-geoip,ipfred/aiwen-mcp-server-geoip,"# 埃文IP定位 MCP Server

## 介绍
[埃文科技](https://www.ipplus360.com/) 是全球IP地址高精准实时定位技术领航者 全球网络空间地图大数据服务提供商

埃文科技IP定位API已全面兼容MCP协议

MCP Server for the Aiwen IP Location API

## 工具介绍

1. IP定位 `aiwen_ip_location`
    - 描述：IP定位 根据IP地址获取IP位置(支持城市、区县、街道三种精度)、经纬度、所属机构、运营商、精度等信息。
    - 参数： `ip`：IP地址，支持IPv4、IPv6
    - 输出：位置信息，大洲`continent`,国家`country` 所属机构`owner`,运营商`isp`,邮编`zipcode`,时区`timezone`，精度`accuracy`，国家编码`areacode`,AS号`asnumber`,纬度`lat`,经度`lng`,省份`prov`,城市`city`,区县`district`,街道级地址`address`

> 1. IP定位产品分三个定位精度：通过环境变量配置(见下方环境变量配置)
> 2. 城市级：最大定位精度返回到城市 city
> 3. 区县级：最大定位精度返回到区县 district
> 4. 街道级：最大定位精度返回到街道 address 

2. 获取当前网络IP地址和位置信息 `user_network_ip`
    - 描述：获取当前网络IP地址和IP位置信息(支持城市、区县、街道三种精度),支持IPv4和IPv6
    - 参数 无
    - 输出结果 同上

3. IP应用场景 `ip_usage_scene`
    - 描述：IP应用场景 根据IP地址获取IP应用场景 输出包括保留IP、未分配IP、组织机构、移动网络、家庭宽带、数据中心、企业专线、CDN、卫星通信、交换中心、Anycast等网络应用场景
    - 参数： `ip`：IP地址，支持IPv4、IPv6
    - 输出：应用场景 `scene`

4. IP Whois `ip_whois_info`
    - 描述：whois信息 根据IP地址查询Whois注册信息 获取IP所属网段范围、所属机构名称、技术联系人、管理员等信息
    - 参数： `ip`：IP地址，支持IPv4
    - 输出：Whois注册信息，`netname`,`status`,`owner`,`techinfo`,`admininfo`等

5. AS Whois `ip_as_mapping`
    - 描述：查询IP地址AS号(自治域号)信息
    - 参数： `ip`：IP地址，支持IPv4
    - 输出：AS 相关信息，`asname`,`asn`,`allocated`，`type`,`industry`等

6. IP宿主信息 `ip_host_info`
    - 描述：IP宿主信息 根据IP地址查询IP的自治域编号(AS Number)、AS名称、运营商、所属机构等归属属性
    - 参数： `ip`：IP地址，支持IPv4
    - 输出：AS 相关信息，`asname`,`asnumber`,`isp`，`owner`等

7. IP风险画像 `ip_risk_portrait`
    - 描述：IP风险画像 根据IP地址获取IP风险画像 识别VPN、代理、秒拨、数据中心、Tor节点、端口扫描、暴力破解等高风险行为,输出风险评分、分级结果、IP位置等信息
    - 参数：`ip`：IP地址，支持IPv4
    - 输出：风险标签`tag`, 风险等级`level`, 风险分数`score`, 位置场景信息`continent`,`country`,`prov`,`city`,`scene`等

8. IP真假人 `ip_identity_check`
    - 描述：IP真假人 根据IP地址判断访问者是否为真实用户或机器流量 返回真人概率(real_person_rate)、秒播概率(mb_rate)
    - 参数：`ip`：IP地址，支持IPv4
    - 输出：真人概率`real_person_rate`, 秒播概率`mb_rate`, 自治域编号`asnumber`,运营商`isp`

9. IPv4行业 `ip_industry_classify`
    - 描述：IPv4行业 查询IP地址行业分类
    - 参数：`ip`：IP地址，支持IPv4
    - 输出：IPv4 所属行业`industry`

> 接口的详细返回数据，参考官方的API文档

## 快速使用

### 获取API KEY
通过埃文科技官网 [获取KEY](https://mall.ipplus360.com/pros/IPVFourGeoAPI?source=mcp)

### 环境变量配置

环境变量说明：
1. **`AIWEN_API_KEY`** 产品的API KEY
2. **`IPV4_ACCURACY`**（默认值：`city`）用于配置IPv4定位精度，根据所购API版本选择一个值：  
可选值：`city`（城市级）、`district`（区级）、`street`（街道级）
3. **`IPV6_ACCURACY`**（默认值：`city`）用于配置IPv6定位精度，根据所购API版本选择一个值：  
可选值：`city`（城市级）、`district`（区级）、`street`（街道级）


### MCP HOST中配置使用
#### cursor



```json
{
    ""mcpServers"": {
        ""aiwen-iplocation"": {
            ""command"": ""npx"",
            ""args"": [
                ""-y"",
                ""aiwen-mcp-server-geoip""
            ],
            ""env"": {
                ""AIWEN_API_KEY"": ""xxxxxx"",
                ""IPV4_ACCURACY"": ""city"",
                ""IPV6_ACCURACY"": ""city"",
            }
        }
    }
}
```
#### vscode
```json
{
    ""mcpServers"": {
        ""aiwen-iplocation"": {
            ""command"": ""npx"",
            ""args"": [
                ""-y"",
                ""aiwen-mcp-server-geoip""
            ],
            ""env"": {
                ""AIWEN_API_KEY"": ""xxxxxx"",
                ""IPV4_ACCURACY"": ""city"",
                ""IPV6_ACCURACY"": ""city"",
            }
        }
    }
}
```

## 应用场景

- 需要快速查询 IP 地址地理位置的应用  
- 网络安全和访问控制 ，验证IP来源
- IP地理位置相关的数据分析，科学研究
- 获取 IP 场景数据的应用
- 获取 IP whois信息的应用
- 获取 IP AS信息的应用
- 获取 IP 宿主信息的应用
- 获取 IP 风险画像的应用
- 获取 IP 真假人信息的应用
- 获取 IP 行业信息的应用","Star
 2",2025-06-11 11:33:49.800930
https://github.com/StacklokLabs/osv-mcp,StacklokLabs/osv-mcp,"# OSV MCP Server

An MCP (Model Context Protocol) server that provides access to the [OSV (Open Source Vulnerabilities) database](https://osv.dev/).

## Overview

This project implements an SSE-based MCP server that allows LLM-powered applications to query the OSV database for vulnerability information. The server provides tools for:

1. Querying vulnerabilities for a specific package version or commit
2. Batch querying vulnerabilities for multiple packages or commits
3. Getting detailed information about a specific vulnerability by ID

## Installation

### Prerequisites

- Go 1.21 or later
- [Task](https://taskfile.dev/) (optional, for running tasks)
- [ko](https://ko.build/) (optional, for building container images)

### Building from source

```bash
# Clone the repository
git clone https://github.com/StacklokLabs/osv-mcp.git
cd osv-mcp

# Build the server
task build
```

## Usage

### Running with ToolHive (Recommended)

The easiest way to run the OSV MCP server is using [ToolHive](https://github.com/stacklok/toolhive), which provides secure, containerized deployment of MCP servers:

```bash
# Install ToolHive (if not already installed)
# See: https://github.com/stacklok/toolhive#installation

# Enable auto-discovery to automatically configure supported clients
thv config auto-discovery true

# Run the OSV MCP server (packaged as 'osv' in ToolHive)
thv run osv

# List running servers
thv list

# Get detailed information about the server
thv registry info osv
```

The server will be available to your MCP-compatible clients and can query the OSV database for vulnerability information.

### Running from Source

### Server Configuration

The server can be configured using environment variables:

- `MCP_PORT`: The port number to run the server on (default: 8080)
  - Must be a valid integer between 0 and 65535
  - If invalid or not set, the server will use port 8080

Example:
```bash
# Run on port 3000
MCP_PORT=3000 ./osv-mcp

# Run on default port 8080
./build/osv-mcp-server
```

### MCP Tools

The server provides the following MCP tools:

#### query_vulnerability

Query for vulnerabilities affecting a specific package version or commit.

**Input Schema:**
```json
{
  ""type"": ""object"",
  ""properties"": {
    ""commit"": {
      ""type"": ""string"",
      ""description"": ""The commit hash to query for. If specified, version should not be set.""
    },
    ""version"": {
      ""type"": ""string"",
      ""description"": ""The version string to query for. If specified, commit should not be set.""
    },
    ""package_name"": {
      ""type"": ""string"",
      ""description"": ""The name of the package.""
    },
    ""ecosystem"": {
      ""type"": ""string"",
      ""description"": ""The ecosystem for this package (e.g., PyPI, npm, Go).""
    },
    ""purl"": {
      ""type"": ""string"",
      ""description"": ""The package URL for this package. If purl is used, package_name and ecosystem should not be set.""
    }
  }
}
```

#### query_vulnerabilities_batch

Query for vulnerabilities affecting multiple packages or commits at once.

**Input Schema:**
```json
{
  ""type"": ""object"",
  ""properties"": {
    ""queries"": {
      ""type"": ""array"",
      ""description"": ""Array of query objects"",
      ""items"": {
        ""type"": ""object"",
        ""properties"": {
          ""commit"": {
            ""type"": ""string"",
            ""description"": ""The commit hash to query for. If specified, version should not be set.""
          },
          ""version"": {
            ""type"": ""string"",
            ""description"": ""The version string to query for. If specified, commit should not be set.""
          },
          ""package_name"": {
            ""type"": ""string"",
            ""description"": ""The name of the package.""
          },
          ""ecosystem"": {
            ""type"": ""string"",
            ""description"": ""The ecosystem for this package (e.g., PyPI, npm, Go).""
          },
          ""purl"": {
            ""type"": ""string"",
            ""description"": ""The package URL for this package. If purl is used, package_name and ecosystem should not be set.""
          }
        }
      }
    }
  },
  ""required"": [""queries""]
}
```

#### get_vulnerability

Get details for a specific vulnerability by ID.

**Input Schema:**
```json
{
  ""type"": ""object"",
  ""properties"": {
    ""id"": {
      ""type"": ""string"",
      ""description"": ""The OSV vulnerability ID""
    }
  },
  ""required"": [""id""]
}
```

## Examples

### Querying vulnerabilities for a package

```json
{
  ""package_name"": ""lodash"",
  ""ecosystem"": ""npm"",
  ""version"": ""4.17.15""
}
```

### Querying vulnerabilities for a commit

```json
{
  ""commit"": ""6879efc2c1596d11a6a6ad296f80063b558d5e0f""
}
```

### Batch querying vulnerabilities

```json
{
  ""queries"": [
    {
      ""package_name"": ""lodash"",
      ""ecosystem"": ""npm"",
      ""version"": ""4.17.15""
    },
    {
      ""package_name"": ""jinja2"",
      ""ecosystem"": ""PyPI"",
      ""version"": ""2.4.1""
    }
  ]
}
```

### Getting vulnerability details

```json
{
  ""id"": ""GHSA-vqj2-4v8m-8vrq""
}
```

## Development

### Running tests

```bash
task test
```

### Linting

```bash
task lint
```

### Formatting code

```bash
task fmt
```

## Contributing

We welcome contributions to this MCP server! If you'd like to contribute, please review
the [CONTRIBUTING guide](./CONTRIBUTING.md) for details on how to get started.

If you run into a bug or have a feature request, please
[open an issue](https://github.com/StacklokLabs/osv-mcp/issues) in the
repository or join us in the `#mcp-servers` channel on our
[community Discord server](https://discord.gg/stacklok).

## License

This project is licensed under the Apache v2 License - see the LICENSE file for details.","Star
 4",2025-06-11 11:33:49.800930
https://github.com/johannesbrandenburger/typst-mcp,johannesbrandenburger/typst-mcp,"# Typst MCP Server

Typst MCP Server is an [MCP (Model Context Protocol)](https://github.com/modelcontextprotocol) implementation that helps AI models interact with [Typst](https://github.com/typst/typst), a markup-based typesetting system. The server provides tools for converting between LaTeX and Typst, validating Typst syntax, and generating images from Typst code.

## Available Tools

>⚠️ Currently all the functionality is implemented as `tools`, because Cursor and VS Code are not able to handle the other primitives yet.

The server provides the following tools:

1. **`list_docs_chapters()`**: Lists all chapters in the Typst documentation.
   - Lets the LLM get an overview of the documentation and select a chapter to read.
   - The LLM should select the relevant chapter to read based on the task at hand.

2. **`get_docs_chapter(route)`**: Retrieves a specific chapter from the Typst documentation.
   - Based on the chapter selected by the LLM, this tool retrieves the content of the chapter.
   - Also available as `get_docs_chapters(routes: list)` for retrieving multiple chapters at once.

3. **`latex_snippet_to_typst(latex_snippet)`**: Converts LaTeX code to Typst using Pandoc.
   - LLMs are better at writing LaTeX than Typst, so this tool helps convert LaTeX code to Typst.
   - Also available as `latex_snippets_to_typst(latex_snippets: list)` for converting multiple LaTeX snippets at once.

4. **`check_if_snippet_is_valid_typst_syntax(typst_snippet)`**: Validates Typst code.
   - Before sending Typst code to the user, the LLM should check if the code is valid.
   - Also available as `check_if_snippets_are_valid_typst_syntax(typst_snippets: list)` for validating multiple Typst snippets at once.

5. **`typst_to_image(typst_snippet)`**: Renders Typst code to a PNG image.
   - Before sending complex Typst illustrations to the user, the LLM should render the code to an image and check if it looks correct.
   - Only relevant for multi modal models.

## Getting Started

- Clone this repository
  - `git clone https://github.com/johannesbrandenburger/typst-mcp.git`
- Clone the [typst repository](https://github.com/typst/typst.git)
  - `git clone https://github.com/typst/typst.git`
- Run the docs generation in the typst repository
  - `cargo run --package typst-docs -- --assets-dir ../typst-mcp/typst-docs --out-file ../typst-mcp/typst-docs/main.json`
    - Make sure to adjust the path to your local clone of the typst-mcp repository
    - This will generate the `main.json` and the assets in the `typst-docs` folder
- Install required dependencies: `uv sync` (install [uv](https://github.com/astral-sh/uv) if not already installed)
  
- Install Typst

## Running the Server

Execute the server script:

```bash
python server.py
```

Or install it in Claude Desktop with MCP:

```bash
mcp install server.py
```

Or use the new agent mode in VS Code:

[Agent mode: available to all users and supports MCP](https://code.visualstudio.com/blogs/2025/04/07/agentMode)

## JSON Schema of the Typst Documentation

>⚠️ The schema of the typst documentation is not stable and may change at any time. The schema is generated from the typst source code and is not guaranteed to be complete or correct. If the schema changes, this repository will need to be updated accordingly, so that the docs functionality works again.","Star
 38",2025-06-11 11:33:49.800930
https://github.com/VictoriaMetrics-Community/mcp-victoriametrics,VictoriaMetrics-Community/mcp-victoriametrics,"# VictoriaMetrics MCP Server

[![Latest Release](https://img.shields.io/github/v/release/VictoriaMetrics-Community/mcp-victoriametrics?sort=semver&label=&filter=!*-victorialogs&logo=github&labelColor=gray&color=gray&link=https%3A%2F%2Fgithub.com%2FVictoriaMetrics-Community%2Fmcp-victoriametrics%2Freleases%2Flatest)](https://github.com/VictoriaMetrics-Community/mcp-victoriametrics/releases)
[![smithery badge](https://smithery.ai/badge/@VictoriaMetrics-Community/mcp-victoriametrics)](https://smithery.ai/server/@VictoriaMetrics-Community/mcp-victoriametrics)
![License](https://img.shields.io/github/license/VictoriaMetrics-Community/mcp-victoriametrics?labelColor=green&label=&link=https%3A%2F%2Fgithub.com%2FVictoriaMetrics-Community%2Fmcp-victoriametrics%2Fblob%2Fmain%2FLICENSE)
![Slack](https://img.shields.io/badge/Join-4A154B?logo=slack&link=https%3A%2F%2Fslack.victoriametrics.com)
![X](https://img.shields.io/twitter/follow/VictoriaMetrics?style=flat&label=Follow&color=black&logo=x&labelColor=black&link=https%3A%2F%2Fx.com%2FVictoriaMetrics)
![Reddit](https://img.shields.io/reddit/subreddit-subscribers/VictoriaMetrics?style=flat&label=Join&labelColor=red&logoColor=white&logo=reddit&link=https%3A%2F%2Fwww.reddit.com%2Fr%2FVictoriaMetrics)

The implementation of [Model Context Protocol (MCP)](https://modelcontextprotocol.io/) server for [VictoriaMetrics](https://docs.victoriametrics.com/victoriametrics/).

This provides access to your VictoriaMetrics instance and seamless integration with [VictoriaMetrics APIs](https://docs.victoriametrics.com/victoriametrics/url-examples/) and [documentation](https://docs.victoriametrics.com/).
It can give you a comprehensive interface for monitoring, observability, and debugging tasks related to your VictoriaMetrics instances, enable advanced automation and interaction capabilities for engineers and tools.

## Features

This MCP server allows you to use almost all read-only APIs of VictoriaMetrics, i.e. all functions available in [VMUI](https://docs.victoriametrics.com/#vmui):

- Querying metrics and exploring data (even drawing graphs if your client supports it)
- Listing and exporting available metrics, labels, labels values and entire series
- Analyzing and testing your alerting and recording rules and alerts
- Showing parameters of your VictoriaMetrics instance
- Exploring cardinality of your data and metrics usage statistics
- Analyzing, tracing, prettifying and explaining your queries
- Debugging your relabeling rules, downsampling and retention policy configurations 
- Integration with [VictoriaMetrics Cloud](https://docs.victoriametrics.com/victoriametrics-cloud/)
 
In addition, the MCP server contains embedded up-to-date documentation and is able to search it without online access.

More details about the exact available tools and prompts can be found in the [Usage](#usage) section.

You can combine functionality of tools, docs search in your prompts and invent great usage scenarios for your VictoriaMetrics instance.
Just check the [Dialog example](#dialog-example) section to see how it can work.
And please note the fact that the quality of the MCP Server and its responses depends very much on the capabilities of your client and the quality of the model you are using.

You can also combine the MCP server with other observability or doc search related MCP Servers and get even more powerful results.

## Requirements

- [VictoriaMetrics](https://docs.victoriametrics.com/victoriametrics/) or [VictoriaMetrics Cloud](https://docs.victoriametrics.com/victoriametrics-cloud/) instance ([single-node](https://docs.victoriametrics.com/victoriametrics/single-server-victoriametrics/) or [cluster](https://docs.victoriametrics.com/victoriametrics/cluster-victoriametrics/))
- Go 1.24 or higher (if you want to build from source)

## Installation

### Go

```bash
go install github.com/VictoriaMetrics-Community/mcp-victoriametrics/cmd/mcp-victoriametrics@latest
```

### Binaries

Just download the latest release from [Releases](https://github.com/VictoriaMetrics-Community/mcp-victoriametrics/releases) page and put it to your PATH.

Example for Linux x86_64 (note that other architectures and platforms are also available):

```bash
latest=$(curl -s https://api.github.com/repos/VictoriaMetrics-Community/mcp-victoriametrics/releases/latest | grep 'tag_name' | cut -d\"" -f4)
wget https://github.com/VictoriaMetrics-Community/mcp-victoriametrics/releases/download/$latest/mcp-victoriametrics_Linux_x86_64.tar.gz
tar axvf mcp-victoriametrics_Linux_x86_64.tar.gz
```

### Docker

You can run the MCP Helm server using Docker. 

This is the easiest way to get started without needing to install Go or build from source.

```bash
docker run -d --name mcp-victoriametrics \
  -e MCP_SERVER_MODE=sse \
  -e VM_INSTANCE_ENTRYPOINT=https://play.victoriametrics.com \
  -e VM_INSTANCE_TYPE=cluster \
  ghcr.io/victoriametrics-community/mcp-victoriametrics
```

You should replace environment variables with your own parameters.

Note that the `MCP_SERVER_MODE=sse` flag is used to enable Server-Sent Events mode, which used by MCP clients to connect.
Alternatively, you can use `MCP_SERVER_MODE=http` to enable Streamable HTTP mode. More details about server modes can be found in the [Configuration](#configuration) section.

See available docker images in [github registry](https://github.com/orgs/VictoriaMetrics-Community/packages/container/package/mcp-victoriametrics).

Also see [Using Docker instead of binary](#using-docker-instead-of-binary) section for more details about using Docker with MCP server with clients in stdio mode.

### Source Code

For building binary from source code you can use the following approach:

- Clone repo:
  
  ```bash
  git clone https://github.com/VictoriaMetrics-Community/mcp-victoriametrics.git
  cd mcp-victoriametrics
  ```
- Build binary from cloned source code: 
  
  ```bash
  make build
  # after that you can find binary mcp-victoriametrics and copy this file to your PATH or run inplace
  ```
- Build image from cloned source code:
  
  ```bash
  docker build -t mcp-victoriametrics .
  # after that you can use docker image mcp-victoriametrics for running or pushing
  ```

### Smithery

To install VictoriaMetrics MCP Server for your client automatically via Smithery, yo can use the following commands:

```bash
# Get the list of supported MCP clients
npx -y @smithery/cli list clients
#Available clients:
#  claude
#  cline
#  windsurf
#  roocode
#  witsy
#  enconvo
#  cursor
#  vscode
#  vscode-insiders
#  boltai
#  amazon-bedrock

# Install VictoriaMetrics MCP server for your client
npx -y @smithery/cli install @VictoriaMetrics-Community/mcp-victoriametrics --client <YOUR-CLIENT-NAME>
# and follow the instructions
```

## Configuration

MCP Server for VictoriaMetrics is configured via environment variables:

| Variable                                 | Description                                                                                               | Required                               | Default | Allowed values         |
|------------------------------------------|-----------------------------------------------------------------------------------------------------------|----------------------------------------|---------|------------------------|
| `VM_INSTANCE_ENTRYPOINT` / `VMC_API_KEY` | URL to VictoriaMetrics instance                                                                           | Yes (if you don't use `VMC_API_KEY`)   | - | -                      |
| `VM_INSTANCE_TYPE`                       | Type of VictoriaMetrics instance                                                                          | Yes (if you don't use ``VMC_API_KEY``) | - | `single`, `cluster`    |
| `VM_INSTANCE_BEARER_TOKEN`               | Authentication token for VictoriaMetrics API                                                              | No                                     | - | -                      |
| `VMC_API_KEY`                            | [API key from VictoriaMetrics Cloud Console](https://docs.victoriametrics.com/victoriametrics-cloud/api/) | No                                     | - | -                      |
| `MCP_SERVER_MODE`                        | Server operation mode                                                                                     | No                                     | `stdio` | `stdio`, `sse`, `http` |
| `MCP_LISTEN_ADDR`                        | Address for SSE or HTTP server to listen on                                                               | No                                     | `localhost:8080` | -                      |
| `MCP_DISABLED_TOOLS`                     | Comma-separated list of tools to disable                                                                  | No                                     | - | -                      |

You can use two options to connect to your VictoriaMetrics instance:

- Using `VM_INSTANCE_ENTRYPOINT` + `VM_INSTANCE_TYPE` + `VM_INSTANCE_BEARER_TOKEN` (optional) environment variables to connect to any single-node or cluster instance of VictoriaMetrics.
- Using `VMC_API_KEY` environment variable to work with your [VictoriaMetrics Cloud](https://victoriametrics.com/products/cloud/) instances.

### Сonfiguration examples

```bash
# For a single-node instance
export VM_INSTANCE_ENTRYPOINT=""http://localhost:8428""
export VM_INSTANCE_TYPE=""single""
export VM_INSTANCE_BEARER_TOKEN=""your-token""

# For a cluster
export VM_INSTANCE_ENTRYPOINT=""https://play.victoriametrics.com""
export VM_INSTANCE_TYPE=""cluster""
export MCP_DISABLED_TOOLS=""export,metric_statistics,test_rules"" # disable export, statistics and rules unit test tools

# For VictoriaMetrics Cloud
export VMC_API_KEY=""<you-api-key>""

# Server mode
export MCP_SERVER_MODE=""sse""
export MCP_LISTEN_ADDR=""0.0.0.0:8080""
```

## Setup in clients

### Cursor

Go to: `Settings` -> `Cursor Settings` -> `MCP` -> `Add new global MCP server` and paste the following configuration into your Cursor `~/.cursor/mcp.json` file:

```json
{
  ""mcpServers"": {
    ""victoriametrics"": {
      ""command"": ""/path/to/mcp-victoriametrics"",
      ""env"": {
        ""VM_INSTANCE_ENTRYPOINT"": ""<YOUR_VM_INSTANCE>"",
        ""VM_INSTANCE_TYPE"": ""<YOUR_VM_INSTANCE_TYPE>"",
        ""VM_INSTANCE_BEARER_TOKEN"": ""<YOUR_VM_BEARER_TOKEN>""
      }
    }
  }
}
```

See [Cursor MCP docs](https://docs.cursor.com/context/model-context-protocol) for more info.

### Claude Desktop

Add this to your Claude Desktop `claude_desktop_config.json` file (you can find it if open `Settings` -> `Developer` -> `Edit config`):

```json
{
  ""mcpServers"": {
    ""victoriametrics"": {
      ""command"": ""/path/to/mcp-victoriametrics"",
      ""env"": {
        ""VM_INSTANCE_ENTRYPOINT"": ""<YOUR_VM_INSTANCE>"",
        ""VM_INSTANCE_TYPE"": ""<YOUR_VM_INSTANCE_TYPE>"",
        ""VM_INSTANCE_BEARER_TOKEN"": ""<YOUR_VM_BEARER_TOKEN>""
      }
    }
  }
}
```

See [Claude Desktop MCP docs](https://modelcontextprotocol.io/quickstart/user) for more info.

### Claude Code

Run the command:

```sh
claude mcp add victoriametrics -- /path/to/mcp-victoriametrics \
  -e VM_INSTANCE_ENTRYPOINT=<YOUR_VM_INSTANCE> \
  -e VM_INSTANCE_TYPE=<YOUR_VM_INSTANCE_TYPE>
  -e VM_INSTANCE_BEARER_TOKEN=<YOUR_VM_BEARER_TOKEN>
```

See [Claude Code MCP docs](https://docs.anthropic.com/en/docs/agents-and-tools/claude-code/tutorials#set-up-model-context-protocol-mcp) for more info.

### Visual Studio Code

Add this to your VS Code MCP config file:

```json
{
  ""servers"": {
    ""victoriametrics"": {
      ""type"": ""stdio"",
      ""command"": ""/path/to/mcp-victoriametrics"",
      ""env"": {
        ""VM_INSTANCE_ENTRYPOINT"": ""<YOUR_VM_INSTANCE>"",
        ""VM_INSTANCE_TYPE"": ""<YOUR_VM_INSTANCE_TYPE>"",
        ""VM_INSTANCE_BEARER_TOKEN"": ""<YOUR_VM_BEARER_TOKEN>""
      }
    }
  }
}
```

See [VS Code MCP docs](https://code.visualstudio.com/docs/copilot/chat/mcp-servers) for more info.

### Zed

Add the following to your Zed config file:

```json
  ""context_servers"": {
    ""victoriametrics"": {
      ""command"": {
        ""path"": ""/path/to/mcp-victoriametrics"",
        ""args"": [],
        ""env"": {
          ""VM_INSTANCE_ENTRYPOINT"": ""<YOUR_VM_INSTANCE>"",
          ""VM_INSTANCE_TYPE"": ""<YOUR_VM_INSTANCE_TYPE>"",
          ""VM_INSTANCE_BEARER_TOKEN"": ""<YOUR_VM_BEARER_TOKEN>""
        }
      },
      ""settings"": {}
    }
  }
```

See [Zed MCP docs](https://zed.dev/docs/ai/mcp) for more info.

### JetBrains IDEs

- Open `Settings` -> `Tools` -> `AI Assistant` -> `Model Context Protocol (MCP)`.
- Click `Add (+)`
- Select `As JSON`
- Put the following to the input field:

```json
{
  ""mcpServers"": {
    ""victoriametrics"": {
      ""command"": ""/path/to/mcp-victoriametrics"",
      ""env"": {
        ""VM_INSTANCE_ENTRYPOINT"": ""<YOUR_VM_INSTANCE>"",
        ""VM_INSTANCE_TYPE"": ""<YOUR_VM_INSTANCE_TYPE>"",
        ""VM_INSTANCE_BEARER_TOKEN"": ""<YOUR_VM_BEARER_TOKEN>""
      }
    }
  }
}
```

### Windsurf

Add the following to your Windsurf MCP config file.

```json
{
  ""mcpServers"": {
    ""victoriametrics"": {
      ""command"": ""/path/to/mcp-victoriametrics"",
      ""env"": {
        ""VM_INSTANCE_ENTRYPOINT"": ""<YOUR_VM_INSTANCE>"",
        ""VM_INSTANCE_TYPE"": ""<YOUR_VM_INSTANCE_TYPE>"",
        ""VM_INSTANCE_BEARER_TOKEN"": ""<YOUR_VM_BEARER_TOKEN>""
      }
    }
  }
}
```

See [Windsurf MCP docs](https://docs.windsurf.com/windsurf/mcp) for more info.

### Using Docker instead of binary

### Run with docker

You can run the MCP Helm server using Docker instead of local binary.

You should replace run command in configuration examples above in the following way:

```
{
  ""mcpServers"": {
    ""victoriametrics"": {
      ""command"": ""docker"",
        ""args"": [
          ""run"",
          ""-i"", ""--rm"",
          ""-e"", ""VM_INSTANCE_ENTRYPOINT"",
          ""-e"", ""VM_INSTANCE_TYPE"",
          ""-e"", ""VM_INSTANCE_BEARER_TOKEN"",
          ""ghcr.io/victoriametrics-community/mcp-victoriametrics"",
        ],
      ""env"": {
        ""VM_INSTANCE_ENTRYPOINT"": ""<YOUR_VM_INSTANCE>"",
        ""VM_INSTANCE_TYPE"": ""<YOUR_VM_INSTANCE_TYPE>"",
        ""VM_INSTANCE_BEARER_TOKEN"": ""<YOUR_VM_BEARER_TOKEN>""
      }
    }
  }
}
```

## Usage

After [installing](#installation) and [configuring](#setup-in-clients) the MCP server, you can start using it with your favorite MCP client.

You can start dialog with AI assistant from the phrase:

```
Use MCP VictoriaMetrics in the following answers
```

But it's not required, you can just start asking questions and the assistant will automatically use the tools and documentation to provide you with the best answers.
Just take a look into [Dialog example](#dialog-example) section for better understanding what you can do with it.

### Toolset

MCP VictoriaMetrics provides numerous tools for interacting with your VictoriaMetrics instance.

Here's a list of common available tools:

| Tool | Description                                               |
|------|-----------------------------------------------------------|
| `query` | Execute instant PromQL/MetricsQL queries                  |
| `query_range` | Execute range PromQL/MetricsQL queries over a time period |
| `metrics` | List available metrics                                    |
| `labels` | List available label names                                |
| `label_values` | List values for a specific label                          |
| `series` | List available time series                                |
| `export` | Export raw time series data to JSON or CSV                |
| `rules` | View alerting and recording rules                         |
| `alerts` | View current alerts (firing and pending)                  |
| `flags` | View non-default flags of the VictoriaMetrics instance    |
| `metric_statistics` | Get metrics usage (in queries) statistics                 |
| `active_queries` | View currently executing queries                          |
| `top_queries` | View most frequent or slowest queries                     |
| `tsdb_status` | View TSDB cardinality statistics                          |
| `tenants` | List available tenants in multi-tenant cluster setup      |
| `documentation` | Search in embedded VictoriaMetrics documentation          |
| `metric_relabel_debug` | Debug Prometheus-compatible relabeling rules              |
| `downsampling_filters_debug` | Debug downsampling configuration                          |
| `retention_filters_debug` | Debug retention filters configuration                     |
| `prettify_query` | Prettify and format PromQL/MetricsQL queries              |
| `explain_query` | Parse PromQL/MetricsQL queries and explain how it works   |
| `test_rules` | Unit-test alerting and recording rules using vmalert tool |

Here are some additional tools that are available for [VictoriaMetrics Cloud](https://docs.victoriametrics.com/victoriametrics-cloud/) (with specifying `VMC_API_KEY` parameter) users:

| Tool              | Description                                                      |
|-------------------|------------------------------------------------------------------|
| `deployments`     | List available deployments in VictoriaMetrics Cloud              |
| `cloud_providers` | List available cloud providers in VictoriaMetrics Cloud          |
| `regions`         | List available cloud providers regions in VictoriaMetrics Cloud  |
| `tiers`           | List available deployment tiers in VictoriaMetrics Cloud         |
| `access_tokens`   | List available deployment access tokens in VictoriaMetrics Cloud |
| `rule_filenames`  | List available alerting and recording rule filenames in VictoriaMetrics Cloud |
| `rule_file`       | Content of a specific alerting or recording rule file in VictoriaMetrics Cloud |

### Prompts

The server includes pre-defined prompts for common tasks.

These are just examples at the moment, the prompt library will be added to in the future:

| Prompt | Description |
|--------|-------------|
| `unused_metrics` | Find metrics that are never queried and create relabel configs to stop ingesting them |
| `documentation` | Search VictoriaMetrics documentation for specific topics |
| `rarely_used_metrics_with_high_cardinality` | Identify high-cardinality metrics that are rarely queried |

## Dialog example

This is just example of dialog with Assistant using MCP Server that can help to understand how it can work (but's it's real dialog with using the Victoria Metrics MCP server with Claude Desktop and configured to [public playground instance](https://play.victoriametrics.com)):

- > **You**: What tenants are available in my VM?
- > **Assistant** *In your VictoriaMetrics instance, the following tenants are available `0:0` (default tenant), `1:0`, `10:0`, `100:0`*
- > **You**: From now on, please use only default tenant
- > **Assistant**: *I understand. From now on, I'll use only the default tenant (0:0) for all operations with your VictoriaMetrics instance.*
- > **You**: Which metrics do we have?
- > **Assistant**: *There are many metrics available in your VictoriaMetrics instance. The list includes metrics from various systems and components, such as:*
  >
  > - *System metrics (`node_`, `machine_`, etc.),*
  > - *Kubernetes metrics (`kube_`, `container_`, `apiserver_`, etc.),*
  > - *VictoriaMetrics-specific metrics (`vm_`, `vmagent_`, `vmalert_`, etc.),*
  > - *Go runtime metrics (`go_`),*
  > - *Prometheus-related metrics (`scrape_`, `promhttp_`),*
  > - *Various application metrics (`argocd_`, `alertmanager_`).*
  >
  > *This is a comprehensive list of over 2000 metrics that are available for querying. Is there a specific type of metric you're interested in exploring further?*
- > **You**: Ok, let's work with kubernetes metrics from now on. Please show me which of these metrics never queried?
- > **Assistant**: *Based on the results, I can see that there are two Kubernetes metrics that have never been queried (query count is 0):*
  >
  > - *`kube_persistentvolumeclaim_deletion_timestamp` - This metric provides information about when PersistentVolumeClaims were marked for deletion.*
  > - *`kube_pod_init_container_status_last_terminated_reason` - This metric shows the reason why init containers in pods were last terminated.*
  >
  > *These metrics might be valuable for monitoring and troubleshooting purposes:*
  >
  > - *The PVC deletion timestamp metric could help track deletion patterns or identify stuck PVCs.*
  > - *The init container termination reason metric could help identify common failure patterns in your initialization containers.*
  >
  > *Would you like me to explain more about what these metrics track or suggest some useful queries to start monitoring them?*
- > **You**: Yes, please. And also, can you create relabeling rules to stop ingesting these metrics?
- > **Assistant**: *I'll create a relabel configuration to exclude these never-queried metrics from collection. This will help reduce storage and processing overhead. Here's the relabel configuration you can add to your VictoriaMetrics scrape configuration:*
  >
  > ```yaml
  > relabel_configs:
  > - action: drop
  >   source_labels: [__name__]
  >   regex: kube_persistentvolumeclaim_deletion_timestamp|kube_pod_init_container_status_last_terminated_reason
  > ```
  >
  > *This configuration will:*
  > 1. *Look at each metric's name (the `__name__` label)*
  > 2. *Check if it matches either of the specified metrics*
  > 3. *Drop the metrics that match, preventing them from being collected and stored*
  >
  > *You can add this to your VictoriaMetrics configuration file or to the relevant scrape job configurations in your Prometheus configuration if you're using Prometheus as a scraper for VictoriaMetrics.*
  > *If you want to test this relabel configuration before implementing it, you can use the metric relabel debug tool to see how it would affect your metrics.*
- > **You**: Yes, please debug this relabel config on real time series to see how it would affect my metrics
- > **Assistant**: *Perfect! The relabel config works as expected:*
  >
  > - *The first two test cases show that both `kube_persistentvolumeclaim_deletion_timestamp` and `kube_pod_init_container_status_last_terminated_reason` metrics are correctly dropped (resulting labels are empty `{}`).*
  > - *The third test case confirms that other metrics like `kube_pod_status_phase` are preserved and not affected by our drop rule.*
  >
  > *You can confidently add this relabel configuration to your VictoriaMetrics or Prometheus configuration to stop collecting these unused metrics. This will save storage space and reduce processing overhead without affecting your current monitoring capabilities.*
- > **You**: But i'm using vmagent resource of VictoriaMetrics operator, could you please give me part of specification for my agent with this relabel config?
- > **Assistant**: *You're right, since you're using the VictoriaMetrics Operator and the VMAgent custom resource, the configuration needs to be in the appropriate format for that CR. Here's how you can add the relabel configuration to your VMAgent specification:*
  >
  > ```yaml
  > apiVersion: operator.victoriametrics.com/v1beta1
  > kind: VMAgent
  > metadata:
  > name: your-vmagent-name
  > spec:
  >   # ... your existing configuration ...
  >   
  >   # Add this section to configure global metric relabeling
  >   inlineRelabelConfig:
  >     - action: drop
  >       sourceLabels: [""__name__""]
  >       regex: kube_persistentvolumeclaim_deletion_timestamp|kube_pod_init_container_status_last_terminated_reason
  >   
  >   # ... rest of your configuration ...
  > ```
  >
  > *Key points:*
  >
  > - *In the VMAgent spec, you use `inlineRelabelConfig` for inline relabeling rules*
  > - *The field is called `sourceLabels` (not `source_labels` as in Prometheus config)*
  > - *This is a global relabeling that will be applied to all metrics before they're stored*
- > **You**: I decided not to remove those metrics, but just lower the sample rate for them using Downsampling. What downsampling parameters i have now?
- > **Assistant**: *I can see that you currently have the following downsampling configuration:*
  >
  > ```
  > -downsampling.period=""7d:1m,30d:5m,180d:30m""
  > ```
  >
  > *This means:*
  >
  > - *Data newer than 7 days is kept at its original resolution*
  > - *Data between 7-30 days old is downsampled to 1-minute resolution*
  > - *Data between 30-180 days old is downsampled to 5-minute resolution*
  > - *Data older than 180 days is downsampled to 30-minute resolution*
  >
  > *Now, if you want to add specific downsampling rules for those rarely-queried metrics, you can use the downsampling filters debug tool to test how new rules would work. Let's test a configuration where we apply more aggressive downsampling to those specific metrics:*
  >
  > *...getting series and using downsampling filter debugger...*
  >
  > *Great! Now our test is working correctly. Based on the current configuration and our testing, here's a suggested downsampling configuration for your VMAgent CR that would apply more aggressive downsampling to those rarely-queried metrics:*
  >
  > ```yaml
  > apiVersion: operator.victoriametrics.com/v1beta1
  > kind: VMCluster
  > metadata:
  >   name: monitoring-vm-cluster
  > spec:
  >   # ... your existing configuration ...
  >   
  >   # Update the vmstorage section with the new downsampling parameters
  >   vmstorage:
  >     extraArgs:
  >       downsampling.period: '{__name__=\""kube_persistentvolumeclaim_deletion_timestamp\""}:1d:10m,{__name__=\""kube_pod_init_container_status_last_terminated_reason\""}:1d:10m,7d:1m,30d:5m,180d:30m'
  >   
  >   # ... rest of your configuration ...
  > ``` 
  >
  > *This configuration:*
  >
  > *- Applies 10-minute resolution downsampling for the rarely-queried metrics after just 1 day*
  > *- Maintains your existing downsampling policy for all other metrics*
  >
  > *The order of the downsampling rules is important - more specific rules should be listed first.*

During this dialog, the assistant was using the following tools:

- `tenants` to get the list of available tenants
- `documentation` to get information about functionality and data formats
- `metrics` to get the list of available metrics
- `metrics_staistics` to get the information about metrics usage
- `series` to get the time series for debugging
- `metric_relabel_debug` to debug relabeling rules
- `flags` to get the information about instance parameters
- `downsampling_filters_debug` to debug downsampling configuration

But you can use any other tools and combine them in your own way.

## Roadmap

- [x] Support ""Prettify query"" tool (done in [`v0.0.5`](https://github.com/VictoriaMetrics-Community/mcp-victoriametrics/releases/tag/v0.0.5))
- [x] Support ""Explain query"" tool (done in [`v0.0.6`](https://github.com/VictoriaMetrics-Community/mcp-victoriametrics/releases/tag/v0.0.6))
- [x] Support CI pipeline for building and pushing multiarch docker images (done in [`v1.0.0`](https://github.com/VictoriaMetrics-Community/mcp-victoriametrics/releases/tag/v1.0.0))
- [ ] Support tool for analysis of [Query execution statistics](https://docs.victoriametrics.com/victoriametrics/query-stats/)
- [x] Support tool for [unit-testing of alerting and recording rules](https://docs.victoriametrics.com/victoriametrics/vmalert-tool/) (done in [`v0.0.7`](https://github.com/VictoriaMetrics-Community/mcp-victoriametrics/releases/tag/v0.0.7))
- [x] Support optional integration with [VictoriaMetrics Cloud](https://victoriametrics.com/products/cloud/) (via [API keys](https://docs.victoriametrics.com/victoriametrics-cloud/api/)) (done in [`v0.0.9`](https://github.com/VictoriaMetrics-Community/mcp-victoriametrics/releases/tag/v0.0.9))
- [ ] Add some extra knowledge to server in addition to current documentation tool:
  - [x] [VictoriaMetrics blog](https://victoriametrics.com/blog/) posts (done in [`v1.1.0`](https://github.com/VictoriaMetrics-Community/mcp-victoriametrics/releases/tag/v1.1.0))
  - [ ] Github issues
  - [ ] Public slack chat history
  - [ ] CRD schemas
  - [ ] Alerting and recording rule sets
- [ ] Implement multitenant version of MCP (that will support several deployments)
- [ ] Add flags/configs validation tool
- [ ] Support tools for vmagent API
- [ ] Support [new vmalert API](https://github.com/VictoriaMetrics/VictoriaMetrics/pull/9046/files)
- [x] Enabling/disabling tools via configuration (done in [`v0.0.8`](https://github.com/VictoriaMetrics-Community/mcp-victoriametrics/releases/tag/v0.0.8))
- [ ] Tools for Alertmanager APIs [#6](https://github.com/VictoriaMetrics-Community/mcp-victoriametrics/issues/6)
- [ ] Support for [metrics metadata](https://github.com/VictoriaMetrics/VictoriaMetrics/issues/2974) in case of implementation in VictoriaMetrics

## Disclaimer

AI services and agents along with MCP servers like this cannot guarantee the accuracy, completeness and reliability of results.
You should double check the results obtained with AI.

The quality of the MCP Server and its responses depends very much on the capabilities of your client and the quality of the model you are using.

## Contributing

Contributions to the MCP VictoriaMetrics project are welcome! 

Please feel free to submit issues, feature requests, or pull requests.
","Star
 34",2025-06-11 11:33:49.800930
https://github.com/MarketplaceAdPros/amazon-ads-mcp-server,MarketplaceAdPros/amazon-ads-mcp-server,"# amazon-ads-mcp-server

Connect to your Amazon Advertising Data by integrating your account with [MarketplaceAdPros](https://marketplaceadpros.com).

Provides access to:

- Advertising Resources in Sponsored Products, Sponsored Brands and Sponsored Display, like Campaigns, Ad Groups, Keywords, Product Ads, Targeting
- Reports and ability to query them with plain english.
- Marketplace Ad Pros Recommendations, Experiments and more with purchased subscription plan

Also available as a Streamable HTTP MCP Server by connecting to `https://app.marketplaceadpros.com/mcp`

## Installation

To add the amazon-ads-mcp-server to your MCP client of choice, add the following to the server config:

On MacOS: `~/Library/Application Support/Claude/claude_desktop_config.json`

On Windows: `%APPDATA%/Claude/claude_desktop_config.json`

### Env Vars

- `BEARER_TOKEN`: The Bearer token you got from MarketplaceAdPros.com


### Configuration

You can use it via `npx` in your Claude Desktop configuration like this:

```json
{
  ""mcpServers"": {
    ""marketplaceadpros"": {
      ""command"": ""npx"",
      ""args"": [
        ""@marketplaceadpros/amazon-ads-mcp-server""
      ],
      ""env"": {
        ""BEARER_TOKEN"": ""abcdefghijklmnop""
      }
    }
  }
}
```


Or, if you clone the repo, you can build and use in your Claude Desktop configuration like this:


```json

{
  ""mcpServers"": {
    ""marketplaceadpros"": {
      ""command"": ""node"",
      ""args"": [
        ""/path/to/amazon-ads-mcp-server/build/index.js""
      ],
      ""env"": {
        ""BEARER_TOKEN"": ""abcdefghijklmnop""
      }
    }
  }
}
```


Or, if your client supports the Streamable HTTP MCP Servers, you can just point to the MCP endpoint at `https://app.marketplaceadpros.com/mcp`. 


```json

{
  ""mcpServers"": {
    ""marketplaceadpros"": {
      ""type"": ""streamable-http"",
      ""url"": ""https://app.marketplaceadpros.com/mcp""
    }
  }
}
```


Or, configure in [LibreChat](https://www.librechat.ai/) like:
```yaml
  MAP:
    type: streamable-http
    url: https://app.marketplaceadpros.com/mcp
    headers:
      Authorization: ""Bearer abcdefghijklmnop""
````


## Development

Install dependencies:
```bash
npm install
```

Build the server:
```bash
npm run build
```

For development with auto-rebuild:
```bash
npm run watch
```

### Debugging

Since MCP servers communicate over stdio, debugging can be challenging. We recommend using the [MCP Inspector](https://github.com/modelcontextprotocol/inspector), which is available as a package script:

```bash
npm run inspector
```

![amazon-ads-mcp-server live in inspector](img/inspector.png)

The Inspector will provide a URL to access debugging tools in your browser.

### Acknowledgements

- Obviously the modelcontextprotocol and Anthropic teams for the MCP Specification. [https://modelcontextprotocol.io/introduction](https://modelcontextprotocol.io/introduction)
- [MarketplaceAdPros](https://marketplaceadpros.com?ref=github-amazon-ads-mcp-server) for enabling and sponsoring this project.
","Star
 1",2025-06-11 11:33:49.800930
https://github.com/jsdelivr/globalping-mcp-server,jsdelivr/globalping-mcp-server,"# Globalping MCP Server

<p align=""center"">
  <img src=""https://raw.githubusercontent.com/jsdelivr/globalping-media/refs/heads/master/logo/full_colored_dark.svg"" alt=""Globalping Logo"" width=""180""/>
</p>

<p align=""center"">
  <b>Enable AI models to interact with a global network measurement platform through natural language. Give network access to any LLM.</b>
</p>

<p align=""center"">
  <a href=""https://github.com/modelcontextprotocol/modelcontextprotocol"">
    <img src=""https://img.shields.io/badge/MCP-compatible-brightgreen.svg"" alt=""MCP Compatible"">
  </a>
</p>


## What is Globalping?

[Globalping](https://globalping.io) is a free, public API that provides access to a globally distributed network of probes for monitoring, debugging, and benchmarking internet infrastructure. With Globalping, you can run network tests (ping, traceroute, DNS, MTR, HTTP) from thousands of locations worldwide.


## What is the Globalping MCP Server?

The Globalping MCP Server implements the [Model Context Protocol (MCP)](https://modelcontextprotocol.io), allowing AI models like OpenAI's GPT and Anthropic's Claude to interact with Globalping's network measurement capabilities through natural language.

It also supports oAuth authentication, which offers a secure way to interact with our API and benefits from higher rate limits associated with your account.

### Key Features

- 🌐 **Global Network Access**: Run measurements from thousands of probes worldwide
- 🤖 **AI-Friendly Interface**: Any LLM will easily parse the data and run new measurements as needed
- 📊 **Comprehensive Measurements**: Support for ping, traceroute, DNS, MTR, and HTTP tests
- 🔍 **Smart Context Handling**: Provides detailed parameter descriptions for AI clients to intelligently select measurement types and options
- 🔄 **Comparative Analysis**: Allows to compare network performance between different targets
- 🔑 **oAuth Support**: Use your own Globalping account for higher rate limits


## Installation

The remote MCP server is available under this endpoint `https://mcp.globalping.dev/sse`

You can integrate our Globalping MCP server with various AI tools that support the Model Context Protocol. 

Here are instructions for the top 3 most popular tools:

#### Claude Desktop App

Add to your Claude Desktop configuration file (located at `%APPDATA%\Claude\config.json` on Windows or `~/Library/Application Support/Claude/config.json` on macOS):

```json
{
    ""mcpServers"": {
        ""globalping"": {
            ""command"": ""npx"",
            ""args"": [
                ""mcp-remote"",
                ""https://mcp.globalping.dev/sse""
            ]
        }
    }
}
```

#### Anthropic Claude API (via Console)

When creating a Claude Assistant in the Anthropic Console:

1. Go to [console.anthropic.com](https://console.anthropic.com/)
2. Navigate to the Assistants section
3. Create a new Assistant or edit an existing one
4. In the Tools section, select ""Add custom tool""
5. Enter the following details:
   - Tool Name: `Globalping`
   - Description: `Run network tests from locations worldwide`
   - Tool URL: `https://mcp.globalping.dev/sse`

#### Cursor

To add the Globalping MCP server to Cursor:

1. Open Cursor settings
2. Navigate to the MCP tab
3. Click on ""+ Add new global MCP server""
4. This opens the `mcp.json` config file, where you will need to add:
```json
{
    ""mcpServers"": {
        ""globalping"": {
            ""command"": ""npx"",
            ""args"": [
                ""mcp-remote"",
                ""https://mcp.globalping.dev/sse""
            ]
        }
    }
}
```
5. Click ""Save"" and restart Cursor

## Connecting AI Assistants

This MCP server can be used with any MCP-compatible AI assistant, including:

- Claude Desktop
- Anthropic Assistants
- Cursor
- Windsurf
- Any custom implementation of the MCP protocol

See the MCP documentation for details on connecting clients to this server.


## Available Tools

- `ping` - Perform a ping test to a target
- `traceroute` - Perform a traceroute test to a target
- `dns` - Perform a DNS lookup for a domain
- `mtr` - Perform an MTR (My Traceroute) test to a target
- `http` - Perform an HTTP request to a URL
- `locations` - List all available Globalping probe locations
- `limits` - Show your current rate limits for the Globalping API
- `getMeasurement` - Retrieve a previously run measurement by ID
- `compareLocations` - Guide on how to run comparison measurements
- `help` - Show a help message with documentation on available tools

## Usage Examples

Once connected to an AI model through a compatible MCP client, you can interact with Globalping using natural language:

```
Ping google.com from 3 locations in Europe
```

```
Run a traceroute to github.com from Japan and compare with traceroute from the US
```

```
Check the DNS resolution of example.com using Google DNS (8.8.8.8)
```

```
Is jsdelivr.com reachable from China? Test with both ping and HTTP
```

```
What's the average response time for cloudflare.com across different continents?
```


## Location Specification

Locations can be specified using the ""magic"" field, which supports various formats:

- Continent codes: ""EU"", ""NA"", ""AS"", etc.
- Country codes: ""US"", ""DE"", ""JP"", etc.
- City names: ""London"", ""Tokyo"", ""New York"", etc.
- Network names: ""Cloudflare"", ""Google"", etc.
- ASN numbers: ""AS13335"", ""AS15169"", etc.
- Cloud provider regions: ""aws-us-east-1"", ""gcp-us-central1"", etc.

You can also combine these with a plus sign for more specific targeting: ""London+UK"", ""Cloudflare+US"", etc.


## Development

The codebase is organized into modules:

- `src/index.ts` - Main entry point and MCP agent definition
- `src/globalping/types.ts` - TypeScript interfaces for the Globalping API
- `src/globalping/api.ts` - API wrapper functions for Globalping
- `src/globalping/tools.ts` - MCP tool implementations
- `src/utils.ts` - Helper utilities for rendering the web UI


### Add Globalping credentials

Add Globalping OAuth credentials:

- `npx wrangler secret put GLOBALPING_CLIENT_ID`

### KV storage
Used for `OAuthProvider` docs https://github.com/cloudflare/workers-oauth-provider
- create a KV namespace and copy ID
- binding for it must be `OAUTH_KV`
- configure `kv_namespaces` in the `wrangler.jsonc` file
","Star
 5",2025-06-11 11:33:49.800930
https://github.com/ivnvxd/mcp-server-odoo,ivnvxd/mcp-server-odoo,"# MCP Server for Odoo

[![CI](https://github.com/ivnvxd/mcp-server-odoo/actions/workflows/ci.yml/badge.svg)](https://github.com/ivnvxd/mcp-server-odoo/actions/workflows/ci.yml)
[![codecov](https://codecov.io/gh/ivnvxd/mcp-server-odoo/branch/main/graph/badge.svg)](https://codecov.io/gh/ivnvxd/mcp-server-odoo)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)
[![Ruff](https://img.shields.io/endpoint?url=https://raw.githubusercontent.com/astral-sh/ruff/main/assets/badge/v2.json)](https://github.com/astral-sh/ruff)
[![Checked with mypy](https://www.mypy-lang.org/static/mypy_badge.svg)](https://mypy-lang.org/)
[![Python 3.10+](https://img.shields.io/badge/python-3.10+-blue.svg)](https://www.python.org/downloads/)
[![License: MPL 2.0](https://img.shields.io/badge/License-MPL_2.0-brightgreen.svg)](https://opensource.org/licenses/MPL-2.0)

An MCP server that enables AI assistants like Claude to interact with Odoo ERP systems. Access business data, search records, and work with Odoo through natural language.

## Features

- 🔍 **Search and retrieve** any Odoo record (customers, products, invoices, etc.)
- 📊 **Browse multiple records** and get formatted summaries
- 🔢 **Count records** matching specific criteria
- 📋 **Inspect model fields** to understand data structure
- 🔐 **Secure access** with API key or username/password authentication
- 🎯 **Smart pagination** for large datasets
- 💬 **LLM-optimized output** with hierarchical text formatting

## Installation

### Prerequisites

- Python 3.10 or higher
- Access to an Odoo instance (version 18.0+)
- The [Odoo MCP module](https://github.com/ivnvxd/mcp-server-odoo/tree/main/odoo-apps/mcp_server) installed on your Odoo server
- An API key generated in Odoo (Settings > Users > API Keys)

### Installing via MCP Settings (Recommended)

Add this configuration to your MCP settings:

<details>
<summary>Claude Desktop</summary>

Add to `~/Library/Application Support/Claude/claude_desktop_config.json`:

```json
{
  ""mcpServers"": {
    ""odoo"": {
      ""command"": ""uvx"",
      ""args"": [""mcp-server-odoo""],
      ""env"": {
        ""ODOO_URL"": ""https://your-odoo-instance.com"",
        ""ODOO_API_KEY"": ""your-api-key-here"",
        ""ODOO_DB"": ""your-database-name""
      }
    }
  }
}
```
</details>

<details>
<summary>Cursor</summary>

Add to `~/.cursor/mcp_settings.json`:

```json
{
  ""mcpServers"": {
    ""odoo"": {
      ""command"": ""uvx"",
      ""args"": [""mcp-server-odoo""],
      ""env"": {
        ""ODOO_URL"": ""https://your-odoo-instance.com"",
        ""ODOO_API_KEY"": ""your-api-key-here"",
        ""ODOO_DB"": ""your-database-name""
      }
    }
  }
}
```
</details>

<details>
<summary>VS Code (with GitHub Copilot)</summary>

Add to your VS Code settings (`~/.vscode/mcp_settings.json` or workspace settings):

```json
{
  ""github.copilot.chat.mcpServers"": {
    ""odoo"": {
      ""command"": ""uvx"",
      ""args"": [""mcp-server-odoo""],
      ""env"": {
        ""ODOO_URL"": ""https://your-odoo-instance.com"",
        ""ODOO_API_KEY"": ""your-api-key-here"",
        ""ODOO_DB"": ""your-database-name""
      }
    }
  }
}
```
</details>

<details>
<summary>Zed</summary>

Add to `~/.config/zed/settings.json`:

```json
{
  ""context_servers"": {
    ""odoo"": {
      ""command"": ""uvx"",
      ""args"": [""mcp-server-odoo""],
      ""env"": {
        ""ODOO_URL"": ""https://your-odoo-instance.com"",
        ""ODOO_API_KEY"": ""your-api-key-here"",
        ""ODOO_DB"": ""your-database-name""
      }
    }
  }
}
```
</details>

### Alternative Installation Methods

<details>
<summary>Using pip</summary>

```bash
# Install globally
pip install mcp-server-odoo

# Or use pipx for isolated environment
pipx install mcp-server-odoo
```

Then use `mcp-server-odoo` as the command in your MCP configuration.
</details>

<details>
<summary>From source</summary>

```bash
git clone https://github.com/ivnvxd/mcp-server-odoo.git
cd mcp-server-odoo
pip install -e .
```

Then use the full path to the package in your MCP configuration.
</details>

## Configuration

### Environment Variables

The server requires the following environment variables:

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `ODOO_URL` | Yes | Your Odoo instance URL | `https://mycompany.odoo.com` |
| `ODOO_API_KEY` | Yes* | API key for authentication | `0ef5b399e9ee9c11b053dfb6eeba8de473c29fcd` |
| `ODOO_USER` | Yes* | Username (if not using API key) | `admin` |
| `ODOO_PASSWORD` | Yes* | Password (if not using API key) | `admin` |
| `ODOO_DB` | No | Database name (auto-detected if not set) | `mycompany` |

*Either `ODOO_API_KEY` or both `ODOO_USER` and `ODOO_PASSWORD` are required.

### Setting up Odoo

1. **Install the MCP module**:
   - Download the [mcp_server module](https://github.com/ivnvxd/mcp-server-odoo/tree/main/odoo-apps/mcp_server)
   - Install it in your Odoo instance
   - Navigate to Settings > MCP Server

2. **Enable models for MCP access**:
   - Go to Settings > MCP Server > Enabled Models
   - Add models you want to access (e.g., res.partner, product.product)
   - Configure permissions (read, write, create, delete) per model

3. **Generate an API key**:
   - Go to Settings > Users & Companies > Users
   - Select your user
   - Under the ""API Keys"" tab, create a new key
   - Copy the key for your MCP configuration

## Usage Examples

Once configured, you can ask Claude:

- ""Show me all customers from Spain""
- ""Find products with stock below 10 units""
- ""List today's sales orders over $1000""
- ""Search for unpaid invoices from last month""
- ""Count how many active employees we have""
- ""Show me the contact information for Microsoft""

## Available Tools

### `search_records`
Search for records in any Odoo model with filters.

```json
{
  ""model"": ""res.partner"",
  ""domain"": [[""is_company"", ""="", true], [""country_id.code"", ""="", ""ES""]],
  ""fields"": [""name"", ""email"", ""phone""],
  ""limit"": 10
}
```

**Field Selection Options:**
- Omit `fields` or set to `null`: Returns smart selection of common fields
- Specify field list: Returns only those specific fields
- Use `[""__all__""]`: Returns all fields (use with caution)

### `get_record`
Retrieve a specific record by ID.

```json
{
  ""model"": ""res.partner"",
  ""record_id"": 42,
  ""fields"": [""name"", ""email"", ""street"", ""city""]
}
```

**Field Selection Options:**
- Omit `fields` or set to `null`: Returns smart selection of common fields with metadata
- Specify field list: Returns only those specific fields
- Use `[""__all__""]`: Returns all fields without metadata

### `list_models`
List all models enabled for MCP access.

```json
{}
```

### `create_record`
Create a new record in Odoo.

```json
{
  ""model"": ""res.partner"",
  ""values"": {
    ""name"": ""New Customer"",
    ""email"": ""<EMAIL>"",
    ""is_company"": true
  }
}
```

### `update_record`
Update an existing record.

```json
{
  ""model"": ""res.partner"",
  ""record_id"": 42,
  ""values"": {
    ""phone"": ""+1234567890"",
    ""website"": ""https://example.com""
  }
}
```

### `delete_record`
Delete a record from Odoo.

```json
{
  ""model"": ""res.partner"",
  ""record_id"": 42
}
```

## Resources

The server also provides direct access to Odoo data through resource URIs:

- `odoo://res.partner/record/1` - Get partner with ID 1
- `odoo://product.product/search?domain=[[""qty_available"","">"",0]]` - Search products in stock
- `odoo://sale.order/browse?ids=1,2,3` - Browse multiple sales orders
- `odoo://res.partner/count?domain=[[""customer_rank"","">"",0]]` - Count customers
- `odoo://product.product/fields` - List available fields for products

## Security

- Always use HTTPS in production environments
- Keep your API keys secure and rotate them regularly
- Configure model access carefully - only enable necessary models
- The MCP module respects Odoo's built-in access rights and record rules
- Each API key is linked to a specific user with their permissions

## Troubleshooting

<details>
<summary>Connection Issues</summary>

If you're getting connection errors:
1. Verify your Odoo URL is correct and accessible
2. Check that the MCP module is installed: visit `https://your-odoo.com/mcp/health`
3. Ensure your firewall allows connections to Odoo
</details>

<details>
<summary>Authentication Errors</summary>

If authentication fails:
1. Verify your API key is active in Odoo
2. Check that the user has appropriate permissions
3. Try regenerating the API key
4. For username/password auth, ensure 2FA is not enabled
</details>

<details>
<summary>Model Access Errors</summary>

If you can't access certain models:
1. Go to Settings > MCP Server > Enabled Models in Odoo
2. Ensure the model is in the list and has appropriate permissions
3. Check that your user has access to that model in Odoo's security settings
</details>

<details>
<summary>Debug Mode</summary>

Enable debug logging for more information:

```json
{
  ""env"": {
    ""ODOO_URL"": ""https://your-odoo.com"",
    ""ODOO_API_KEY"": ""your-key"",
    ""ODOO_MCP_LOG_LEVEL"": ""DEBUG""
  }
}
```
</details>

## Development

<details>
<summary>Running from source</summary>

```bash
# Clone the repository
git clone https://github.com/ivnvxd/mcp-server-odoo.git
cd mcp-server-odoo

# Install in development mode
pip install -e "".[dev]""

# Run tests
pytest --cov

# Run the server
python -m mcp_server_odoo
```
</details>

<details>
<summary>Testing with MCP Inspector</summary>

```bash
# Using uvx
npx @modelcontextprotocol/inspector uvx mcp-server-odoo

# Using local installation
npx @modelcontextprotocol/inspector python -m mcp_server_odoo
```
</details>

## License

This project is licensed under the Mozilla Public License 2.0 (MPL-2.0) - see the [LICENSE](LICENSE) file for details.

## Contributing

Contributions are very welcome! Please see the [CONTRIBUTING](CONTRIBUTING.md) guide for details.

## Support

Thank you for using this project! If you find it helpful and would like to support my work, kindly consider buying me a coffee. Your support is greatly appreciated!

<a href=""https://www.buymeacoffee.com/ivnvxd"" target=""_blank""><img src=""https://cdn.buymeacoffee.com/buttons/v2/default-yellow.png"" alt=""Buy Me A Coffee"" style=""height: 60px !important;width: 217px !important;"" ></a>

And do not forget to give the project a star if you like it! :star:","Star
 5",2025-06-11 11:33:49.800930
https://github.com/lucygoodchild/mcp-national-rail,lucygoodchild/mcp-national-rail,"[![smithery badge](https://smithery.ai/badge/@lucygoodchild/mcp-national-rail)](https://smithery.ai/server/@lucygoodchild/mcp-national-rail)

# mcp-national-rail

A Model Context Protocol (MCP) server to retrieve train schedules from National Rail.

## Overview

This project implements a server using the Model Context Protocol (MCP) that allows AI agents to retrieve train information on National Rail trains using the Realtime Trains API. 

It provides tools for:
- get_live_departures
- get_live_arrivals
- get_departures_by_date
- get_arrivals_by_date

## Installation

Real Time Trains API account can be created from here: https://api.rtt.io/ 

You will need to note down your API Auth credentials 

### Installing via Smithery

To install mcp-national-rail for Claude Desktop automatically via [Smithery](https://smithery.ai/server/@lucygoodchild/mcp-national-rail):

```bash
npx -y @smithery/cli install @lucygoodchild/mcp-national-rail --client claude
```

### Manual Installation for Claude Desktop

#### Prerequisites
- Node.js

#### Setup

1. Clone this repository
2. Install dependencies
```bash
npm install
```
3. Build and start the project
```bash
npm run build
npm run start
```
4. Add the following to your MCP client configuration (~/Library/Application Support/Claude/claude_desktop_config.json):
```bash
{
  ""mcpServers"": {
    ""mcp-national-rail"": {
      ""command"": ""node"",
      ""args"": [""/path/to/mcp-national-rail/dist/index.js""],
      ""env"": {
        ""RTT_API_USERNAME"": ""your_rtt_api_username"",
        ""RTT_API_PASSWORD"": ""your_rtt_api_password""
      }
    }
  }
}
```
Make sure to replace ""/path/to/mcp-national-rail/dist/index.js"" with the actual path and add your RTT API username and password which can be created from here: https://api.rtt.io/

5. Restart Claude

### Development
- Create .env file based on the example file
```bash
RTT_API_USERNAME=your_rtt_api_username
RTT_API_PASSWORD=your_rtt_api_password
```

Run the inspector with the following command:
```bash
npx @modelcontextprotocol/inspector node dist/index.js
```
","Star
 0",2025-06-11 11:33:49.800930
https://github.com/marctheshark3/ergo-mcp,marctheshark3/ergo-mcp,,,2025-05-07 15:22:02.281957
https://github.com/vishalmysore/choturobo,vishalmysore/choturobo,"# **Chotu Robo Server - MCP Server for Arduino **

<div align=""center"">
  <img src=""Chotu.png"" alt=""Chotu Robo"" width=""300"" height=""300"">
</div>


## DEMO

👉 [Chotu Robo Demo 1](https://www.linkedin.com/posts/vishalrow_robotics-artificialintelligence-openai-activity-7312972405512028160-FfPB?utm_source=share&utm_medium=member_desktop&rcm=ACoAAABupTgBwY_78GTh-90OXNsbtOgGHbRO7HQ)

👉  [Chotu Robo Demo 2](https://www.linkedin.com/posts/vishalrow_claudeai-anthropic-ai-activity-7307097300147834880-Mi7n?utm_source=share&utm_medium=member_desktop&rcm=ACoAAABupTgBwY_78GTh-90OXNsbtOgGHbRO7HQ)

👉  [Chotu Robo Demo 3](https://www.linkedin.com/posts/vishalrow_autonomousrobot-springboot-artificialintelligence-activity-7316839291148554240-jFyM?utm_source=share&utm_medium=member_desktop&rcm=ACoAAABupTgBwY_78GTh-90OXNsbtOgGHbRO7HQ)

👉  [Chotu Robo Demo 4](https://www.linkedin.com/feed/update/urn:li:activity:7319065900031098880/)  

👉  [Chotu Robo Article](https://www.linkedin.com/pulse/arduino-robot-controlled-artificial-intelligence-using-vishal-mysore-kwltc/?utm_source=share&utm_medium=member_android&utm_campaign=share_via)

👉  [Chotu Robo Article 2](https://www.linkedin.com/pulse/choturobo-mapletruck-step-guide-building-ai-powered-vishal-mysore-elvzc/?trackingId=0oj6GKrfS5eZu%2B9OYuIb3w%3D%3D)  


<div align=""center"">
  <img src=""mapleTruck.jpg"" alt=""Chotu Robo"" width=""300"" height=""300"">
</div> 

## Overview 
 
This project showcases how to integrate Arduino-based robotics (using the **NodeMCU ESP32** or **Arduino Nano 368** board) with **AI** using the **MCP (Model Context Protocol)** framework from Claude AI by Anthropic. It provides a seamless connection between hardware components and AI through a simple interface that can be controlled by a user via a mobile phone or computer.

This system acts as a **robotic server** (named **Chotu Robo**) that can interact with various physical components like LEDs, motors, servos, fans, sensors, and more. These components can be controlled remotely by sending commands to the server, which in turn controls the hardware via the ESP32 or Arduino Nano 368 microcontroller.

This setup opens up the possibility for future AI-based applications, such as controlling the robot through AI assistants (like Claude) from mobile or web applications.

The Chotu Robot operates in two modes: Wired Mode and Wireless Mode, each designed for different use cases and hardware configurations.

### Wired Mode - Arduino Nano 368
In Wired Mode, the robot uses an Arduino Nano 368 board, where the code is uploaded directly via USB. This mode does not support real-time commands over the network as it requires a physical USB connection to operate. The robot runs its pre-uploaded code, making it suitable for projects where Wi-Fi or network connectivity is not required.

### Wireless Mode - ESP32 Wi-Fi Enabled Web Server
In Wireless Mode, the robot is powered by an ESP32 board. The ESP32 connects to your local Wi-Fi network and starts a Wi-Fi enabled web server that listens for real-time commands through REST API or WebSocket. This mode allows the robot to interact with external devices and control various hardware components like LEDs, motors, servos, and fans.

Once the ESP32 is connected to Wi-Fi, it accepts incoming commands that control hardware functionality in real-time. These commands can be sent from a TypeScript MCP server or a Java Spring-based MCP server, allowing seamless integration between microcontroller hardware and AI-based systems for automation, robotics, or IoT applications.

## Features

1. **LED Control**: Blink an LED for a given duration.
2. **Buzzer Control**: Sound a buzzer for a specified time.
3. **Motor Control**: Run a motor at a certain speed for a set time.
4. **Servo Control**: Move a servo motor to a specific angle.
5. **Fan Control**: Turn a fan on/off.
6. **Relay Control**: Switch a relay on or off.
7. **Sensor Readings**:
    - Read **temperature** from a sensor.
    - Measure **distance** using an ultrasonic sensor.
8. **AI-powered Commands**: Control the robot using AI-based prompts for actions like moving, starting, stopping, turning, and adjusting speed.

## Components Used

- **NodeMCU ESP32** or **Arduino Nano 368**: Microcontrollers responsible for controlling the hardware and communicating with the server.
- **Johnny-Five**: The JavaScript robotics and IoT library to interact with the hardware components.
- **MCP Framework**: The Model Context Protocol framework to handle requests and interactions with external systems, enabling AI-based communication.
- **Sensors & Actuators**:
    - **LED** (for indicating status)
    - **Buzzer**
    - **Servo Motor**
    - **Motor**
    - **Relay Module**
    - **Ultrasonic Distance Sensor**
    - **Temperature Sensor**

## How It Works

The **Chotu Robo Server** acts as the main hub for handling commands from an AI system. The following flow outlines how the system works:

1. **Setup**:
    - The **NodeMCU ESP32** or **Arduino Nano 368** board is connected to various sensors and actuators (LEDs, motors, etc.).
    - The **Johnny-Five** library allows the server to control hardware components through the microcontroller.

2. **MCP Integration**:
    - The **McpServer** manages communication between the hardware and AI.
    - The server provides several **tools** (commands) such as `blinkLED`, `buzz`, `runMotor`, `moveServo`, and more.
    - Each tool is associated with a specific function, and the server waits for commands from the AI system to execute those actions.

3. **AI Control**:
    - **AI Prompts** are used to trigger actions like moving the robot (`move-chotu`), controlling speed (`set-chotu-speed`), and turning the robot (`turn-chotu`).
    - The AI uses **Claude** or other AI assistants to send commands to the server. The server processes these commands and interacts with the hardware accordingly.

4. **Real-Time Communication**:
    - **StdioServerTransport** ensures the server can connect to external AI systems and execute commands in real-time.

5. **User Commands**:
    - The AI can send user-friendly commands like **""turn Chotu left""** or **""set speed to 5""** to control the robot's behavior.

## Commands and Tools

Here are some of the available commands and their functionalities:

- **`blinkLED`**: Blink the LED for a specified time.
- **`buzz`**: Activate the buzzer for a certain duration.
- **`runMotor`**: Start the motor at a given speed for a set period.
- **`moveServo`**: Move the servo motor to a specified angle.
- **`controlFan`**: Turn the fan on or off.
- **`toggleRelay`**: Switch a relay module on or off.
- **`readTemperature`**: Get the current temperature reading.
- **`readDistance`**: Measure the distance using the ultrasonic sensor.
- **`move-chotu`**: Move Chotu in specific steps (via AI prompt).
- **`start-chotu`**: Start Chotu and get ready to operate.
- **`stop-chotu`**: Stop Chotu and shut down operations.
- **`turn-chotu`**: Turn Chotu to a specified direction (left or right).
- **`set-chotu-speed`**: Set the speed for Chotu.

## How to Use

### Requirements

1. **Hardware**:
    - **NodeMCU ESP32** or **Arduino Nano 368** (or any supported Arduino/ESP32 board).
    - Various sensors and actuators (LED, servo, motor, ultrasonic sensor, temperature sensor, relay).

2. **Software**:
    - Install **Node.js**.
    - Install **Johnny-Five** for hardware control.
    - Use the **MCP SDK** to integrate with AI.
    - Use the **Arduino IDE** to program the microcontroller.

### Steps to Setup

1. Install **Node.js** and **Johnny-Five**:
   ```bash
   npm install johnny-five
   npm install @modelcontextprotocol/sdk
   Set up the ESP32 or Arduino Nano 368 on the Arduino IDE:

   Install the ESP32 board support in the Arduino IDE for ESP32 or Arduino Nano 368 board if using.
   Connect the NodeMCU ESP32 or Arduino Nano 368 to your computer via USB.
   Upload the Code to the Board:
        
   Use the Arduino IDE to upload the provided code to the ESP32 or Arduino Nano 368.
   ```
   Run the Server:
        
        Start the MCP server by running the script. This will set up the server and establish communication with the hardware.
        Connect to the AI:
        
        Send commands from the AI system (like Claude) to control the robot.
Example AI Commands
Move Chotu:

Command: ""Move Chotu forward by 5 steps.""
Action: The robot will move based on the specified number of steps.
Set Speed:

Command: ""Set speed to 10.""
Action: The robot's speed will be adjusted accordingly.
Turn Left:

Command: ""Turn Chotu left.""
Action: The robot will rotate left.

### Debug using the MCP Inspector:
```  npx @modelcontextprotocol/inspector node  build/choturobo.js ```

## Future Improvements
This project can serve as a foundational framework for developing more advanced AI-based robotic systems. You can extend the functionality by:
1. **Adding more sensors** like cameras or microphones.
2. **Integrating advanced AI models** for better decision-making.
3. **Developing a mobile app or web interface** to control the robot via a graphical interface.
4. **Using a Raspberry Pi**:
    - **Setup**: Use a Raspberry Pi to run the MCP server and control the hardware components.
    - **Installation**:
        ```bash
        sudo apt-get update
        sudo apt-get install nodejs npm
        npm install johnny-five
        npm install @modelcontextprotocol/sdk
        ```
    - **Connecting to Java-based Server**:
        - **Java Server Setup**: Create a Java-based MCP server to handle requests and communicate with the Raspberry Pi.
        - **Java Code Example**:
            ```java
            import org.modelcontextprotocol.server.McpServer;
            import org.modelcontextprotocol.server.transport.WebSocketTransport;

            public class ChotuRoboServer {
                public static void main(String[] args) {
                    McpServer server = new McpServer();
                    WebSocketTransport transport = new WebSocketTransport(""ws://raspberrypi.local:8080"");
                    server.connect(transport);

                    // Define tools and prompts here
                    server.tool(""blinkLED"", params -> {
                        // Implement LED blinking logic
                    });

                    server.start();
                }
            }
            ```
        - **Running the Server**:
            ```bash
            javac -cp modelcontextprotocol-sdk.jar ChotuRoboServer.java
            java -cp .:modelcontextprotocol-sdk.jar ChotuRoboServer
            ```
    - **Connecting Raspberry Pi to Java Server**:
        - Ensure the Raspberry Pi and Java server are on the same network.
        - Use WebSocket or REST API to send commands from the Java server to the Raspberry Pi.
        - Example command from Java server to Raspberry Pi:
            ```java
            WebSocketClient client = new WebSocketClient(new URI(""ws://raspberrypi.local:8080""));
            client.connect();
            client.send(""{\""command\"": \""blinkLED\"", \""params\"": {\""time\"": 5}}"");
            ```

By following these steps, you can enhance the Chotu Robo project with more advanced features and better integration with AI and Java-based systems.","Star
 37",2025-05-07 15:22:02.281957
https://github.com/Boston343/starwind-ui-mcp,Boston343/starwind-ui-mcp,"# Starwind UI MCP Server

[![smithery badge](https://smithery.ai/badge/@Boston343/starwind-ui-mcp)](https://smithery.ai/server/@Boston343/starwind-ui-mcp)

A TypeScript implementation of a Model Context Protocol (MCP) server for Starwind UI, providing tools to help developers work with Starwind UI components.

## Quick Start

```bash
# Install dependencies
pnpm install

# Build the TypeScript code
pnpm build

# Start the server
pnpm start
```

### Installing via Smithery

To install Starwind UI MCP Server for Claude Desktop automatically via [Smithery](https://smithery.ai/server/@Boston343/starwind-ui-mcp):

```bash
npx -y @smithery/cli install @Boston343/starwind-ui-mcp --client claude
```

### Setup Your IDE

Instructions to set up your IDE to use a local MCP server vary by IDE. Here is an example for Windsurf:

```json title=""mcp_config.json""
{
	""mcpServers"": {
		""starwind ui"": {
			""command"": ""node"",
			""args"": [""c:\\path\\to\\folder\\starwind-ui-mcp\\dist\\server.js""],
			""env"": {}
		}
	}
}
```

Detailed instructions:

- [Windsurf MCP Setup](https://docs.codeium.com/windsurf/mcp)
- [Cursor MCP Setup](https://docs.cursor.com/context/model-context-protocol)

## What is MCP?

The Model Context Protocol (MCP) is a protocol for extending AI capabilities through local servers. This implementation provides Starwind UI-specific tools to enhance AI assistant capabilities when working with Starwind UI. For more information about MCP itself, please visit the [official documentation](https://modelcontextprotocol.io/).

## Features

- **Tool-based Architecture** - Modular design for easy addition of new tools
- **Starwind UI Documentation Tool** - Access documentation links for Starwind UI components
- **Package Manager Detection** - Detect and use the appropriate package manager (npm, yarn, pnpm)
- **LLM Data Fetcher** - Retrieve Starwind UI information for LLMs with caching and rate limiting
- **TypeScript Implementation** - Built with TypeScript for better type safety and developer experience
- **Standard I/O Transport** - Uses stdio for communication with AI assistants

## Available Tools

| Tool Name             | Description                                                       |
| --------------------- | ----------------------------------------------------------------- |
| `init_project`        | Initializes a new Starwind UI project                             |
| `install_component`   | Generates installation commands for Starwind UI components        |
| `update_component`    | Generates update commands for Starwind UI components              |
| `get_documentation`   | Returns documentation links for Starwind UI components and guides |
| `fetch_llm_data`      | Fetches LLM data from starwind.dev (rate limited, with caching)   |
| `get_package_manager` | Detects and returns the current package manager information       |

## Development

This project is set up to use PNPM for package manager for development purposes. If you are not using pnpm, you will need to update the package.json file with the appropriate package manager commands you need.

## Project Structure

```
src/
  ├── config/         # Server configuration
  │   └── settings.ts # Configuration settings
  ├── tools/          # MCP tools implementations
  │   ├── index.ts    # Tool registration
  │   └── *.ts        # Individual tool implementations
  ├── utils/          # Utility functions
  └── server.ts       # Main MCP server implementation
```

## Adding New Tools

To add your own tools to the server:

1. Create a new tool file in the `src/tools/` directory
2. Register the tool in `src/tools/index.ts`
3. Enable the tool in `src/config/settings.ts`
4. Rebuild the server with `pnpm build`
5. Restart the server with `pnpm start`

## License

MIT License - See LICENSE file for details.
","Star
 17",2025-05-07 15:22:02.281957
https://github.com/aliyun/alibabacloud-hologres-mcp-server,aliyun/alibabacloud-hologres-mcp-server,"# Hologres MCP Server

Hologres MCP Server serves as a universal interface between AI Agents and Hologres databases. It enables seamless communication between AI Agents and Hologres, helping AI Agents retrieve Hologres database metadata and execute SQL operations.

## Configuration

### Mode 1: Using Local File

#### Download

Download from Github

```shell
git clone https://github.com/aliyun/alibabacloud-hologres-mcp-server.git
```

#### MCP Integration

Add the following configuration to the MCP client configuration file:

```json
""mcpServers"": {
  ""hologres-mcp-server"": {
    ""command"": ""uv"",
    ""args"": [
      ""--directory"",
      ""/path/to/alibabacloud-hologres-mcp-server"",
      ""run"",
      ""hologres-mcp-server""
    ],
    ""env"": {
      ""HOLOGRES_HOST"": ""host"",
      ""HOLOGRES_PORT"": ""port"",
      ""HOLOGRES_USER"": ""access_id"",
      ""HOLOGRES_PASSWORD"": ""access_key"",
      ""HOLOGRES_DATABASE"": ""database""
    }
  }
}
```

### Mode 2: Using PIP Mode

#### Installation

Install MCP Server using the following package:

```bash
pip install hologres-mcp-server
```

#### MCP Integration

Add the following configuration to the MCP client configuration file:

```json
""mcpServers"": {
    ""hologres-mcp-server"": {
      ""command"": ""uv"",
      ""args"": [
        ""run"",
        ""--with"",
        ""hologres-mcp-server"",
        ""hologres-mcp-server""
      ],
      ""env"": {
        ""HOLOGRES_HOST"": ""host"",
        ""HOLOGRES_PORT"": ""port"",
        ""HOLOGRES_USER"": ""access_id"",
        ""HOLOGRES_PASSWORD"": ""access_key"",
        ""HOLOGRES_DATABASE"": ""database""
      }
    }
  }
```

## Components

### Tools

* `execute_select_sql`: Execute a SELECT SQL query on the Hologres server
* `execute_dml_sql`: Execute a DML (INSERT, UPDATE, DELETE) SQL query on the Hologres server
* `execute_ddl_sql`: Execute a DDL (CREATE, ALTER, DROP) SQL query on the Hologres server
* `gather_table_statistics`: Collect table statistics
* `get_query_plan`: Get query plan
* `get_execution_plan`: Get execution plan

### Resources

#### Built-in Resources

* `hologres:///schemas`: Get all schemas in the database

#### Resource Templates

* `hologres:///{schema}/tables`: List all tables in a schema
* `hologres:///{schema}/{table}/partitions`: List all partitions of a partitioned table
* `hologres:///{schema}/{table}/ddl`: Get table DDL
* `hologres:///{schema}/{table}/statistic`: Show collected table statistics
* `system:///{+system_path}`:
  System paths include:

  * missing_stats_tables - Shows the tables that are missing statistics.
  * stat_activity - Shows the information of current running queries.
  * query_log/latest/<row_limits> - Get recent query log history with specified number of rows.
  * query_log/user/<user_name>/<row_limits> - Get query log history for a specific user with row limits.
  * query_log/application/<application_name>/<row_limits> - Get query log history for a specific application with row limits.
  * query_log/failed/\<interval>\/<row_limits> - Get failed query log history with interval and specified number of rows.

### Prompts

None at this time
","Star
 18",2025-05-07 15:22:02.281957
https://github.com/rishijatia/fantasy-pl-mcp,rishijatia/fantasy-pl-mcp,"# Fantasy Premier League MCP Server

[![PyPI version](https://badge.fury.io/py/fpl-mcp.svg)](https://badge.fury.io/py/fpl-mcp)
[![Package Check](https://github.com/rishijatia/fantasy-pl-mcp/actions/workflows/package-check.yml/badge.svg)](https://github.com/rishijatia/fantasy-pl-mcp/actions/workflows/package-check.yml)
[![PyPI - Python Version](https://img.shields.io/pypi/pyversions/fpl-mcp)](https://pypi.org/project/fpl-mcp/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Downloads](https://static.pepy.tech/badge/fpl-mcp)](https://pepy.tech/project/fpl-mcp)
<a href=""https://glama.ai/mcp/servers/2zxsxuxuj9"">
  <img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/2zxsxuxuj9/badge"" />

A Model Context Protocol (MCP) server that provides access to Fantasy Premier League (FPL) data and tools. This server allows you to interact with FPL data in Claude for Desktop and other MCP-compatible clients.

*Demo of the Fantasy Premier League MCP Server in action*

[![Fantasy Premier League MCP Demo](https://img.youtube.com/vi/QfOOOQ_jeMA/0.jpg)](https://youtu.be/QfOOOQ_jeMA)


## Supported Platforms

- Claude Desktop
- Cursor
- Windsurf
- Other MCP Compatible Desktop LLMs

Mobile is currently not supported.

## Features

- **Rich Player Data**: Access comprehensive player statistics from the FPL API
- **Team Information**: Get details about Premier League teams
- **Gameweek Data**: View current and past gameweek information
- **Player Search**: Find players by name or team
- **Player Comparison**: Compare detailed statistics between any two players

## Requirements

- Python 3.10 or higher
- Claude Desktop (for AI integration)

## Installation

### Option 1: Install from PyPI (Recommended)

```bash
pip install fpl-mcp
```

### Option 1b: Install with Development Dependencies

```bash
pip install ""fpl-mcp[dev]""
```

### Option 2: Install from GitHub

```bash
pip install git+https://github.com/rishijatia/fantasy-pl-mcp.git
```

### Option 3: Clone and Install Locally

```bash
git clone https://github.com/rishijatia/fantasy-pl-mcp.git
cd fantasy-pl-mcp
pip install -e .
```

## Running the Server

After installation, you have several options to run the server:

### 1. Using the CLI command

```bash
fpl-mcp
```

### 2. Using the Python module

```bash
python -m fpl_mcp
```

### 3. Using with Claude Desktop

Configure Claude Desktop to use the installed package by editing your `claude_desktop_config.json` file:

**Method 1: Using the Python module directly (most reliable)**

```json
{
  ""mcpServers"": {
    ""fantasy-pl"": {
      ""command"": ""python"",
      ""args"": [""-m"", ""fpl_mcp""]
    }
  }
}
```

**Method 2: Using the installed command with full path (if installed with pip)**

```json
{
  ""mcpServers"": {
    ""fantasy-pl"": {
      ""command"": ""/full/path/to/your/venv/bin/fpl-mcp""
    }
  }
}
```

Replace `/full/path/to/your/venv/bin/fpl-mcp` with the actual path to the executable. You can find this by running `which fpl-mcp` in your terminal after activating your virtual environment.

> **Note:** Using just `""command"": ""fpl-mcp""` may result in a `spawn fpl-mcp ENOENT` error since Claude Desktop might not have access to your virtual environment's PATH. Using the full path or the Python module approach helps avoid this issue.

## Usage

### In Claude for Desktop

1. Start Claude for Desktop
2. You should see FPL tools available via the hammer icon
3. Example queries:
   - ""Compare Mohamed Salah and Erling Haaland over the last 5 gameweeks""
   - ""Find all Arsenal midfielders""
   - ""What's the current gameweek status?""
   - ""Show me the top 5 forwards by points""

#### Fantasy-PL MCP Usage Instructions

#### Basic Commands:
- Compare players: ""Compare [Player1] and [Player2]""
- Find players: ""Find players from [Team]"" or ""Search for [Player Name]""
- Fixture difficulty: ""Show upcoming fixtures for [Team]""
- Captain advice: ""Who should I captain between [Player1] and [Player2]?""

#### Advanced Features:
- Statistical analysis: ""Compare underlying stats for [Player1] and [Player2]""
- Form check: ""Show me players in form right now""
- Differential picks: ""Suggest differentials under 10% ownership""
- Team optimization: ""Rate my team and suggest transfers""

#### Tips:
- Be specific with player names for accurate results
- Include positions when searching (FWD, MID, DEF, GK)
- For best captain advice, ask about form, fixtures, and underlying stats
- Request comparison of specific metrics (xG, shots in box, etc.   

### MCP Inspector for Development

For development and testing:

```bash
# If you have mcp[cli] installed
mcp dev -m fpl_mcp

# Or use npx
npx @modelcontextprotocol/inspector python -m fpl_mcp
```

## Available Resources
- `fpl://static/players` - All player data with comprehensive statistics
- `fpl://static/players/{name}` - Player data by name search
- `fpl://static/teams` - All Premier League teams
- `fpl://static/teams/{name}` - Team data by name search
- `fpl://gameweeks/current` - Current gameweek data
- `fpl://gameweeks/all` - All gameweeks data
- `fpl://fixtures` - All fixtures for the current season
- `fpl://fixtures/gameweek/{gameweek_id}` - Fixtures for a specific gameweek
- `fpl://fixtures/team/{team_name}` - Fixtures for a specific team
- `fpl://players/{player_name}/fixtures` - Upcoming fixtures for a specific player
- `fpl://gameweeks/blank` - Information about upcoming blank gameweeks
- `fpl://gameweeks/double` - Information about upcoming double gameweeks

## Available Tools
- `get_gameweek_status` - Get precise information about current, previous, and next gameweeks
- `analyze_player_fixtures` - Analyze upcoming fixtures for a player with difficulty ratings
- `get_blank_gameweeks` - Get information about upcoming blank gameweeks
- `get_double_gameweeks` - Get information about upcoming double gameweeks
- `analyze_players` - Filter and analyze FPL players based on multiple criteria
- `analyze_fixtures` - Analyze upcoming fixtures for players, teams, or positions
- `compare_players` - Compare multiple players across various metrics
- `check_fpl_authentication` - Check if FPL authentication is working correctly
- `get_my_team` - View your authenticated team (requires authentication)
- `get_team` - View any team with a specific ID (requires authentication)
- `get_manager_info` - Get manager details (requires authentication)

## Prompt Templates
- `player_analysis_prompt` - Create a prompt for analyzing an FPL player in depth
- `transfer_advice_prompt` - Get advice on player transfers based on budget and position
- `team_rating_prompt` - Create a prompt for rating and analyzing an FPL team
- `differential_players_prompt` - Create a prompt for finding differential players with low ownership
- `chip_strategy_prompt` - Create a prompt for chip strategy advice

## Development

### Adding Features

To add new features:

1. Add resource handlers in the appropriate file within `fpl_mcp/fpl/resources/`
2. Add tool handlers in the appropriate file within `fpl_mcp/fpl/tools/`
3. Update the `__main__.py` file to register new resources and tools
4. Test using the MCP Inspector before deploying to Claude for Desktop

## Authentication

To use features requiring authentication (like accessing your team or private leagues), you need to set up your FPL credentials:

```bash
# Run the credential setup tool
fpl-mcp-config setup
```

This interactive tool will:
1. Prompt for your FPL email, password, and team ID
2. Let you choose between storing in config.json or .env file
3. Save credentials securely to ~/.fpl-mcp/

You can test your authentication with:
```bash
fpl-mcp-config test
```

Alternatively, you can manually configure authentication:
1. Create `~/.fpl-mcp/.env` file with:
   ```
   FPL_EMAIL=<EMAIL>
   FPL_PASSWORD=your_password
   FPL_TEAM_ID=your_team_id
   ```
   
2. Or create `~/.fpl-mcp/config.json`:
   ```json
   {
     ""email"": ""<EMAIL>"",
     ""password"": ""your_password"",
     ""team_id"": ""your_team_id""
   }
   ```

3. Or set environment variables:
   ```bash
   export FPL_EMAIL=<EMAIL>
   export FPL_PASSWORD=your_password
   export FPL_TEAM_ID=your_team_id
   ```

## Limitations

- The FPL API is not officially documented and may change without notice
- Only read operations are currently supported

## Troubleshooting

### Common Issues

#### 1. ""spawn fpl-mcp ENOENT"" error in Claude Desktop

This occurs because Claude Desktop cannot find the `fpl-mcp` executable in its PATH.

**Solution:** Use one of these approaches:

- Use the full path to the executable in your config file
  ```json
  {
    ""mcpServers"": {
      ""fantasy-pl"": {
        ""command"": ""/full/path/to/your/venv/bin/fpl-mcp""
      }
    }
  }
  ```

- Use Python to run the module directly (preferred method)
  ```json
  {
    ""mcpServers"": {
      ""fantasy-pl"": {
        ""command"": ""python"",
        ""args"": [""-m"", ""fpl_mcp""]
      }
    }
  }
  ```

#### 2. Server disconnects immediately

If the server starts but immediately disconnects:

- Check logs at `~/Library/Logs/Claude/mcp*.log` (macOS) or `%APPDATA%\Claude\logs\mcp*.log` (Windows)
- Ensure all dependencies are installed
- Try running the server manually with `python -m fpl_mcp` to see any errors

#### 3. Server not showing in Claude Desktop

If the hammer icon doesn't appear:

- Restart Claude Desktop completely
- Verify your `claude_desktop_config.json` has correct JSON syntax
- Ensure the path to Python or the executable is absolute, not relative

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

For more details, please refer to the [CONTRIBUTING.md](CONTRIBUTING.md) file.

## Acknowledgments

- [Fantasy Premier League API](https://fantasy.premierleague.com/api/) for providing the data
- [Model Context Protocol](https://modelcontextprotocol.io/) for the connectivity standard
- [Claude](https://claude.ai/) for the AI assistant capabilities

## Citation

If you use this package in your research or project, please consider citing it:

```bibtex
@software{fpl_mcp,
  author = {Jatia, Rishi and Fantasy PL MCP Contributors},
  title = {Fantasy Premier League MCP Server},
  url = {https://github.com/rishijatia/fantasy-pl-mcp},
  version = {0.1.0},
  year = {2025},
}
```
","Star
 19",2025-05-07 15:22:02.281957
https://github.com/qdrant/mcp-server-qdrant,qdrant/mcp-server-qdrant,"# mcp-server-qdrant: A Qdrant MCP server

[![smithery badge](https://smithery.ai/badge/mcp-server-qdrant)](https://smithery.ai/protocol/mcp-server-qdrant)

> The [Model Context Protocol (MCP)](https://modelcontextprotocol.io/introduction) is an open protocol that enables
> seamless integration between LLM applications and external data sources and tools. Whether you're building an
> AI-powered IDE, enhancing a chat interface, or creating custom AI workflows, MCP provides a standardized way to
> connect LLMs with the context they need.

This repository is an example of how to create a MCP server for [Qdrant](https://qdrant.tech/), a vector search engine.

<a href=""https://glama.ai/mcp/servers/9ejy5scw5i""><img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/9ejy5scw5i/badge"" alt=""mcp-server-qdrant MCP server"" /></a>

## Overview

An official Model Context Protocol server for keeping and retrieving memories in the Qdrant vector search engine.
It acts as a semantic memory layer on top of the Qdrant database.

## Components

### Tools

1. `qdrant-store`
   - Store some information in the Qdrant database
   - Input:
     - `information` (string): Information to store
     - `metadata` (JSON): Optional metadata to store
     - `collection_name` (string): Name of the collection to store the information in. This field is required if there are no default collection name.
                                   If there is a default collection name, this field is not enabled.
   - Returns: Confirmation message
2. `qdrant-find`
   - Retrieve relevant information from the Qdrant database
   - Input:
     - `query` (string): Query to use for searching
     - `collection_name` (string): Name of the collection to store the information in. This field is required if there are no default collection name.
                                   If there is a default collection name, this field is not enabled.
   - Returns: Information stored in the Qdrant database as separate messages

## Environment Variables

The configuration of the server is done using environment variables:

| Name                     | Description                                                         | Default Value                                                     |
|--------------------------|---------------------------------------------------------------------|-------------------------------------------------------------------|
| `QDRANT_URL`             | URL of the Qdrant server                                            | None                                                              |
| `QDRANT_API_KEY`         | API key for the Qdrant server                                       | None                                                              |
| `COLLECTION_NAME`        | Name of the default collection to use.                              | None                                                              |
| `QDRANT_LOCAL_PATH`      | Path to the local Qdrant database (alternative to `QDRANT_URL`)     | None                                                              |
| `EMBEDDING_PROVIDER`     | Embedding provider to use (currently only ""fastembed"" is supported) | `fastembed`                                                       |
| `EMBEDDING_MODEL`        | Name of the embedding model to use                                  | `sentence-transformers/all-MiniLM-L6-v2`                          |
| `TOOL_STORE_DESCRIPTION` | Custom description for the store tool                               | See default in [`settings.py`](src/mcp_server_qdrant/settings.py) |
| `TOOL_FIND_DESCRIPTION`  | Custom description for the find tool                                | See default in [`settings.py`](src/mcp_server_qdrant/settings.py) |

Note: You cannot provide both `QDRANT_URL` and `QDRANT_LOCAL_PATH` at the same time.

> [!IMPORTANT]
> Command-line arguments are not supported anymore! Please use environment variables for all configuration.

## Installation

### Using uvx

When using [`uvx`](https://docs.astral.sh/uv/guides/tools/#running-tools) no specific installation is needed to directly run *mcp-server-qdrant*.

```shell
QDRANT_URL=""http://localhost:6333"" \
COLLECTION_NAME=""my-collection"" \
EMBEDDING_MODEL=""sentence-transformers/all-MiniLM-L6-v2"" \
uvx mcp-server-qdrant
```

#### Transport Protocols

The server supports different transport protocols that can be specified using the `--transport` flag:

```shell
QDRANT_URL=""http://localhost:6333"" \
COLLECTION_NAME=""my-collection"" \
uvx mcp-server-qdrant --transport sse
```

Supported transport protocols:

- `stdio` (default): Standard input/output transport, might only be used by local MCP clients
- `sse`: Server-Sent Events transport, perfect for remote clients

The default transport is `stdio` if not specified.

### Using Docker

A Dockerfile is available for building and running the MCP server:

```bash
# Build the container
docker build -t mcp-server-qdrant .

# Run the container
docker run -p 8000:8000 \
  -e QDRANT_URL=""http://your-qdrant-server:6333"" \
  -e QDRANT_API_KEY=""your-api-key"" \
  -e COLLECTION_NAME=""your-collection"" \
  mcp-server-qdrant
```

### Installing via Smithery

To install Qdrant MCP Server for Claude Desktop automatically via [Smithery](https://smithery.ai/protocol/mcp-server-qdrant):

```bash
npx @smithery/cli install mcp-server-qdrant --client claude
```

### Manual configuration of Claude Desktop

To use this server with the Claude Desktop app, add the following configuration to the ""mcpServers"" section of your
`claude_desktop_config.json`:

```json
{
  ""qdrant"": {
    ""command"": ""uvx"",
    ""args"": [""mcp-server-qdrant""],
    ""env"": {
      ""QDRANT_URL"": ""https://xyz-example.eu-central.aws.cloud.qdrant.io:6333"",
      ""QDRANT_API_KEY"": ""your_api_key"",
      ""COLLECTION_NAME"": ""your-collection-name"",
      ""EMBEDDING_MODEL"": ""sentence-transformers/all-MiniLM-L6-v2""
    }
  }
}
```

For local Qdrant mode:

```json
{
  ""qdrant"": {
    ""command"": ""uvx"",
    ""args"": [""mcp-server-qdrant""],
    ""env"": {
      ""QDRANT_LOCAL_PATH"": ""/path/to/qdrant/database"",
      ""COLLECTION_NAME"": ""your-collection-name"",
      ""EMBEDDING_MODEL"": ""sentence-transformers/all-MiniLM-L6-v2""
    }
  }
}
```

This MCP server will automatically create a collection with the specified name if it doesn't exist.

By default, the server will use the `sentence-transformers/all-MiniLM-L6-v2` embedding model to encode memories.
For the time being, only [FastEmbed](https://qdrant.github.io/fastembed/) models are supported.

## Support for other tools

This MCP server can be used with any MCP-compatible client. For example, you can use it with
[Cursor](https://docs.cursor.com/context/model-context-protocol) and [VS Code](https://code.visualstudio.com/docs), which provide built-in support for the Model Context
Protocol.

### Using with Cursor/Windsurf

You can configure this MCP server to work as a code search tool for Cursor or Windsurf by customizing the tool
descriptions:

```bash
QDRANT_URL=""http://localhost:6333"" \
COLLECTION_NAME=""code-snippets"" \
TOOL_STORE_DESCRIPTION=""Store reusable code snippets for later retrieval. \
The 'information' parameter should contain a natural language description of what the code does, \
while the actual code should be included in the 'metadata' parameter as a 'code' property. \
The value of 'metadata' is a Python dictionary with strings as keys. \
Use this whenever you generate some code snippet."" \
TOOL_FIND_DESCRIPTION=""Search for relevant code snippets based on natural language descriptions. \
The 'query' parameter should describe what you're looking for, \
and the tool will return the most relevant code snippets. \
Use this when you need to find existing code snippets for reuse or reference."" \
uvx mcp-server-qdrant --transport sse # Enable SSE transport
```

In Cursor/Windsurf, you can then configure the MCP server in your settings by pointing to this running server using
SSE transport protocol. The description on how to add an MCP server to Cursor can be found in the [Cursor
documentation](https://docs.cursor.com/context/model-context-protocol#adding-an-mcp-server-to-cursor). If you are
running Cursor/Windsurf locally, you can use the following URL:

```
http://localhost:8000/sse
```

> [!TIP]
> We suggest SSE transport as a preferred way to connect Cursor/Windsurf to the MCP server, as it can support remote
> connections. That makes it easy to share the server with your team or use it in a cloud environment.

This configuration transforms the Qdrant MCP server into a specialized code search tool that can:

1. Store code snippets, documentation, and implementation details
2. Retrieve relevant code examples based on semantic search
3. Help developers find specific implementations or usage patterns

You can populate the database by storing natural language descriptions of code snippets (in the `information` parameter)
along with the actual code (in the `metadata.code` property), and then search for them using natural language queries
that describe what you're looking for.

> [!NOTE]
> The tool descriptions provided above are examples and may need to be customized for your specific use case. Consider
> adjusting the descriptions to better match your team's workflow and the specific types of code snippets you want to
> store and retrieve.

**If you have successfully installed the `mcp-server-qdrant`, but still can't get it to work with Cursor, please
consider creating the [Cursor rules](https://docs.cursor.com/context/rules-for-ai) so the MCP tools are always used when
the agent produces a new code snippet.** You can restrict the rules to only work for certain file types, to avoid using
the MCP server for the documentation or other types of content.

### Using with Claude Code

You can enhance Claude Code's capabilities by connecting it to this MCP server, enabling semantic search over your
existing codebase.

#### Setting up mcp-server-qdrant

1. Add the MCP server to Claude Code:

    ```shell
    # Add mcp-server-qdrant configured for code search
    claude mcp add code-search \
    -e QDRANT_URL=""http://localhost:6333"" \
    -e COLLECTION_NAME=""code-repository"" \
    -e EMBEDDING_MODEL=""sentence-transformers/all-MiniLM-L6-v2"" \
    -e TOOL_STORE_DESCRIPTION=""Store code snippets with descriptions. The 'information' parameter should contain a natural language description of what the code does, while the actual code should be included in the 'metadata' parameter as a 'code' property."" \
    -e TOOL_FIND_DESCRIPTION=""Search for relevant code snippets using natural language. The 'query' parameter should describe the functionality you're looking for."" \
    -- uvx mcp-server-qdrant
    ```

2. Verify the server was added:

    ```shell
    claude mcp list
    ```

#### Using Semantic Code Search in Claude Code

Tool descriptions, specified in `TOOL_STORE_DESCRIPTION` and `TOOL_FIND_DESCRIPTION`, guide Claude Code on how to use
the MCP server. The ones provided above are examples and may need to be customized for your specific use case. However,
Claude Code should be already able to:

1. Use the `qdrant-store` tool to store code snippets with descriptions.
2. Use the `qdrant-find` tool to search for relevant code snippets using natural language.

### Run MCP server in Development Mode

The MCP server can be run in development mode using the `mcp dev` command. This will start the server and open the MCP
inspector in your browser.

```shell
COLLECTION_NAME=mcp-dev mcp dev src/mcp_server_qdrant/server.py
```

### Using with VS Code

For one-click installation, click one of the install buttons below:

[![Install with UVX in VS Code](https://img.shields.io/badge/VS_Code-UVX-0098FF?style=flat-square&logo=visualstudiocode&logoColor=white)](https://insiders.vscode.dev/redirect/mcp/install?name=qdrant&config=%7B%22command%22%3A%22uvx%22%2C%22args%22%3A%5B%22mcp-server-qdrant%22%5D%2C%22env%22%3A%7B%22QDRANT_URL%22%3A%22%24%7Binput%3AqdrantUrl%7D%22%2C%22QDRANT_API_KEY%22%3A%22%24%7Binput%3AqdrantApiKey%7D%22%2C%22COLLECTION_NAME%22%3A%22%24%7Binput%3AcollectionName%7D%22%7D%7D&inputs=%5B%7B%22type%22%3A%22promptString%22%2C%22id%22%3A%22qdrantUrl%22%2C%22description%22%3A%22Qdrant+URL%22%7D%2C%7B%22type%22%3A%22promptString%22%2C%22id%22%3A%22qdrantApiKey%22%2C%22description%22%3A%22Qdrant+API+Key%22%2C%22password%22%3Atrue%7D%2C%7B%22type%22%3A%22promptString%22%2C%22id%22%3A%22collectionName%22%2C%22description%22%3A%22Collection+Name%22%7D%5D) [![Install with UVX in VS Code Insiders](https://img.shields.io/badge/VS_Code_Insiders-UVX-24bfa5?style=flat-square&logo=visualstudiocode&logoColor=white)](https://insiders.vscode.dev/redirect/mcp/install?name=qdrant&config=%7B%22command%22%3A%22uvx%22%2C%22args%22%3A%5B%22mcp-server-qdrant%22%5D%2C%22env%22%3A%7B%22QDRANT_URL%22%3A%22%24%7Binput%3AqdrantUrl%7D%22%2C%22QDRANT_API_KEY%22%3A%22%24%7Binput%3AqdrantApiKey%7D%22%2C%22COLLECTION_NAME%22%3A%22%24%7Binput%3AcollectionName%7D%22%7D%7D&inputs=%5B%7B%22type%22%3A%22promptString%22%2C%22id%22%3A%22qdrantUrl%22%2C%22description%22%3A%22Qdrant+URL%22%7D%2C%7B%22type%22%3A%22promptString%22%2C%22id%22%3A%22qdrantApiKey%22%2C%22description%22%3A%22Qdrant+API+Key%22%2C%22password%22%3Atrue%7D%2C%7B%22type%22%3A%22promptString%22%2C%22id%22%3A%22collectionName%22%2C%22description%22%3A%22Collection+Name%22%7D%5D&quality=insiders)

[![Install with Docker in VS Code](https://img.shields.io/badge/VS_Code-Docker-0098FF?style=flat-square&logo=visualstudiocode&logoColor=white)](https://insiders.vscode.dev/redirect/mcp/install?name=qdrant&config=%7B%22command%22%3A%22docker%22%2C%22args%22%3A%5B%22run%22%2C%22-p%22%2C%228000%3A8000%22%2C%22-i%22%2C%22--rm%22%2C%22-e%22%2C%22QDRANT_URL%22%2C%22-e%22%2C%22QDRANT_API_KEY%22%2C%22-e%22%2C%22COLLECTION_NAME%22%2C%22mcp-server-qdrant%22%5D%2C%22env%22%3A%7B%22QDRANT_URL%22%3A%22%24%7Binput%3AqdrantUrl%7D%22%2C%22QDRANT_API_KEY%22%3A%22%24%7Binput%3AqdrantApiKey%7D%22%2C%22COLLECTION_NAME%22%3A%22%24%7Binput%3AcollectionName%7D%22%7D%7D&inputs=%5B%7B%22type%22%3A%22promptString%22%2C%22id%22%3A%22qdrantUrl%22%2C%22description%22%3A%22Qdrant+URL%22%7D%2C%7B%22type%22%3A%22promptString%22%2C%22id%22%3A%22qdrantApiKey%22%2C%22description%22%3A%22Qdrant+API+Key%22%2C%22password%22%3Atrue%7D%2C%7B%22type%22%3A%22promptString%22%2C%22id%22%3A%22collectionName%22%2C%22description%22%3A%22Collection+Name%22%7D%5D) [![Install with Docker in VS Code Insiders](https://img.shields.io/badge/VS_Code_Insiders-Docker-24bfa5?style=flat-square&logo=visualstudiocode&logoColor=white)](https://insiders.vscode.dev/redirect/mcp/install?name=qdrant&config=%7B%22command%22%3A%22docker%22%2C%22args%22%3A%5B%22run%22%2C%22-p%22%2C%228000%3A8000%22%2C%22-i%22%2C%22--rm%22%2C%22-e%22%2C%22QDRANT_URL%22%2C%22-e%22%2C%22QDRANT_API_KEY%22%2C%22-e%22%2C%22COLLECTION_NAME%22%2C%22mcp-server-qdrant%22%5D%2C%22env%22%3A%7B%22QDRANT_URL%22%3A%22%24%7Binput%3AqdrantUrl%7D%22%2C%22QDRANT_API_KEY%22%3A%22%24%7Binput%3AqdrantApiKey%7D%22%2C%22COLLECTION_NAME%22%3A%22%24%7Binput%3AcollectionName%7D%22%7D%7D&inputs=%5B%7B%22type%22%3A%22promptString%22%2C%22id%22%3A%22qdrantUrl%22%2C%22description%22%3A%22Qdrant+URL%22%7D%2C%7B%22type%22%3A%22promptString%22%2C%22id%22%3A%22qdrantApiKey%22%2C%22description%22%3A%22Qdrant+API+Key%22%2C%22password%22%3Atrue%7D%2C%7B%22type%22%3A%22promptString%22%2C%22id%22%3A%22collectionName%22%2C%22description%22%3A%22Collection+Name%22%7D%5D&quality=insiders)

#### Manual Installation

Add the following JSON block to your User Settings (JSON) file in VS Code. You can do this by pressing `Ctrl + Shift + P` and typing `Preferences: Open User Settings (JSON)`.

```json
{
  ""mcp"": {
    ""inputs"": [
      {
        ""type"": ""promptString"",
        ""id"": ""qdrantUrl"",
        ""description"": ""Qdrant URL""
      },
      {
        ""type"": ""promptString"",
        ""id"": ""qdrantApiKey"",
        ""description"": ""Qdrant API Key"",
        ""password"": true
      },
      {
        ""type"": ""promptString"",
        ""id"": ""collectionName"",
        ""description"": ""Collection Name""
      }
    ],
    ""servers"": {
      ""qdrant"": {
        ""command"": ""uvx"",
        ""args"": [""mcp-server-qdrant""],
        ""env"": {
          ""QDRANT_URL"": ""${input:qdrantUrl}"",
          ""QDRANT_API_KEY"": ""${input:qdrantApiKey}"",
          ""COLLECTION_NAME"": ""${input:collectionName}""
        }
      }
    }
  }
}
```

Or if you prefer using Docker, add this configuration instead:

```json
{
  ""mcp"": {
    ""inputs"": [
      {
        ""type"": ""promptString"",
        ""id"": ""qdrantUrl"",
        ""description"": ""Qdrant URL""
      },
      {
        ""type"": ""promptString"",
        ""id"": ""qdrantApiKey"",
        ""description"": ""Qdrant API Key"",
        ""password"": true
      },
      {
        ""type"": ""promptString"",
        ""id"": ""collectionName"",
        ""description"": ""Collection Name""
      }
    ],
    ""servers"": {
      ""qdrant"": {
        ""command"": ""docker"",
        ""args"": [
          ""run"",
          ""-p"", ""8000:8000"",
          ""-i"",
          ""--rm"",
          ""-e"", ""QDRANT_URL"",
          ""-e"", ""QDRANT_API_KEY"",
          ""-e"", ""COLLECTION_NAME"",
          ""mcp-server-qdrant""
        ],
        ""env"": {
          ""QDRANT_URL"": ""${input:qdrantUrl}"",
          ""QDRANT_API_KEY"": ""${input:qdrantApiKey}"",
          ""COLLECTION_NAME"": ""${input:collectionName}""
        }
      }
    }
  }
}
```

Alternatively, you can create a `.vscode/mcp.json` file in your workspace with the following content:

```json
{
  ""inputs"": [
    {
      ""type"": ""promptString"",
      ""id"": ""qdrantUrl"",
      ""description"": ""Qdrant URL""
    },
    {
      ""type"": ""promptString"",
      ""id"": ""qdrantApiKey"",
      ""description"": ""Qdrant API Key"",
      ""password"": true
    },
    {
      ""type"": ""promptString"",
      ""id"": ""collectionName"",
      ""description"": ""Collection Name""
    }
  ],
  ""servers"": {
    ""qdrant"": {
      ""command"": ""uvx"",
      ""args"": [""mcp-server-qdrant""],
      ""env"": {
        ""QDRANT_URL"": ""${input:qdrantUrl}"",
        ""QDRANT_API_KEY"": ""${input:qdrantApiKey}"",
        ""COLLECTION_NAME"": ""${input:collectionName}""
      }
    }
  }
}
```

For workspace configuration with Docker, use this in `.vscode/mcp.json`:

```json
{
  ""inputs"": [
    {
      ""type"": ""promptString"",
      ""id"": ""qdrantUrl"",
      ""description"": ""Qdrant URL""
    },
    {
      ""type"": ""promptString"",
      ""id"": ""qdrantApiKey"",
      ""description"": ""Qdrant API Key"",
      ""password"": true
    },
    {
      ""type"": ""promptString"",
      ""id"": ""collectionName"",
      ""description"": ""Collection Name""
    }
  ],
  ""servers"": {
    ""qdrant"": {
      ""command"": ""docker"",
      ""args"": [
        ""run"",
        ""-p"", ""8000:8000"",
        ""-i"",
        ""--rm"",
        ""-e"", ""QDRANT_URL"",
        ""-e"", ""QDRANT_API_KEY"",
        ""-e"", ""COLLECTION_NAME"",
        ""mcp-server-qdrant""
      ],
      ""env"": {
        ""QDRANT_URL"": ""${input:qdrantUrl}"",
        ""QDRANT_API_KEY"": ""${input:qdrantApiKey}"",
        ""COLLECTION_NAME"": ""${input:collectionName}""
      }
    }
  }
}
```

## Contributing

If you have suggestions for how mcp-server-qdrant could be improved, or want to report a bug, open an issue!
We'd love all and any contributions.

### Testing `mcp-server-qdrant` locally

The [MCP inspector](https://github.com/modelcontextprotocol/inspector) is a developer tool for testing and debugging MCP
servers. It runs both a client UI (default port 5173) and an MCP proxy server (default port 3000). Open the client UI in
your browser to use the inspector.

```shell
QDRANT_URL="":memory:"" COLLECTION_NAME=""test"" \
mcp dev src/mcp_server_qdrant/server.py
```

Once started, open your browser to http://localhost:5173 to access the inspector interface.

## License

This MCP server is licensed under the Apache License 2.0. This means you are free to use, modify, and distribute the
software, subject to the terms and conditions of the Apache License 2.0. For more details, please see the LICENSE file
in the project repository.
","Star
 546",2025-05-07 15:22:02.281957
https://github.com/TaazKareem/clickup-mcp-server,TaazKareem/clickup-mcp-server,"<img src=""assets/images/clickup_mcp_server_social_image.png"" alt=""ClickUp MCP Server"" width=""100%"">

![Total Supporters](https://img.shields.io/badge/🏆%20Total%20Supporters-4-gold)
[![GitHub Stars](https://img.shields.io/github/stars/TaazKareem/clickup-mcp-server?style=flat&logo=github)](https://github.com/TaazKareem/clickup-mcp-server/stargazers)
[![Maintenance](https://img.shields.io/badge/Maintained%3F-yes-brightgreen.svg)](https://github.com/TaazKareem/clickup-mcp-server/graphs/commit-activity)

A Model Context Protocol (MCP) server for integrating ClickUp tasks with AI applications. This server allows AI agents to interact with ClickUp tasks, spaces, lists, and folders through a standardized protocol.

> 🚀 **Status Update:** v0.7.2 now available with complete Time Tracking support and Document Management features.

## Setup

1. Get your credentials:
   - ClickUp API key from [ClickUp Settings](https://app.clickup.com/settings/apps)
   - Team ID from your ClickUp workspace URL
2. Choose either hosted installation (sends webhooks) or NPX installation (downloads to local path and installs dependencies)
3. Use natural language to manage your workspace!

## Smithery Installation (Quick Start)

[![smithery badge](https://smithery.ai/badge/@taazkareem/clickup-mcp-server)](https://smithery.ai/server/@TaazKareem/clickup-mcp-server)

The server is hosted on [Smithery](https://smithery.ai/server/@taazkareem/clickup-mcp-server). There, you can preview the available tools or copy the commands to run on your specific client app.

## NPX Installation

[![NPM Version](https://img.shields.io/npm/v/@taazkareem/clickup-mcp-server.svg?style=flat&logo=npm)](https://www.npmjs.com/package/@taazkareem/clickup-mcp-server)
[![Dependency Status](https://img.shields.io/badge/dependencies-up%20to%20date-brightgreen)](https://github.com/TaazKareem/clickup-mcp-server/blob/main/package.json)
[![NPM Downloads](https://img.shields.io/npm/dm/@taazkareem/clickup-mcp-server.svg?style=flat&logo=npm)](https://npmcharts.com/compare/@taazkareem/clickup-mcp-server?minimal=true)

Add this entry to your client's MCP settings JSON file:

```json
{
  ""mcpServers"": {
    ""ClickUp"": {
      ""command"": ""npx"",
      ""args"": [
        ""-y"",
        ""@taazkareem/clickup-mcp-server@latest""
      ],
      ""env"": {
        ""CLICKUP_API_KEY"": ""your-api-key"",
        ""CLICKUP_TEAM_ID"": ""your-team-id"",
        ""DOCUMENT_SUPPORT"": ""true""
      }
    }
  }
}
```

Or use this npx command:

`npx -y @taazkareem/clickup-mcp-server@latest --env CLICKUP_API_KEY=your-api-key --env CLICKUP_TEAM_ID=your-team-id`

**Obs: if you don't pass ""DOCUMENT_SUPPORT"": ""true"", the default is false and document support will not be active.**

Additionally, you can use the `DISABLED_TOOLS` environment variable or `--env DISABLED_TOOLS` argument to disable specific tools. Provide a comma-separated list of tool names to disable (e.g., `create_task,delete_task`).

Please disable tools you don't need if you are having issues with the number of tools or any context limitations

## Features

| 📝 Task Management                                                                                                                                                                                                                                                   | 🏷️ Tag Management                                                                                                                                                                                                                                                        |
| -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| • Create, update, and delete tasks<br>• Move and duplicate tasks anywhere<br>• Support for single and bulk operations<br>• Set start/due dates with natural language<br>• Create and manage subtasks<br>• Add comments and attachments | • Create, update, and delete space tags<br>• Add and remove tags from tasks<br>• Use natural language color commands<br>• Automatic contrasting foreground colors<br>• View all space tags<br>• Tag-based task organization across workspace |
| ⏱️ **Time Tracking**                                                                                                                                                                                                                                          | 🌳 **Workspace Organization**                                                                                                                                                                                                                                         |
| • View time entries for tasks<br>• Start/stop time tracking on tasks<br>• Add manual time entries<br>• Delete time entries<br>• View currently running timer<br>• Track billable and non-billable time                                 | • Navigate spaces, folders, and lists<br>• Create and manage folders<br>• Organize lists within spaces<br>• Create lists in folders<br>• View workspace hierarchy<br>• Efficient path navigation                                             |
| 📄 **Document Management**                                                                                                                                                                                                                                      | ⚡ **Integration Features**                                                                                                                                                                                                                                           |
| • Document Listing through all workspace<br>• Document Page listing<br>• Document Page Details<br>• Document Creation<br>• Document page update (append & prepend)                                                                       | • Global name or ID-based lookups<br>• Case-insensitive matching<br>• Markdown formatting support<br>• Built-in rate limiting<br>• Error handling and validation<br>• Comprehensive API coverage                                             |

## Available Tools

| Tool                                                               | Description                     | Required Parameters                                                                                                          |
| ------------------------------------------------------------------ | ------------------------------- | ---------------------------------------------------------------------------------------------------------------------------- |
| [get_workspace_hierarchy](docs/api-reference.md#workspace-navigation) | Get workspace structure         | None                                                                                                                         |
| [create_task](docs/api-reference.md#task-management)                  | Create a task                   | `name`, (`listId`/`listName`)                                                                                          |
| [create_bulk_tasks](docs/api-reference.md#task-management)            | Create multiple tasks           | `tasks[]`                                                                                                                  |
| [update_task](docs/api-reference.md#task-management)                  | Modify task                     | `taskId`/`taskName`                                                                                                      |
| [update_bulk_tasks](docs/api-reference.md#task-management)            | Update multiple tasks           | `tasks[]` with IDs or names                                                                                                |
| [get_tasks](docs/api-reference.md#task-management)                    | Get tasks from list             | `listId`/`listName`                                                                                                      |
| [get_task](docs/api-reference.md#task-management)                     | Get single task details         | `taskId`/`taskName` (with smart disambiguation)                                                                          |
| [get_workspace_tasks](docs/api-reference.md#task-management)          | Get tasks with filtering        | At least one filter (tags, list_ids, space_ids, etc.)                                                                        |
| [get_task_comments](docs/api-reference.md#task-management)            | Get comments on a task          | `taskId`/`taskName`                                                                                                      |
| [create_task_comment](docs/api-reference.md#task-management)          | Add a comment to a task         | `commentText`, (`taskId`/(`taskName`+`listName`))                                                                    |
| [attach_task_file](docs/api-reference.md#task-management)             | Attach file to a task           | `taskId`/`taskName`, (`file_data` or `file_url`)                                                                     |
| [delete_task](docs/api-reference.md#task-management)                  | Remove task                     | `taskId`/`taskName`                                                                                                      |
| [delete_bulk_tasks](docs/api-reference.md#task-management)            | Remove multiple tasks           | `tasks[]` with IDs or names                                                                                                |
| [move_task](docs/api-reference.md#task-management)                    | Move task                       | `taskId`/`taskName`, `listId`/`listName`                                                                             |
| [move_bulk_tasks](docs/api-reference.md#task-management)              | Move multiple tasks             | `tasks[]` with IDs or names, target list                                                                                   |
| [duplicate_task](docs/api-reference.md#task-management)               | Copy task                       | `taskId`/`taskName`, `listId`/`listName`                                                                             |
| [create_list](docs/api-reference.md#list-management)                  | Create list in space            | `name`, `spaceId`/`spaceName`                                                                                          |
| [create_folder](docs/api-reference.md#folder-management)              | Create folder                   | `name`, `spaceId`/`spaceName`                                                                                          |
| [create_list_in_folder](docs/api-reference.md#list-management)        | Create list in folder           | `name`, `folderId`/`folderName`                                                                                        |
| [get_folder](docs/api-reference.md#folder-management)                 | Get folder details              | `folderId`/`folderName`                                                                                                  |
| [update_folder](docs/api-reference.md#folder-management)              | Update folder properties        | `folderId`/`folderName`                                                                                                  |
| [delete_folder](docs/api-reference.md#folder-management)              | Delete folder                   | `folderId`/`folderName`                                                                                                  |
| [get_list](docs/api-reference.md#list-management)                     | Get list details                | `listId`/`listName`                                                                                                      |
| [update_list](docs/api-reference.md#list-management)                  | Update list properties          | `listId`/`listName`                                                                                                      |
| [delete_list](docs/api-reference.md#list-management)                  | Delete list                     | `listId`/`listName`                                                                                                      |
| [get_space_tags](docs/api-reference.md#tag-management)                | Get space tags                  | `spaceId`/`spaceName`                                                                                                    |
| [create_space_tag](docs/api-reference.md#tag-management)              | Create tag                      | `tagName`, `spaceId`/`spaceName`                                                                                       |
| [update_space_tag](docs/api-reference.md#tag-management)              | Update tag                      | `tagName`, `spaceId`/`spaceName`                                                                                       |
| [delete_space_tag](docs/api-reference.md#tag-management)              | Delete tag                      | `tagName`, `spaceId`/`spaceName`                                                                                       |
| [add_tag_to_task](docs/api-reference.md#tag-management)               | Add tag to task                 | `tagName`, `taskId`/(`taskName`+`listName`)                                                                          |
| [remove_tag_from_task](docs/api-reference.md#tag-management)          | Remove tag from task            | `tagName`, `taskId`/(`taskName`+`listName`)                                                                          |
| [get_task_time_entries](docs/api-reference.md#time-tracking)          | Get time entries for a task     | `taskId`/`taskName`                                                                                                      |
| [start_time_tracking](docs/api-reference.md#time-tracking)            | Start time tracking on a task   | `taskId`/`taskName`                                                                                                      |
| [stop_time_tracking](docs/api-reference.md#time-tracking)             | Stop current time tracking      | None                                                                                                                         |
| [add_time_entry](docs/api-reference.md#time-tracking)                 | Add manual time entry to a task | `taskId`/`taskName`, `start`, `duration`                                                                             |
| [delete_time_entry](docs/api-reference.md#time-tracking)              | Delete a time entry             | `timeEntryId`                                                                                                              |
| [get_current_time_entry](docs/api-reference.md#time-tracking)         | Get currently running timer     | None                                                                                                                         |
| [create_document](docs/api-reference.md#document-management)          | Create a document               | `workspaceId`, `name`, `parentId`/`parentType`, `visibility`, `create_pages`                                     |
| [get_document](docs/api-reference.md#document-management)             | Get a document                  | `workspaceId`/`documentId`                                                                                               |
| [list_documents](docs/api-reference.md#document-management)           | List documents                  | `workspaceId`, `documentId`/`creator`/`deleted`/`archived`/`parent_id`/`parent_type`/`limit`/`next_cursor` |
| [list_document_pages](docs/api-reference.md#document-management)      | List document pages             | `documentId`/`documentName`                                                                                              |
| [get_document_pages](docs/api-reference.md#document-management)       | Get document pages              | `documentId`/`documentName`, `pageIds`                                                                                 |
| [create_document_pages](docs/api-reference.md#document-management)    | Create a document page          | `workspaceId`/`documentId`, `parent_page_id`/`name`/`sub_title`,`content`/`content_format`                     |
| [update_document_page](docs/api-reference.md#document-management)     | Update a document page          | `workspaceId`/`documentId`, `name`/`sub_title`,`content`/`content_edit_mode`/`content_format`                  |

See [full documentation](docs/api-reference.md) for optional parameters and advanced usage.

## Prompts

Not yet implemented and not supported by all client apps. Request a feature for a Prompt implementation that would be most beneficial for your workflow (without it being too specific). Examples:

| Prompt                                             | Purpose                   | Features                                  |
| -------------------------------------------------- | ------------------------- | ----------------------------------------- |
| [summarize_tasks](docs/api-reference.md#prompts)      | Task overview             | Status summary, priorities, relationships |
| [analyze_priorities](docs/api-reference.md#prompts)   | Priority optimization     | Distribution analysis, sequencing         |
| [generate_description](docs/api-reference.md#prompts) | Task description creation | Objectives, criteria, dependencies        |

## Error Handling

The server provides clear error messages for:

- Missing required parameters
- Invalid IDs or names
- Items not found
- Permission issues
- API errors
- Rate limiting

The `LOG_LEVEL` environment variable can be specified to control the verbosity of server logs. Valid values are `trace`, `debug`, `info`, `warn`, and `error` (default).
This can be also be specified on the command line as, e.g. `--env LOG_LEVEL=info`.

## Support the Developer

When using this server, you may occasionally see a small sponsor message with a link to this repository included in tool responses. I hope you can support the project!
If you find this project useful, please consider supporting:

[![Sponsor TaazKareem](https://img.shields.io/badge/Sponsor-TaazKareem-orange?logo=github)](https://github.com/sponsors/TaazKareem)

<a href=""https://buymeacoffee.com/taazkareem"">
  <img src=""https://cdn.buymeacoffee.com/buttons/v2/default-yellow.png"" width=""200"" alt=""Buy Me A Coffee"">
</a>

## Acknowledgements

Special thanks to [ClickUp](https://clickup.com) for their excellent API and services that make this integration possible.

## Contributing

Contributions are welcome! Please read our [Contributing Guide](CONTRIBUTING.md) for details.

## License

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Disclaimer

This software makes use of third-party APIs and may reference trademarks
or brands owned by third parties. The use of such APIs or references does not imply
any affiliation with or endorsement by the respective companies. All trademarks and
brand names are the property of their respective owners. This project is an independent
work and is not officially associated with or sponsored by any third-party company mentioned.
","Star
 133",2025-05-07 15:22:02.281957
https://github.com/neo4j-contrib/mcp-neo4j,neo4j-contrib/mcp-neo4j,"# Neo4j MCP Clients & Servers

Model Context Protocol (MCP) is a [standardized protocol](https://modelcontextprotocol.io/introduction) for managing context between large language models (LLMs) and external systems. 

This lets you use Claude Desktop, or any other MCP Client (VS Code, Cursor, Windsurf), to use natural language to accomplish things with Neo4j and your Aura account, e.g.:

* What is in this graph?
* Render a chart from the top products sold by frequency, total and average volume
* List my instances
* Create a new instance named mcp-test for Aura Professional with 4GB and Graph Data Science enabled
* Store the fact that I worked on the Neo4j MCP Servers today with Andreas and Oskar

## Servers

### `mcp-neo4j-cypher` - natural language to Cypher queries

[Details in Readme](./servers/mcp-neo4j-cypher/)

Get database schema for a configured database and exeucte generated read and write Cypher queries on that database.

### `mcp-neo4j-memory` - knowledge graph memory stored in Neo4j

[Details in Readme](./servers/mcp-neo4j-memory/)

Store and retrieve entities and relationships from your personal knowledge graph in a local or remote Neo4j instance.
Access that information over different sessions, conversations, clients.

### `mcp-neo4j-cloud-aura-api` - Neo4j Aura cloud service management API

[Details in Readme](./servers/mcp-neo4j-cloud-aura-api//)

Manage your [Neo4j Aura](https://console.neo4j.io) instances directly from the comfort of your AI assistant chat.

Create and destroy instances, find instances by name, scale them up and down and enable features.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## Blog Posts

* [Everything a Developer Needs to Know About the Model Context Protocol (MCP)](https://neo4j.com/blog/developer/model-context-protocol/)
* [Claude Converses With Neo4j Via MCP - Graph Database & Analytics](https://neo4j.com/blog/developer/claude-converses-neo4j-via-mcp/)
* [Building Knowledge Graphs With Claude and Neo4j: A No-Code MCP Approach - Graph Database & Analytics](https://neo4j.com/blog/developer/knowledge-graphs-claude-neo4j-mcp/)

## License

MIT License","Star
 284",2025-05-07 15:22:02.281957
https://github.com/inkeep/mcp-server-python,inkeep/mcp-server-python,"# mcp-server-python
Inkeep MCP Server powered by your docs and product content.

### Dependencies

- An account on [Inkeep](https://inkeep.com) to manage and provide the RAG
- [`uv`](https://github.com/astral-sh/uv) Python project manager

### Local Setup

```
git clone https://github.com/inkeep/mcp-server-python.git
cd mcp-server-python
uv venv
uv pip install -r pyproject.toml
```

Note the full path of the project, referred to as `<YOUR_INKEEP_MCP_SERVER_ABSOLUTE_PATH>` in a later step.

## Get an API key

1. Log in to the [Inkeep Dashboard](https://portal.inkeep.com)
2. Navigate to the **Projects** section and select your project
3. Open the **Integrations** tab
4. Click **Create Integration** and choose **API** from the options
5. Enter a Name for your new API integration.
6. Click on **Create**
7. A generated **API key** will appear that you can use to authenticate API requests.

We'll refer to this API key as the `<YOUR_INKEEP_API_KEY>` in later steps.

### Add to your MCP client

Follow the steps in [this](https://modelcontextprotocol.io/quickstart/user) guide to setup Claude Dekstop.

In your `claude_desktop_config.json` file, add the following entry to `mcpServers`.

```json claude_desktop_config.json
{
    ""mcpServers"": {
        ""inkeep-mcp-server"": {
            ""command"": ""uv"",
            ""args"": [
                ""--directory"",
                ""<YOUR_INKEEP_MCP_SERVER_ABSOLUTE_PATH>"",
                ""run"",
                ""-m"",
                ""inkeep_mcp_server""
            ],
            ""env"": {
                ""INKEEP_API_BASE_URL"": ""https://api.inkeep.com/v1"",
                ""INKEEP_API_KEY"": ""<YOUR_INKEEP_API_KEY>"",
                ""INKEEP_API_MODEL"": ""inkeep-rag"",
                ""INKEEP_MCP_TOOL_NAME"": ""search-product-content"",
                ""INKEEP_MCP_TOOL_DESCRIPTION"": ""Retrieves product documentation about Inkeep. The query should be framed as a conversational question about Inkeep.""
            }
        },
    }
}
```

You may need to put the full path to the `uv` executable in the command field. You can get this by running `which uv` on MacOS/Linux or `where uv` on Windows.
","Star
 11",2025-05-07 15:22:02.281957
https://github.com/ognis1205/mcp-server-unitycatalog,ognis1205/mcp-server-unitycatalog,"# mcp-server-unitycatalog: An Unity Catalog MCP server

<p align=""center"" float=""left"">
  <img width=""256"" src=""https://raw.githubusercontent.com/ognis1205/mcp-server-unitycatalog/main/docs/vscode1.webp"" />
  <img width=""256"" src=""https://raw.githubusercontent.com/ognis1205/mcp-server-unitycatalog/main/docs/vscode2.webp"" />
  <img width=""256"" src=""https://raw.githubusercontent.com/ognis1205/mcp-server-unitycatalog/main/docs/vscode3.webp"" />
</p>

## Overview

A Model Context Protocol server for [Unity Catalog](https://www.unitycatalog.io/). This server provides [Unity Catalog Functions](https://docs.unitycatalog.io/usage/functions/) as MCP tools.

### Tools

You can use **all Unity Catalog Functions registered in Unity Catalog** alongside the following predefined Unity Catalog AI tools:

1. `uc_list_functions`
   - Lists functions within the specified parent catalog and schema.
   - Returns: A list of functions retrieved from Unity Catalog.

2. `uc_get_function`
   - Gets a function within a parent catalog and schema.
   - Input:
     - `name` (string): The name of the function (not fully-qualified).
   - Returns: A function details retrieved from Unity Catalog.

3. `uc_create_function`
   - Creates a function within a parent catalog and schema. **WARNING: This API is experimental and will change in future versions**.
   - Input:
     - `name` (string): The name of the function (not fully-qualified).
     - `script` (string): The Python script including the function to be registered.
   - Returns: A function details created within Unity Catalog.

4. `uc_delete_function`
   - Deletes a function within a parent catalog and schema.
   - Input:
     - `name` (string): The name of the function (not fully-qualified).
   - Returns: None.

## Installation

### Using uv

When using [`uv`](https://docs.astral.sh/uv/) no specific installation is needed. We will use
[`uvx`](https://docs.astral.sh/uv/guides/tools/) to directly run *mcp-server-git*.

## Configuration

These values can also be set via CLI options or `.env` environment variables. Required arguments are the Unity Catalog server, catalog, and schema, while the access token and verbosity level are optional. Run `uv run mcp-server-unitycatalog --help` for more detailed configuration options.

| Argument                   | Environment Variable | Description                                                                        | Required/Optional |
|----------------------------|----------------------|------------------------------------------------------------------------------------|-------------------|
| `-u`, `--uc_server`        | `UC_SERVER`          | The base URL of the Unity Catalog server.                                          | Required          |
| `-c`, `--uc_catalog`       | `UC_CATALOG`         | The name of the Unity Catalog catalog.                                             | Required          |
| `-s`, `--uc_schema`        | `UC_SCHEMA`          | The name of the schema within a Unity Catalog catalog.                             | Required          |
| `-t`, `--uc_token`         | `UC_TOKEN`           | The access token used to authorize API requests to the Unity Catalog server.       | Optional          |
| `-v`, `--uc_verbosity`     | `UC_VERBOSITY`       | The verbosity level for logging. Default: `warn`.                                  | Optional          |
| `-l`, `--uc_log_directory` | `UC_LOG_DIRECTORY`   | The directory where log files will be stored. Default: `.mcp_server_unitycatalog`. | Optional          |

### Usage with Claude Desktop or VSCode Cline

Add this to your `claude_desktop_config.json` (or `cline_mcp_settings.json`):

<details>
<summary>Using uv</summary>

```json
{
  ""mcpServers"": {
    ""unitycatalog"": {
      ""command"": ""uv"",
      ""args"": [
        ""--directory"",
        ""/<path to your local git repository>/mcp-server-unitycatalog"",
        ""run"",
        ""mcp-server-unitycatalog"",
        ""--uc_server"",
        ""<your unity catalog url>"",
        ""--uc_catalog"",
        ""<your catalog name>"",
        ""--uc_schema"",
        ""<your schema name>""
      ]
    }
  }
}
```
</details>

<details>
<summary>Using docker</summary>

* Note: replace '/Users/<USER>' with the a path that you want to be accessible by this tool

```json
{
  ""mcpServers"": {
    ""unitycatalog"": {
      ""command"": ""docker"",
      ""args"": [
        ""run"",
        ""--rm"",
        ""-i"",
        ""mcp/unitycatalog"",
        ""--uc_server"",
        ""<your unity catalog url>"",
        ""--uc_catalog"",
        ""<your catalog name>"",
        ""--uc_schema"",
        ""<your schema name>""
      ]
    }
  }
}
```
</details>

## Building

Docker:

```bash
docker build -t mcp/unitycatalog .   
```

## Future Plans

- [x] Implement support for `list_functions`.
- [x] Implement support for `get_function`.
- [x] Implement support for `create_python_function`.
- [x] Implement support for `execute_function`.
- [x] Implement support for `delete_function`.
- [ ] Implement semantic catalog explorer tools.
- [x] Add Docker image.
- [ ] Implement `use_xxx` methods. In the current implementation, `catalog` and `schema` need to be defined when starting the server. However, they will be implemented as `use_catalog` and `use_schema` functions, dynamically updating the list of available functions when the `use_xxx` is executed.

## License

This MCP server is licensed under the MIT License. This means you are free to use, modify, and distribute the software, subject to the terms and conditions of the MIT License. For more details, please see the LICENSE file in the project repository.
","Star
 12",2025-05-07 15:22:02.281957
https://github.com/elastic/mcp-server-elasticsearch,elastic/mcp-server-elasticsearch,"# Elasticsearch MCP Server

This repository contains experimental features intended for research and evaluation and are not production-ready.

Connect to your Elasticsearch data directly from any MCP Client (like Claude Desktop) using the Model Context Protocol (MCP).

This server connects agents to your Elasticsearch data using the Model Context Protocol. It allows you to interact with your Elasticsearch indices through natural language conversations.

<a href=""https://glama.ai/mcp/servers/@elastic/mcp-server-elasticsearch"">
  <img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/@elastic/mcp-server-elasticsearch/badge"" alt=""Elasticsearch Server MCP server"" />
</a>

## Available Tools

* `list_indices`: List all available Elasticsearch indices
* `get_mappings`: Get field mappings for a specific Elasticsearch index
* `search`: Perform an Elasticsearch search with the provided query DSL
* `get_shards`: Get shard information for all or specific indices

## Prerequisites

* An Elasticsearch instance
* Elasticsearch authentication credentials (API key or username/password)
* MCP Client (e.g. Claude Desktop)

## Demo

https://github.com/user-attachments/assets/5dd292e1-a728-4ca7-8f01-1380d1bebe0c

## Installation & Setup

### Using the Published NPM Package

> [!TIP]
> The easiest way to use Elasticsearch MCP Server is through the published npm package.

1. **Configure MCP Client**
   - Open your MCP Client. See the [list of MCP Clients](https://modelcontextprotocol.io/clients), here we are configuring Claude Desktop.
   - Go to **Settings > Developer > MCP Servers**
   - Click `Edit Config` and add a new MCP Server with the following configuration:

   ```json
   {
     ""mcpServers"": {
       ""elasticsearch-mcp-server"": {
         ""command"": ""npx"",
         ""args"": [
           ""-y"",
           ""@elastic/mcp-server-elasticsearch""
         ],
         ""env"": {
           ""ES_URL"": ""your-elasticsearch-url"",
           ""ES_API_KEY"": ""your-api-key""
         }
       }
     }
   }
   ```

2. **Start a Conversation**
   - Open a new conversation in your MCP Client
   - The MCP server should connect automatically
   - You can now ask questions about your Elasticsearch data

### Configuration Options

The Elasticsearch MCP Server supports configuration options to connect to your Elasticsearch:

> [!NOTE]
> You must provide either an API key or both username and password for authentication.


| Environment Variable | Description | Required |
|---------------------|-------------|----------|
| `ES_URL` | Your Elasticsearch instance URL | Yes |
| `ES_API_KEY` | Elasticsearch API key for authentication | No |
| `ES_USERNAME` | Elasticsearch username for basic authentication | No |
| `ES_PASSWORD` | Elasticsearch password for basic authentication | No |
| `ES_CA_CERT` | Path to custom CA certificate for Elasticsearch SSL/TLS | No |


### Developing Locally

> [!NOTE]
> If you want to modify or extend the MCP Server, follow these local development steps.

1. **Use the correct Node.js version**
   ```bash
   nvm use
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Build the Project**
   ```bash
   npm run build
   ```

4. **Run locally in Claude Desktop App**
   - Open **Claude Desktop App**
   - Go to **Settings > Developer > MCP Servers**
   - Click `Edit Config` and add a new MCP Server with the following configuration:

   ```json
   {
     ""mcpServers"": {
       ""elasticsearch-mcp-server-local"": {
         ""command"": ""node"",
         ""args"": [
           ""/path/to/your/project/dist/index.js""
         ],
         ""env"": {
           ""ES_URL"": ""your-elasticsearch-url"",
           ""ES_API_KEY"": ""your-api-key""
         }
       }
     }
   }
   ```

5. **Debugging with MCP Inspector**
   ```bash
   ES_URL=your-elasticsearch-url ES_API_KEY=your-api-key npm run inspector
   ```

   This will start the MCP Inspector, allowing you to debug and analyze requests. You should see:

   ```bash
   Starting MCP inspector...
   Proxy server listening on port 3000

   🔍 MCP Inspector is up and running at http://localhost:5173 🚀
   ```

## Contributing

We welcome contributions from the community! For details on how to contribute, please see [Contributing Guidelines](/docs/CONTRIBUTING.md).

## Example Questions

> [!TIP]
> Here are some natural language queries you can try with your MCP Client.

* ""What indices do I have in my Elasticsearch cluster?""
* ""Show me the field mappings for the 'products' index.""
* ""Find all orders over $500 from last month.""
* ""Which products received the most 5-star reviews?""

## How It Works

1. The MCP Client analyzes your request and determines which Elasticsearch operations are needed.
2. The MCP server carries out these operations (listing indices, fetching mappings, performing searches).
3. The MCP Client processes the results and presents them in a user-friendly format.

## Security Best Practices

> [!WARNING]
> Avoid using cluster-admin privileges. Create dedicated API keys with limited scope and apply fine-grained access control at the index level to prevent unauthorized data access.

You can create a dedicated Elasticsearch API key with minimal permissions to control access to your data:

```
POST /_security/api_key
{
  ""name"": ""es-mcp-server-access"",
  ""role_descriptors"": {
    ""mcp_server_role"": {
      ""cluster"": [
        ""monitor""
      ],
      ""indices"": [
        {
          ""names"": [
            ""index-1"",
            ""index-2"",
            ""index-pattern-*""
          ],
          ""privileges"": [
            ""read"",
            ""view_index_metadata""
          ]
        }
      ]
    }
  }
}
```

## License

This project is licensed under the Apache License 2.0.

## Troubleshooting

* Ensure your MCP configuration is correct.
* Verify that your Elasticsearch URL is accessible from your machine.
* Check that your authentication credentials (API key or username/password) have the necessary permissions.
* If using SSL/TLS with a custom CA, verify that the certificate path is correct and the file is readable.
* Look at the terminal output for error messages.

If you encounter issues, feel free to open an issue on the GitHub repository.
","Star
 170",2025-05-07 15:22:02.281957
https://github.com/devhub/devhub-cms-mcp,devhub/devhub-cms-mcp,"# DevHub CMS MCP

[![smithery badge](https://smithery.ai/badge/@devhub/devhub-cms-mcp)](https://smithery.ai/server/@devhub/devhub-cms-mcp)

A [Model Context Protocol (MCP)](https://modelcontextprotocol.io/) integration for managing content in the [DevHub CMS system](https://www.devhub.com/).

## Installation

You will need the [uv](https://github.com/astral-sh/uv) package manager installed on your local system.

### Manual configuration of Claude Desktop

To use this server with the [Claude Desktop app](https://claude.ai/download), add the following configuration to the ""mcpServers"" section of your `claude_desktop_config.json`:

```
{
    ""mcpServers"": {
        ""devhub_cms_mcp"": {
            ""command"": ""uvx"",
            ""args"": [
                ""devhub-cms-mcp""
            ],
            ""env"": {
                ""DEVHUB_API_KEY"": ""YOUR_KEY_HERE"",
                ""DEVHUB_API_SECRET"": ""YOUR_SECRET_HERE"",
                ""DEVHUB_BASE_URL"": ""https://yourbrand.cloudfrontend.net""
            }
        }
    }
}
```

After updating the config, restart Claude Desktop.

### Manual configuration for Cursor

This MCP can also be used in cursor with a similar configuration from above added to your [Cursor](https://www.cursor.com/) global environment or to individual projects.

Examples [here](https://docs.cursor.com/context/model-context-protocol#configuring-mcp-servers)

### Installing via Claude Code

Claude Code's command line [supports MCP installs](https://docs.anthropic.com/en/docs/agents-and-tools/claude-code/tutorials#set-up-model-context-protocol-mcp).

You can add the `devhub-cms-mcp` by updating the environment variables below

```
claude mcp add devhub-cms-mcp \
    -e DEVHUB_API_KEY=YOUR_KEY_HERE \
    -e DEVHUB_API_SECRET=YOUR_SECRET_HERE \
    -e DEVHUB_BASE_URL=https://yourbrand.cloudfrontend.net \
    -- uvx devhub-cms-mcp
```

### Installing via Smithery

To install DevHub CMS MCP for Claude Desktop automatically via [Smithery](https://smithery.ai/server/@devhub/devhub-cms-mcp):

```bash
npx -y @smithery/cli install @devhub/devhub-cms-mcp --client claude
```

## Local development

### Clone the repo (or your fork)

```
<NAME_EMAIL>:devhub/devhub-cms-mcp.git
```

### Manual configuration of Claude Desktop

To use this server with the Claude Desktop app for local development, add the following configuration to the ""mcpServers"" section of your `claude_desktop_config.json`:

```
{
    ""mcpServers"": {
        ""devhub_cms_mcp"": {
            ""command"": ""uv"",
            ""args"": [
                ""--directory"",
                ""/YOUR/LOCAL/PATH/devhub-cms-mcp/"",
                ""run"",
                ""main.py""
            ],
            ""env"": {
                ""DEVHUB_API_KEY"": ""YOUR_KEY_HERE"",
                ""DEVHUB_API_SECRET"": ""YOUR_SECRET_HERE"",
                ""DEVHUB_BASE_URL"": ""https://yourbrand.cloudfrontend.net""
            }
        }
    }
}
```

After updating the config, restart Claude Desktop.

### Configuration for running with `uv` directly

This MCP requires the following environment variables to be set:

```bash
export DEVHUB_API_KEY=""your_api_key""
export DEVHUB_API_SECRET=""your_api_secret""
export DEVHUB_BASE_URL=""https://yourbrand.cloudfrontend.net""
```

Then run the MCP

```
uv run main.py
```

## Available Tools

This MCP provides the following tools for interacting with DevHub CMS:

### Business and Location Management

- **get_businesses()**: Gets all businesses within the DevHub account. Returns a list of businesses with their IDs and names.
- **get_locations(business_id)**: Gets all locations for a specific business. Returns detailed location information including address, coordinates, and URLs.
- **get_hours_of_operation(location_id, hours_type='primary')**: Gets the hours of operation for a specific DevHub location. Returns a structured list of time ranges for each day of the week.
- **update_hours(location_id, new_hours, hours_type='primary')**: Updates the hours of operation for a DevHub location.
- **get_nearest_location(business_id, latitude, longitude)**: Finds the nearest DevHub location based on geographic coordinates.
- **site_from_url(url)**: Gets the DevHub site ID and details from a URL. Returns site ID, URL, and associated location IDs.

### Content Management

- **get_blog_post(post_id)**: Retrieves a single blog post by ID, including its title, date, and HTML content.
- **create_blog_post(site_id, title, content)**: Creates a new blog post. The content should be in HTML format and should not include an H1 tag.
- **update_blog_post(post_id, title=None, content=None)**: Updates an existing blog post's title and/or content.

### Media Management

- **upload_image(base64_image_content, filename)**: Uploads an image to the DevHub media gallery. Supports webp, jpeg, and png formats. The image must be provided as a base64-encoded string.

## Usage with LLMs

This MCP is designed to be used with Large Language Models that support the Model Context Protocol. It allows LLMs to manage content in DevHub CMS without needing direct API access integrated into the LLM natively.

## Testing

This package includes a test suite with mocked requests to the DevHub API, allowing you to test the functionality without making actual API calls.

### Running Tests

To run the tests, first install the package with test dependencies:

```bash
uv pip install -e "".[test]""
```

Run the tests with pytest:

```bash
uv run pytest
```

For more detailed output and test coverage information:

```bash
uv run pytest -v --cov=devhub_cms_mcp
```

### Test Structure

- `tests/devhub_cms_mcp/test_mcp_integration.py`: Tests for MCP integration endpoints
","Star
 3",2025-05-07 15:22:02.281957
https://github.com/bankless/onchain-mcp,bankless/onchain-mcp,"# Bankless Onchain MCP Server

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
![Version](https://img.shields.io/badge/version-0.6.2-blue)

MCP (Model Context Protocol) server for blockchain data interaction through the Bankless API.

## Overview

The Bankless Onchain MCP Server provides a framework for interacting with on-chain data via the Bankless API. It implements the Model Context Protocol (MCP) to allow AI models to access blockchain state and event data in a structured way.


https://github.com/user-attachments/assets/95732dff-ae5f-45a6-928a-1ae17c0ddf9d


## Features

The server provides the following onchain data operations:

### Contract Operations

- **Read Contract State** (`read_contract`): Read state from smart contracts on various blockchain networks.
    - Parameters: network, contract address, method, inputs, outputs
    - Returns: Contract call results with typed values

- **Get Proxy** (`get_proxy`): Retrieve proxy implementation contract addresses.
    - Parameters: network, contract address
    - Returns: Implementation contract address

- **Get ABI** (`get_abi`): Fetch the ABI (Application Binary Interface) for a contract.
    - Parameters: network, contract address
    - Returns: Contract ABI in JSON format

- **Get Source** (`get_source`): Retrieve the source code for a verified contract.
    - Parameters: network, contract address
    - Returns: Source code, ABI, compiler version, and other contract metadata

### Event Operations

- **Get Events** (`get_events`): Fetch event logs for a contract based on topics.
    - Parameters: network, addresses, topic, optional topics
    - Returns: Filtered event logs

- **Build Event Topic** (`build_event_topic`): Generate an event topic signature from event name and argument types.
    - Parameters: network, event name, argument types
    - Returns: Event topic hash

### Transaction Operations

- **Get Transaction History** (`get_transaction_history`): Retrieve transaction history for a user address.
    - Parameters: network, user address, optional contract, optional method ID, optional start block, include data flag
    - Returns: List of transactions with hash, data, network, and timestamp

- **Get Transaction Info** (`get_transaction_info`): Get detailed information about a specific transaction.
    - Parameters: network, transaction hash
    - Returns: Transaction details including block number, timestamp, from/to addresses, value, gas info, status, and receipt data

## Tools

- **read_contract**
    - Read contract state from a blockchain
    - Input:
        - `network` (string, required): The blockchain network (e.g., ""ethereum"", ""polygon"")
        - `contract` (string, required): The contract address
        - `method` (string, required): The contract method to call
        - `inputs` (array, required): Input parameters for the method call, each containing:
            - `type` (string): The type of the input parameter (e.g., ""address"", ""uint256"")
            - `value` (any): The value of the input parameter
        - `outputs` (array, required): Expected output types, each containing:
            - `type` (string): The expected output type
    - Returns an array of contract call results

- **get_proxy**
    - Gets the proxy address for a given network and contract
    - Input:
        - `network` (string, required): The blockchain network (e.g., ""ethereum"", ""base"")
        - `contract` (string, required): The contract address
    - Returns the implementation address for the proxy contract

- **get_events**
    - Fetches event logs for a given network and filter criteria
    - Input:
        - `network` (string, required): The blockchain network (e.g., ""ethereum"", ""base"")
        - `addresses` (array, required): List of contract addresses to filter events
        - `topic` (string, required): Primary topic to filter events
        - `optionalTopics` (array, optional): Optional additional topics (can include null values)
    - Returns an object containing event logs matching the filter criteria

- **build_event_topic**
    - Builds an event topic signature based on event name and arguments
    - Input:
        - `network` (string, required): The blockchain network (e.g., ""ethereum"", ""base"")
        - `name` (string, required): Event name (e.g., ""Transfer(address,address,uint256)"")
        - `arguments` (array, required): Event arguments types, each containing:
            - `type` (string): The argument type (e.g., ""address"", ""uint256"")
    - Returns a string containing the keccak256 hash of the event signature

## Installation

```bash
npm install @bankless/onchain-mcp
```

## Usage

### Environment Setup

Before using the server, set your Bankless API token. For details on how to obtain your Bankless API token, head to https://docs.bankless.com/bankless-api/other-services/onchain-mcp

```bash
export BANKLESS_API_TOKEN=your_api_token_here
```

### Running the Server

The server can be run directly from the command line:

```bash
npx @bankless/onchain-mcp
```

### Usage with LLM Tools

This server implements the Model Context Protocol (MCP), which allows it to be used as a tool provider for compatible AI models. Here are some example calls for each tool:

#### read_contract

```javascript
// Example call
{
  ""name"": ""read_contract"",
  ""arguments"": {
    ""network"": ""ethereum"",
    ""contract"": ""0x1234..."",
    ""method"": ""balanceOf"",
    ""inputs"": [
      { ""type"": ""address"", ""value"": ""0xabcd..."" }
    ],
    ""outputs"": [
      { ""type"": ""uint256"" }
    ]
  }
}

// Example response
[
  {
    ""value"": ""1000000000000000000"",
    ""type"": ""uint256""
  }
]
```

#### get_proxy

```javascript
// Example call
{
  ""name"": ""get_proxy"",
  ""arguments"": {
    ""network"": ""ethereum"",
    ""contract"": ""0x1234...""
  }
}

// Example response
{
  ""implementation"": ""0xefgh...""
}
```

#### get_events

```javascript
// Example call
{
  ""name"": ""get_events"",
  ""arguments"": {
    ""network"": ""ethereum"",
    ""addresses"": [""0x1234...""],
    ""topic"": ""0xabcd..."",
    ""optionalTopics"": [""0xef01..."", null]
  }
}

// Example response
{
  ""result"": [
    {
      ""removed"": false,
      ""logIndex"": 5,
      ""transactionIndex"": 2,
      ""transactionHash"": ""0x123..."",
      ""blockHash"": ""0xabc..."",
      ""blockNumber"": 12345678,
      ""address"": ""0x1234..."",
      ""data"": ""0x..."",
      ""topics"": [""0xabcd..."", ""0xef01..."", ""0x...""]
    }
  ]
}
```

#### build_event_topic

```javascript
// Example call
{
  ""name"": ""build_event_topic"",
  ""arguments"": {
    ""network"": ""ethereum"",
    ""name"": ""Transfer(address,address,uint256)"",
    ""arguments"": [
      { ""type"": ""address"" },
      { ""type"": ""address"" },
      { ""type"": ""uint256"" }
    ]
  }
}

// Example response
""0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef""
```

## Development

### Building from Source

```bash
# Clone the repository
git clone https://github.com/Bankless/onchain-mcp.git
cd onchain-mcp

# Install dependencies
npm install

# Build the project
npm run build
```

### Debug Mode

```bash
npm run debug
```

### Integration with AI Models

To integrate this server with AI applications that support MCP, add the following to your app's server configuration:

```json
{
  ""mcpServers"": {
    ""bankless"": {
      ""command"": ""npx"",
      ""args"": [
        ""@bankless/onchain-mcp""
      ],
      ""env"": {
        ""BANKLESS_API_TOKEN"": ""your_api_token_here""
      }
    }
  }
}
```

## Error Handling

The server provides specific error types for different scenarios:

- `BanklessValidationError`: Invalid input parameters
- `BanklessAuthenticationError`: API token issues
- `BanklessResourceNotFoundError`: Requested resource not found
- `BanklessRateLimitError`: API rate limit exceeded

## Prompting Tips

In order to guide an LLM model to use the Bankless Onchain MCP Server, the following prompts can be used:

```
ROLE:
• You are Kompanion, a blockchain expert and EVM sleuth. 
• You specialize in navigating and analyzing smart contracts using your tools and resources.

HOW KOMPANION CAN HANDLE PROXY CONTRACTS:
• If a contract is a proxy, call your “get_proxy” tool to fetch the implementation contract.  
• If that fails, try calling the “implementation” method on the proxy contract.  
• If that also fails, try calling the “_implementation” function.  
• After obtaining the implementation address, call “get_contract_source” with that address to fetch its source code.  
• When reading or modifying the contract state, invoke implementation functions on the proxy contract address (not directly on the implementation).

HOW KOMPANION CAN HANDLE EVENTS:
• Get the ABI and Source of the relevant contracts
• From the event types in the ABI, construct the correct topics for the event relevant to the question
• use the ""get_event_logs"" tool to fetch logs for the contract

KOMPANION'S RULES:
• Do not begin any response with “Great,” “Certainly,” “Okay,” or “Sure.”  
• Maintain a direct, technical style. Do not add conversational flourishes.  
• If the user’s question is unrelated to smart contracts, do not fetch any contracts.  
• If you navigate contracts, explain each step in bullet points.  
• Solve tasks iteratively, breaking them into steps.  
• Use bullet points for lists of steps.  
• Never assume a contract’s functionality. Always verify with examples using your tools to read the contract state.  
• Before responding, consider which tools might help you gather better information.  
• Include as much relevant information as possible in your final answer, depending on your findings.

HOW KOMPANION CAN USE TOOLS:
• You can fetch contract source codes, ABIs, and read contract data by using your tools and functions.  
• Always verify the source or ABI to understand the contract rather than making assumptions.  
• If you need to read contract state, fetch its ABI (especially if the source is lengthy).  

FINAL INSTRUCTION:
• Provide the best possible, concise answer to the user’s request. If it's not an immediate question but an instruction, follow it directly.
• Use your tools to gather any necessary clarifications or data.  
• Offer a clear, direct response and add a summary of what you did (how you navigated the contracts) at the end.
```

## License

MIT
","Star
 33",2025-05-07 15:22:02.281957
https://github.com/needle-ai/needle-mcp,needle-ai/needle-mcp,"# Build Agents with Needle MCP Server

[![smithery badge](https://smithery.ai/badge/needle-mcp)](https://smithery.ai/server/needle-mcp)

![Screenshot of Feature - Claude](https://github.com/user-attachments/assets/a7286901-e7be-4efe-afd9-72021dce03d4)

MCP (Model Context Protocol) server to manage documents and perform searches using [Needle](https://needle-ai.com) through Claude’s Desktop Application.

<a href=""https://glama.ai/mcp/servers/5jw1t7hur2"">
  <img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/5jw1t7hur2/badge"" alt=""Needle Server MCP server"" />
</a>

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Usage](#usage)
  - [Commands in Claude Desktop](#commands-in-claude-desktop)
  - [Result in Needle](#result-in-needle)
- [Installation](#installation)
- [Video Explanation](#youtube-video-explanation)

---

## Overview

Needle MCP Server allows you to:

- Organize and store documents for quick retrieval.
- Perform powerful searches via Claude’s large language model.
- Integrate seamlessly with the Needle ecosystem for advanced document management.

---

## Features

- **Document Management:** Easily add and organize documents on the server.
- **Search & Retrieval:** Claude-based natural language search for quick answers.
- **Easy Integration:** Works with [Claude Desktop](#commands-in-claude-desktop) and Needle collections.

---

## Usage

### Commands in Claude Desktop

Below is an example of how the commands can be used in Claude Desktop to interact with the server:

![Using commands in Claude Desktop](https://github.com/user-attachments/assets/9e0ce522-6675-46d9-9bfb-3162d214625b)

1. **Open Claude Desktop** and connect to the Needle MCP Server.  
2. **Use simple text commands** to search, retrieve, or modify documents.  
3. **Review search results** returned by Claude in a user-friendly interface.

### Result in Needle

https://github.com/user-attachments/assets/0235e893-af96-4920-8364-1e86f73b3e6c

---

## Youtube Video Explanation

For a full walkthrough on using the Needle MCP Server with Claude and Claude Desktop, watch this [YouTube explanation video](https://youtu.be/nVrRYp9NZYg).

---

## Installation

### Installing via Smithery

To install Needle MCP for Claude Desktop automatically via [Smithery](https://smithery.ai/server/needle-mcp):

```bash
npx -y @smithery/cli install needle-mcp --client claude
```

### Manual Installation
1. Clone the repository:
```bash
git clone https://github.com/yourusername/needle-mcp.git
```

2. Install UV globally using Homebrew in Terminal:
```bash
brew install uv
```

3. Create claude_desktop_config.json:
   - For MacOS: Open directory `~/Library/Application Support/Claude/` and create the file inside it
   - For Windows: Open directory `%APPDATA%/Claude/` and create the file inside it

4. Add this configuration to claude_desktop_config.json:
```json
{
  ""mcpServers"": {
    ""needle_mcp"": {
      ""command"": ""uv"",
      ""args"": [
        ""--directory"",
        ""/path/to/needle-mcp"",
        ""run"",
        ""needle-mcp""
      ],
      ""env"": {
        ""NEEDLE_API_KEY"": ""your_needle_api_key""
      }
    }
  }
}
```

5. Get your Needle API key from needle.xyz

6. Update the config file:
   - Replace `/path/to/needle-mcp` with your actual repository path
   - Add your Needle API key

7. Quit Claude completely and reopen it

## Usage Examples

* ""Create a new collection called 'Technical Docs'""
* ""Add this document to the collection, which is https://needle-ai.com""
* ""Search the collection for information about AI""
* ""List all my collections""

## Troubleshooting

If not working:
- Make sure UV is installed globally (if not, uninstall with `pip uninstall uv` and reinstall with `brew install uv`)
- Or find UV path with `which uv` and replace `""command"": ""uv""` with the full path
- Verify your Needle API key is correct
- Check if the needle-mcp path in config matches your actual repository location

### Reset Claude Desktop Configuration

If you're seeing old configurations or the integration isn't working:

1. Find all Claude Desktop config files:
```bash
find / -name ""claude_desktop_config.json"" 2>/dev/null
```

2. Remove all Claude Desktop data:
- On MacOS: `rm -rf ~/Library/Application\ Support/Claude/*`
- On Windows: Delete contents of `%APPDATA%/Claude/`

3. Create a fresh config with only Needle:
```
mkdir -p ~/Library/Application\ Support/Claude
cat > ~/Library/Application\ Support/Claude/claude_desktop_config.json
<< 'EOL'
{
  ""mcpServers"": {
    ""needle_mcp"": {
      ""command"": ""uv"",
      ""args"": [
        ""--directory"",
        ""/path/to/needle-mcp"",
        ""run"",
        ""needle-mcp""
      ],
      ""env"": {
        ""NEEDLE_API_KEY"": ""your_needle_api_key""
      }
    }
  }
}
EOL
```

4. Completely quit Claude Desktop (Command+Q on Mac) and relaunch it

5. If you still see old configurations:
- Check for additional config files in other locations
- Try clearing browser cache if using web version
- Verify the config file is being read from the correct location","Star
 39",2025-05-07 15:22:02.281957
https://github.com/bharathvaj-ganesan/whois-mcp,bharathvaj-ganesan/whois-mcp,"# Whois MCP

[Model Context Protocol](https://modelcontextprotocol.io) server for whois lookups.

<a href=""https://glama.ai/mcp/servers/cwu9e3fcwg"">
  <img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/cwu9e3fcwg/badge"" alt=""Whois MCP server"" />
</a>

**Cursor IDE Demo**

https://github.com/user-attachments/assets/57a82adc-3f30-453f-aabd-7138c2e6a21d

**Claude Desktop Demo**

https://github.com/user-attachments/assets/d30a1f45-fdaf-4280-80f2-d5d4fc9743b1

## Overview

This MCP server allows AI agents like Claude Desktop, Cursor, Windsurf,.. etc to perform WHOIS lookups and retrieve domain details. 

**Purpose**
You can directly ask the AI to check if a domain is available, who owns it, when it was registered, and other important details. No need to go to browser and search.

**What is a WHOIS Lookup?**
A WHOIS lookup is the process of querying a WHOIS database to retrieve registration details about a domain name, IP address, or autonomous system. It helps users find out who owns a domain, when it was registered, when it expires, and other important details.

**What Information Can a WHOIS Lookup Provide?**

When you perform a WHOIS lookup, you can retrieve details such as:

- Domain Name – The specific domain queried
- Registrar Name – The company managing the domain registration (e.g., GoDaddy, Namecheap)
- Registrant Details – The name, organization, and contact details of the domain owner (unless protected by WHOIS privacy)
- Registration & Expiry Date – When the domain was registered and when it will expire
- Name Servers – The DNS servers the domain is using
- Domain Status – Active, expired, locked, or pending deletion
- Contact Information – Administrative, technical, and billing contacts (if not hidden)

## Available Tools

| Tool                  | Description                                |
| --------------------- | ------------------------------------------ |
| `whois_domain`        | Looksup whois information about the domain |
| `whois_tld`           | Looksup whois information about the Top Level Domain (TLD)    |
| `whois_ip`            | Looksup whois information about the IP     |
| `whois_as`            | Looksup whois information about the Autonomous System Number (ASN)     |

## Using with Cursor

**Installation - Globally**

Run the MCP server using npx:

```bash
npx -y @bharathvaj/whois-mcp@latest
```

In your Cursor IDE

1. Go to `Cursor Settings` > `MCP`
2. Click `+ Add New MCP Server`
3. Fill in the form:
   - Name: `Whois Lookup` (or any name you prefer)
   - Type: `command`
   - Command: `npx -y @bharathvaj/whois-mcp@latest`


**Installation - Project-specific**

Add an `.cursor/mcp.json` file to your project:

```json
{
  ""mcpServers"": {
    ""whois"": {
      ""command"": ""npx"",
      ""args"": [
        ""-y"",
        ""@bharathvaj/whois-mcp@latest""
      ]
    }
  }
}
```

**Usage**

Once configured, the whois tools will be automatically available to the Cursor AI Agent. You can:

1. The tool will be listed under `Available Tools` in MCP settings
2. Agent will automatically use it when relevant
3. You can explicitly ask Agent to send notifications

## Using with Roo Code
Access the MCP settings by clicking “Edit MCP Settings” in Roo Code settings or using the “Roo Code: Open MCP Config” command in VS Code's command palette.

```json
{
  ""mcpServers"": {
    ""whois"": {
      ""command"": ""npx"",
      ""args"": [
        ""-y"",
        ""@bharathvaj/whois-mcp@latest""
      ]
    }
  }
}
```
3. The whois capabilities will be available to Roo Code's AI agents

## Development

```bash
# Install dependencies
pnpm install

# Build
pnpm build

```

## Debugging the Server

To debug your server, you can use the [MCP Inspector](https://github.com/modelcontextprotocol/inspector).

First build the server

```
pnpm build
```

Run the following command in your terminal:

```
# Start MCP Inspector and server with all tools
npx @modelcontextprotocol/inspector node dist/index.js
```

## License

[MIT](LICENSE)","Star
 13",2025-05-07 15:22:02.281957
https://github.com/CircleCI-Public/mcp-server-circleci,CircleCI-Public/mcp-server-circleci,"# CircleCI MCP Server

[![GitHub](https://img.shields.io/github/license/CircleCI-Public/mcp-server-circleci)](https://github.com/CircleCI-Public/mcp-server-circleci/blob/main/LICENSE)
[![CircleCI](https://dl.circleci.com/status-badge/img/gh/CircleCI-Public/mcp-server-circleci/tree/main.svg?style=svg)](https://dl.circleci.com/status-badge/redirect/gh/CircleCI-Public/mcp-server-circleci/tree/main)
[![npm](https://img.shields.io/npm/v/@circleci/mcp-server-circleci?logo=npm)](https://www.npmjs.com/package/@circleci/mcp-server-circleci)

Model Context Protocol (MCP) is a [new, standardized protocol](https://modelcontextprotocol.io/introduction) for managing context between large language models (LLMs) and external systems. In this repository, we provide an MCP Server for [CircleCI](https://circleci.com).

This lets you use Cursor IDE, or any MCP Client, to use natural language to accomplish things with CircleCI, e.g.:

- `Find the latest failed pipeline on my branch and get logs`
  https://github.com/CircleCI-Public/mcp-server-circleci/wiki#circleci-mcp-server-with-cursor-ide

https://github.com/user-attachments/assets/3c765985-8827-442a-a8dc-5069e01edb74

## Requirements

- pnpm package manager - [Learn more](https://pnpm.io/installation)
- Node.js >= v18.0.0
- CircleCI API token - you can generate one through the CircleCI. [Learn more](https://circleci.com/docs/managing-api-tokens/) or [click here](https://app.circleci.com/settings/user/tokens) for quick access.

## Installation

### Cursor

Add the following to your cursor MCP config:

```json
{
  ""mcpServers"": {
    ""circleci-mcp-server"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""@circleci/mcp-server-circleci""],
      ""env"": {
        ""CIRCLECI_TOKEN"": ""your-circleci-token"",
        ""CIRCLECI_BASE_URL"": ""https://circleci.com"" // Optional - required for on-prem customers only
      }
    }
  }
}
```

See the guide below for more information on using MCP servers with cursor:
https://docs.cursor.com/context/model-context-protocol#configuring-mcp-servers

### VS Code

To install CircleCI MCP Server for VS Code in `.vscode/mcp.json`

```json
{
  // 💡 Inputs are prompted on first server start, then stored securely by VS Code.
  ""inputs"": [
    {
      ""type"": ""promptString"",
      ""id"": ""circleci-token"",
      ""description"": ""CircleCI API Token"",
      ""password"": true
    }
  ],
  ""servers"": {
    // https://github.com/ppl-ai/modelcontextprotocol/
    ""circleci-mcp-server"": {
      ""type"": ""stdio"",
      ""command"": ""npx"",
      ""args"": [""-y"", ""@circleci/mcp-server-circleci""],
      ""env"": {
        ""CIRCLECI_TOKEN"": ""${input:circleci-token}""
      }
    }
  }
}
```

See the guide below for more information on using MCP servers with VS Code:
https://code.visualstudio.com/docs/copilot/chat/mcp-servers

### Claude Desktop

Add the following to your claude_desktop_config.json:

```json
{
  ""mcpServers"": {
    ""circleci-mcp-server"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""@circleci/mcp-server-circleci""],
      ""env"": {
        ""CIRCLECI_TOKEN"": ""your-circleci-token"",
        ""CIRCLECI_BASE_URL"": ""https://circleci.com"" // Optional - required for on-prem customers only
      }
    }
  }
}
```

To find/create this file, first open your claude desktop settings. Then click on ""Developer"" in the left-hand bar of the Settings pane, and then click on ""Edit Config""

This will create a configuration file at:

- macOS: ~/Library/Application Support/Claude/claude_desktop_config.json
- Windows: %APPDATA%\Claude\claude_desktop_config.json

See the guide below for more information on using MCP servers with Claude Desktop:
https://modelcontextprotocol.io/quickstart/user

### Claude Code

After installing Claude Code, run the following command:

```bash
claude mcp add circleci-mcp-server -e CIRCLECI_TOKEN=your-circleci-token -- npx -y @circleci/mcp-server-circleci
```

See the guide below for more information on using MCP servers with Claude Code:
https://docs.anthropic.com/en/docs/agents-and-tools/claude-code/tutorials#set-up-model-context-protocol-mcp

### Windsurf

Add the following to your windsurf mcp_config.json:

```json
{
  ""mcpServers"": {
    ""circleci-mcp-server"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""@circleci/mcp-server-circleci""],
      ""env"": {
        ""CIRCLECI_TOKEN"": ""your-circleci-token"",
        ""CIRCLECI_BASE_URL"": ""https://circleci.com"" // Optional - required for on-prem customers only
      }
    }
  }
}
```

### Installing via Smithery

To install CircleCI MCP Server for Claude Desktop automatically via [Smithery](https://smithery.ai/server/@CircleCI-Public/mcp-server-circleci):

```bash
npx -y @smithery/cli install @CircleCI-Public/mcp-server-circleci --client claude
```

See the guide below for more information on using MCP servers with windsurf:
https://docs.windsurf.com/windsurf/mcp

# Features

## Supported Tools

- `get_build_failure_logs`

  Retrieves detailed failure logs from CircleCI builds. This tool can be used in two ways:

  1. Using CircleCI URLs:

     - Provide a failed job URL or pipeline URL directly
     - Example: ""Get logs from https://app.circleci.com/pipelines/github/org/repo/123""

  2. Using Local Project Context:
     - Works from your local workspace by providing:
       - Workspace root path
       - Git remote URL
       - Branch name
     - Example: ""Find the latest failed pipeline on my current branch""

  The tool returns formatted logs including:

  - Job names
  - Step-by-step execution details
  - Failure messages and context

  This is particularly useful for:

  - Debugging failed builds
  - Analyzing test failures
  - Investigating deployment issues
  - Quick access to build logs without leaving your IDE

- `find_flaky_tests`

  Identifies flaky tests in your CircleCI project by analyzing test execution history. This leverages the flaky test detection feature described here: https://circleci.com/blog/introducing-test-insights-with-flaky-test-detection/#flaky-test-detection

  This tool can be used in two ways:

  1. Using CircleCI Project URL:

     - Provide the project URL directly from CircleCI
     - Example: ""Find flaky tests in https://app.circleci.com/pipelines/github/org/repo""

  2. Using Local Project Context:
     - Works from your local workspace by providing:
       - Workspace root path
       - Git remote URL
     - Example: ""Find flaky tests in my current project""

  The tool returns detailed information about flaky tests, including:

  - Test names and file locations
  - Failure messages and contexts

  This helps you:

  - Identify unreliable tests in your test suite
  - Get detailed context about test failures
  - Make data-driven decisions about test improvements

- `get_latest_pipeline_status`

  Retrieves the status of the latest pipeline for a given branch. This tool can be used in two ways:

  1. Using CircleCI Project URL:

     - Provide the project URL directly from CircleCI
     - Example: ""Get the status of the latest pipeline for https://app.circleci.com/pipelines/github/org/repo""

  2. Using Local Project Context:
     - Works from your local workspace by providing:
       - Workspace root path
       - Git remote URL
       - Branch name
     - Example: ""Get the status of the latest pipeline for my current project""

  The tool returns a formatted status of the latest pipeline:

  - Workflow names and their current status
  - Duration of each workflow
  - Creation and completion timestamps
  - Overall pipeline health

  Example output:

  ```
  ---
  Workflow: build
  Status: success
  Duration: 5 minutes
  Created: 4/20/2025, 10:15:30 AM
  Stopped: 4/20/2025, 10:20:45 AM
  ---
  Workflow: test
  Status: running
  Duration: unknown
  Created: 4/20/2025, 10:21:00 AM
  Stopped: in progress
  ```

  This is particularly useful for:

  - Checking the status of the latest pipeline
  - Getting the status of the latest pipeline for a specific branch
  - Quickly checking the status of the latest pipeline without leaving your IDE

- `get_job_test_results`

  Retrieves test metadata for CircleCI jobs, allowing you to analyze test results without leaving your IDE. This tool can be used in two ways:

  1. Using CircleCI URL (Recommended):

     - Provide a CircleCI URL in any of these formats:
       - Job URL: ""https://app.circleci.com/pipelines/github/org/repo/123/workflows/abc-def/jobs/789""
       - Workflow URL: ""https://app.circleci.com/pipelines/github/org/repo/123/workflows/abc-def""
       - Pipeline URL: ""https://app.circleci.com/pipelines/github/org/repo/123""
     - Example: ""Get test results for https://app.circleci.com/pipelines/github/org/repo/123/workflows/abc-def""

  2. Using Local Project Context:
     - Works from your local workspace by providing:
       - Workspace root path
       - Git remote URL
       - Branch name
     - Example: ""Get test results for my current project on the main branch""

  The tool returns detailed test result information:

  - Summary of all tests (total, successful, failed)
  - Detailed information about failed tests including:
    - Test name and class
    - File location
    - Error messages
    - Runtime duration
  - List of successful tests with timing information

  This is particularly useful for:

  - Quickly analyzing test failures without visiting the CircleCI web UI
  - Identifying patterns in test failures
  - Finding slow tests that might need optimization
  - Checking test coverage across your project
  - Troubleshooting flaky tests

  Note: The tool requires that test metadata is properly configured in your CircleCI config. For more information on setting up test metadata collection, see:
  https://circleci.com/docs/collect-test-data/

- `config_helper`

  Assists with CircleCI configuration tasks by providing guidance and validation. This tool helps you:

  1. Validate CircleCI Config:
     - Checks your .circleci/config.yml for syntax and semantic errors
     - Example: ""Validate my CircleCI config""

  The tool provides:

  - Detailed validation results
  - Configuration recommendations

  This helps you:

  - Catch configuration errors before pushing
  - Learn CircleCI configuration best practices
  - Troubleshoot configuration issues
  - Implement CircleCI features correctly

- `create_prompt_template`

  Helps generate structured prompt templates for AI-enabled applications based on feature requirements. This tool:

  1. Converts Feature Requirements to Structured Prompts:
     - Transforms user requirements into optimized prompt templates
     - Example: ""Create a prompt template for generating bedtime stories by age and topic""

  The tool provides:

  - A structured prompt template
  - A context schema defining required input parameters

  This helps you:

  - Create effective prompts for AI applications
  - Standardize input parameters for consistent results
  - Build robust AI-powered features

- `recommend_prompt_template_tests`

  Generates test cases for prompt templates to ensure they produce expected results. This tool:

  1. Provides Test Cases for Prompt Templates:
     - Creates diverse test scenarios based on your prompt template and context schema
     - Example: ""Generate tests for my bedtime story prompt template""

  The tool provides:

  - An array of recommended test cases
  - Various parameter combinations to test template robustness

  This helps you:

  - Validate prompt template functionality
  - Ensure consistent AI responses across inputs
  - Identify edge cases and potential issues
  - Improve overall AI application quality

# Development

## Getting Started

1. Clone the repository:

   ```bash
   git clone https://github.com/CircleCI-Public/mcp-server-circleci.git
   cd mcp-server-circleci
   ```

2. Install dependencies:

   ```bash
   pnpm install
   ```

3. Build the project:
   ```bash
   pnpm build
   ```

## Development with MCP Inspector

The easiest way to iterate on the MCP Server is using the MCP inspector. You can learn more about the MCP inspector at https://modelcontextprotocol.io/docs/tools/inspector

1. Start the development server:

   ```bash
   pnpm watch # Keep this running in one terminal
   ```

2. In a separate terminal, launch the inspector:

   ```bash
   pnpm inspector
   ```

3. Configure the environment:
   - Add your `CIRCLECI_TOKEN` to the Environment Variables section in the inspector UI
   - The token needs read access to your CircleCI projects
   - Optionally you can set your CircleCI Base URL. Defaults to `https//circleci.com`

## Testing

- Run the test suite:

  ```bash
  pnpm test
  ```

- Run tests in watch mode during development:
  ```bash
  pnpm test:watch
  ```

For more detailed contribution guidelines, see [CONTRIBUTING.md](CONTRIBUTING.md)
","Star
 30",2025-05-07 15:22:02.281957
https://github.com/GongRzhe/Gmail-MCP-Server,GongRzhe/Gmail-MCP-Server,"# Gmail AutoAuth MCP Server

A Model Context Protocol (MCP) server for Gmail integration in Claude Desktop with auto authentication support. This server enables AI assistants to manage Gmail through natural language interactions.

![](https://badge.mcpx.dev?type=server 'MCP Server')
[![smithery badge](https://smithery.ai/badge/@gongrzhe/server-gmail-autoauth-mcp)](https://smithery.ai/server/@gongrzhe/server-gmail-autoauth-mcp)


## Features

- Send emails with subject, content, attachments, and recipients
- Full support for international characters in subject lines and email content
- Read email messages by ID with advanced MIME structure handling
- View email attachments information (filenames, types, sizes)
- Search emails with various criteria (subject, sender, date range)
- **Comprehensive label management with ability to create, update, delete and list labels**
- List all available Gmail labels (system and user-defined)
- List emails in inbox, sent, or custom labels
- Mark emails as read/unread
- Move emails to different labels/folders
- Delete emails
- **Batch operations for efficiently processing multiple emails at once**
- Full integration with Gmail API
- Simple OAuth2 authentication flow with auto browser launch
- Support for both Desktop and Web application credentials
- Global credential storage for convenience

## Installation & Authentication

### Installing via Smithery

To install Gmail AutoAuth for Claude Desktop automatically via [Smithery](https://smithery.ai/server/@gongrzhe/server-gmail-autoauth-mcp):

```bash
npx -y @smithery/cli install @gongrzhe/server-gmail-autoauth-mcp --client claude
```

### Installing Manually
1. Create a Google Cloud Project and obtain credentials:

   a. Create a Google Cloud Project:
      - Go to [Google Cloud Console](https://console.cloud.google.com/)
      - Create a new project or select an existing one
      - Enable the Gmail API for your project

   b. Create OAuth 2.0 Credentials:
      - Go to ""APIs & Services"" > ""Credentials""
      - Click ""Create Credentials"" > ""OAuth client ID""
      - Choose either ""Desktop app"" or ""Web application"" as application type
      - Give it a name and click ""Create""
      - For Web application, add `http://localhost:3000/oauth2callback` to the authorized redirect URIs
      - Download the JSON file of your client's OAuth keys
      - Rename the key file to `gcp-oauth.keys.json`

2. Run Authentication:

   You can authenticate in two ways:

   a. Global Authentication (Recommended):
   ```bash
   # First time: Place gcp-oauth.keys.json in your home directory's .gmail-mcp folder
   mkdir -p ~/.gmail-mcp
   mv gcp-oauth.keys.json ~/.gmail-mcp/

   # Run authentication from anywhere
   npx @gongrzhe/server-gmail-autoauth-mcp auth
   ```

   b. Local Authentication:
   ```bash
   # Place gcp-oauth.keys.json in your current directory
   # The file will be automatically copied to global config
   npx @gongrzhe/server-gmail-autoauth-mcp auth
   ```

   The authentication process will:
   - Look for `gcp-oauth.keys.json` in the current directory or `~/.gmail-mcp/`
   - If found in current directory, copy it to `~/.gmail-mcp/`
   - Open your default browser for Google authentication
   - Save credentials as `~/.gmail-mcp/credentials.json`

   > **Note**: 
   > - After successful authentication, credentials are stored globally in `~/.gmail-mcp/` and can be used from any directory
   > - Both Desktop app and Web application credentials are supported
   > - For Web application credentials, make sure to add `http://localhost:3000/oauth2callback` to your authorized redirect URIs

3. Configure in Claude Desktop:

```json
{
  ""mcpServers"": {
    ""gmail"": {
      ""command"": ""npx"",
      ""args"": [
        ""@gongrzhe/server-gmail-autoauth-mcp""
      ]
    }
  }
}
```

### Docker Support

If you prefer using Docker:

1. Authentication:
```bash
docker run -i --rm \
  --mount type=bind,source=/path/to/gcp-oauth.keys.json,target=/gcp-oauth.keys.json \
  -v mcp-gmail:/gmail-server \
  -e GMAIL_OAUTH_PATH=/gcp-oauth.keys.json \
  -e ""GMAIL_CREDENTIALS_PATH=/gmail-server/credentials.json"" \
  -p 3000:3000 \
  mcp/gmail auth
```

2. Usage:
```json
{
  ""mcpServers"": {
    ""gmail"": {
      ""command"": ""docker"",
      ""args"": [
        ""run"",
        ""-i"",
        ""--rm"",
        ""-v"",
        ""mcp-gmail:/gmail-server"",
        ""-e"",
        ""GMAIL_CREDENTIALS_PATH=/gmail-server/credentials.json"",
        ""mcp/gmail""
      ]
    }
  }
}
```

### Cloud Server Authentication

For cloud server environments (like n8n), you can specify a custom callback URL during authentication:

```bash
npx @gongrzhe/server-gmail-autoauth-mcp auth https://gmail.gongrzhe.com/oauth2callback
```

#### Setup Instructions for Cloud Environment

1. **Configure Reverse Proxy:**
   - Set up your n8n container to expose a port for authentication
   - Configure a reverse proxy to forward traffic from your domain (e.g., `gmail.gongrzhe.com`) to this port

2. **DNS Configuration:**
   - Add an A record in your DNS settings to resolve your domain to your cloud server's IP address

3. **Google Cloud Platform Setup:**
   - In your Google Cloud Console, add your custom domain callback URL (e.g., `https://gmail.gongrzhe.com/oauth2callback`) to the authorized redirect URIs list

4. **Run Authentication:**
   ```bash
   npx @gongrzhe/server-gmail-autoauth-mcp auth https://gmail.gongrzhe.com/oauth2callback
   ```

5. **Configure in your application:**
   ```json
   {
     ""mcpServers"": {
       ""gmail"": {
         ""command"": ""npx"",
         ""args"": [
           ""@gongrzhe/server-gmail-autoauth-mcp""
         ]
       }
     }
   }
   ```

This approach allows authentication flows to work properly in environments where localhost isn't accessible, such as containerized applications or cloud servers.

## Available Tools

The server provides the following tools that can be used through Claude Desktop:

### 1. Send Email (`send_email`)
Sends a new email immediately.

```json
{
  ""to"": [""<EMAIL>""],
  ""subject"": ""Meeting Tomorrow"",
  ""body"": ""Hi,\n\nJust a reminder about our meeting tomorrow at 10 AM.\n\nBest regards"",
  ""cc"": [""<EMAIL>""],
  ""bcc"": [""<EMAIL>""]
}
```

### 2. Draft Email (`draft_email`)
Creates a draft email without sending it.

```json
{
  ""to"": [""<EMAIL>""],
  ""subject"": ""Draft Report"",
  ""body"": ""Here's the draft report for your review."",
  ""cc"": [""<EMAIL>""]
}
```

### 3. Read Email (`read_email`)
Retrieves the content of a specific email by its ID.

```json
{
  ""messageId"": ""182ab45cd67ef""
}
```

### 4. Search Emails (`search_emails`)
Searches for emails using Gmail search syntax.

```json
{
  ""query"": ""from:<EMAIL> after:2024/01/01 has:attachment"",
  ""maxResults"": 10
}
```

### 5. Modify Email (`modify_email`)
Adds or removes labels from emails (move to different folders, archive, etc.).

```json
{
  ""messageId"": ""182ab45cd67ef"",
  ""addLabelIds"": [""IMPORTANT""],
  ""removeLabelIds"": [""INBOX""]
}
```

### 6. Delete Email (`delete_email`)
Permanently deletes an email.

```json
{
  ""messageId"": ""182ab45cd67ef""
}
```

### 7. List Email Labels (`list_email_labels`)
Retrieves all available Gmail labels.

```json
{}
```

### 8. Create Label (`create_label`)
Creates a new Gmail label.

```json
{
  ""name"": ""Important Projects"",
  ""messageListVisibility"": ""show"",
  ""labelListVisibility"": ""labelShow""
}
```

### 9. Update Label (`update_label`)
Updates an existing Gmail label.

```json
{
  ""id"": ""Label_1234567890"",
  ""name"": ""Urgent Projects"",
  ""messageListVisibility"": ""show"",
  ""labelListVisibility"": ""labelShow""
}
```

### 10. Delete Label (`delete_label`)
Deletes a Gmail label.

```json
{
  ""id"": ""Label_1234567890""
}
```

### 11. Get or Create Label (`get_or_create_label`)
Gets an existing label by name or creates it if it doesn't exist.

```json
{
  ""name"": ""Project XYZ"",
  ""messageListVisibility"": ""show"",
  ""labelListVisibility"": ""labelShow""
}
```

### 12. Batch Modify Emails (`batch_modify_emails`)
Modifies labels for multiple emails in efficient batches.

```json
{
  ""messageIds"": [""182ab45cd67ef"", ""182ab45cd67eg"", ""182ab45cd67eh""],
  ""addLabelIds"": [""IMPORTANT""],
  ""removeLabelIds"": [""INBOX""],
  ""batchSize"": 50
}
```

### 13. Batch Delete Emails (`batch_delete_emails`)
Permanently deletes multiple emails in efficient batches.

```json
{
  ""messageIds"": [""182ab45cd67ef"", ""182ab45cd67eg"", ""182ab45cd67eh""],
  ""batchSize"": 50
}
```

## Advanced Search Syntax

The `search_emails` tool supports Gmail's powerful search operators:

| Operator | Example | Description |
|----------|---------|-------------|
| `from:` | `from:<EMAIL>` | Emails from a specific sender |
| `to:` | `to:<EMAIL>` | Emails sent to a specific recipient |
| `subject:` | `subject:""meeting notes""` | Emails with specific text in the subject |
| `has:attachment` | `has:attachment` | Emails with attachments |
| `after:` | `after:2024/01/01` | Emails received after a date |
| `before:` | `before:2024/02/01` | Emails received before a date |
| `is:` | `is:unread` | Emails with a specific state |
| `label:` | `label:work` | Emails with a specific label |

You can combine multiple operators: `from:<EMAIL> after:2024/01/01 has:attachment`

## Advanced Features

### Email Content Extraction

The server intelligently extracts email content from complex MIME structures:

- Prioritizes plain text content when available
- Falls back to HTML content if plain text is not available
- Handles multi-part MIME messages with nested parts
- Processes attachments information (filename, type, size)
- Preserves original email headers (From, To, Subject, Date)

### International Character Support

The server fully supports non-ASCII characters in email subjects and content, including:
- Turkish, Chinese, Japanese, Korean, and other non-Latin alphabets
- Special characters and symbols
- Proper encoding ensures correct display in email clients

### Comprehensive Label Management

The server provides a complete set of tools for managing Gmail labels:

- **Create Labels**: Create new labels with customizable visibility settings
- **Update Labels**: Rename labels or change their visibility settings
- **Delete Labels**: Remove user-created labels (system labels are protected)
- **Find or Create**: Get a label by name or automatically create it if not found
- **List All Labels**: View all system and user labels with detailed information
- **Label Visibility Options**: Control how labels appear in message and label lists

Label visibility settings include:
- `messageListVisibility`: Controls whether the label appears in the message list (`show` or `hide`)
- `labelListVisibility`: Controls how the label appears in the label list (`labelShow`, `labelShowIfUnread`, or `labelHide`)

These label management features enable sophisticated organization of emails directly through Claude, without needing to switch to the Gmail interface.

### Batch Operations

The server includes efficient batch processing capabilities:

- Process up to 50 emails at once (configurable batch size)
- Automatic chunking of large email sets to avoid API limits
- Detailed success/failure reporting for each operation
- Graceful error handling with individual retries
- Perfect for bulk inbox management and organization tasks

## Security Notes

- OAuth credentials are stored securely in your local environment (`~/.gmail-mcp/`)
- The server uses offline access to maintain persistent authentication
- Never share or commit your credentials to version control
- Regularly review and revoke unused access in your Google Account settings
- Credentials are stored globally but are only accessible by the current user

## Troubleshooting

1. **OAuth Keys Not Found**
   - Make sure `gcp-oauth.keys.json` is in either your current directory or `~/.gmail-mcp/`
   - Check file permissions

2. **Invalid Credentials Format**
   - Ensure your OAuth keys file contains either `web` or `installed` credentials
   - For web applications, verify the redirect URI is correctly configured

3. **Port Already in Use**
   - If port 3000 is already in use, please free it up before running authentication
   - You can find and stop the process using that port

4. **Batch Operation Failures**
   - If batch operations fail, they automatically retry individual items
   - Check the detailed error messages for specific failures
   - Consider reducing the batch size if you encounter rate limiting

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.


## Running evals

The evals package loads an mcp client that then runs the index.ts file, so there is no need to rebuild between tests. You can load environment variables by prefixing the npx command. Full documentation can be found [here](https://www.mcpevals.io/docs).

```bash
OPENAI_API_KEY=your-key  npx mcp-eval src/evals/evals.ts src/index.ts
```

## License

MIT

## Support

If you encounter any issues or have questions, please file an issue on the GitHub repository.","Star
 258",2025-05-07 15:22:02.281957
https://github.com/teddyzxcv/ntfy-mcp,teddyzxcv/ntfy-mcp,"# ntfy-mcp: Your Friendly Task Completion Notifier

Welcome to ntfy-mcp, the MCP server that keeps you caffeinated and informed! 🚀☕️

This handy little server integrates with the Model Context Protocol to send you delightful ntfy notifications whenever your AI assistant completes a task. Because let's face it - you deserve that tea break while your code writes itself.

<a href=""https://glama.ai/mcp/servers/@teddyzxcv/ntfy-mcp"">
  <img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/@teddyzxcv/ntfy-mcp/badge"" />
</a>

## Getting Started: The Quick Brew Guide

### Step 1: Clone & Navigate
```bash
git clone https://github.com/teddyzxcv/ntfy-mcp.git
cd ntfy-mcp
```

### Step 2: Install 
```bash
npm install
```

### Step 3: Build 
```bash
npm run build
```

### Step 4: Connect
Choose your adventure:

**Manual Start:**
```bash
npm start
```

**Cline Configuration:**
```json
""ntfy-mcp"": {
  ""command"": ""node"",
  ""args"": [
    ""/path/to/ntfy-mcp/build/index.js""
  ],
  ""env"": {
    ""NTFY_TOPIC"": ""<your topic name>""
  },
  ""autoApprove"": [
    ""notify_user"" // Highly recommended for maximum chill
  ]
}
```

### Step 5: Get Notified in Style
1. Download the [ntfy app](https://ntfy.sh) on your phone
2. Subscribe to your chosen topic
3. Kick back and relax

### Step 6: The Magic Command
Write a prompt like this, otherwise the function won't call 
(tried use `Custom Instructions` in cline, but they are in the ring 3, so model just forget about it)
```
Write me a hello world in python, notify me when the task is done
```

### Step 7: Enjoy Your Beverage of Choice
☕️🍵 Your notification will arrive when the task is complete. No peeking!

## How It Works (The Technical Tea)

This MCP server integrates seamlessly with the Model Context Protocol, acting as your personal notification butler. When tasks are completed, it sends notifications via ntfy, keeping you informed without interrupting your flow.

## Dependencies: The Secret Sauce

- [@modelcontextprotocol/sdk](https://github.com/modelcontextprotocol/typescript-sdk)
- [node-fetch](https://github.com/node-fetch/node-fetch)
- [dotenv](https://github.com/motdotla/dotenv)
- [zod](https://github.com/colinhacks/zod)



## License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.

```
Copyright 2025 Casey Hand @cyanheads

Licensed under the Apache License, Version 2.0 (the ""License"");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an ""AS IS"" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
```

---

Now go forth and code with confidence, knowing your notifications are in good hands! 🎉
","Star
 19",2025-05-07 15:22:02.281957
https://github.com/AudienseCo/mcp-audiense-insights,AudienseCo/mcp-audiense-insights,"# 🏆 Audiense Insights MCP Server

This server, based on the [Model Context Protocol (MCP)](https://github.com/modelcontextprotocol), allows **Claude** or any other MCP-compatible client to interact with your [Audiense Insights](https://www.audiense.com/) account. It extracts **marketing insights and audience analysis** from Audiense reports, covering **demographic, cultural, influencer, and content engagement analysis**.
---

## 🚀 Prerequisites

Before using this server, ensure you have:

- **Node.js** (v18 or higher)
- **Claude Desktop App**
- **Audiense Insights Account** with API credentials
- **X/Twitter API Bearer Token** _(optional, for enriched influencer data)_

---

## ⚙️ Configuring Claude Desktop

1. Open the configuration file for Claude Desktop:

   - **MacOS:**
     ```bash
     code ~/Library/Application\ Support/Claude/claude_desktop_config.json
     ```
   - **Windows:**
     ```bash
     code %AppData%\Claude\claude_desktop_config.json
     ```

2. Add or update the following configuration:

   ```json
   ""mcpServers"": {
     ""audiense-insights"": {
       ""command"": ""npx"",
       ""args"": [
        ""-y"",
         ""@AudienseCo/mcp-audiense-insights""
       ],
       ""env"": {
         ""AUDIENSE_CLIENT_ID"": ""your_client_id_here"",
         ""AUDIENSE_CLIENT_SECRET"": ""your_client_secret_here"",
         ""TWITTER_BEARER_TOKEN"": ""your_token_here""
       }          
     }     
   }

3.	Save the file and restart Claude Desktop.

## 🛠️ Available Tools
### 📌 `get-reports`
**Description**: Retrieves the list of **Audiense insights reports** owned by the authenticated user.

- **Parameters**: _None_
- **Response**:
  - List of reports in JSON format.

---

### 📌 `get-report-info`
**Description**: Fetches detailed information about a **specific intelligence report**, including:
  - Status
  - Segmentation type
  - Audience size
  - Segments
  - Access links

- **Parameters**:
  - `report_id` _(string)_: The ID of the intelligence report.

- **Response**:
  - Full report details in JSON format.
  - If the report is still processing, returns a message indicating the pending status.

---

### 📌 `get-audience-insights`
**Description**: Retrieves **aggregated insights** for a given **audience**, including:
  - **Demographics**: Gender, age, country.
  - **Behavioral traits**: Active hours, platform usage.
  - **Psychographics**: Personality traits, interests.
  - **Socioeconomic factors**: Income, education status.

- **Parameters**:
  - `audience_insights_id` _(string)_: The ID of the audience insights.
  - `insights` _(array of strings, optional)_: List of specific insight names to filter.

- **Response**:
  - Insights formatted as a structured text list.

---

### 📌 `get-baselines`
**Description**: Retrieves available **baseline audiences**, optionally filtered by **country**.

- **Parameters**:
  - `country` _(string, optional)_: ISO country code to filter by.

- **Response**:
  - List of baseline audiences in JSON format.

---

### 📌 `get-categories`
**Description**: Retrieves the list of **available affinity categories** that can be used in influencer comparisons.

- **Parameters**: _None_
- **Response**:
  - List of categories in JSON format.

---

### 📌 `compare-audience-influencers`
**Description**: Compares **influencers** of a given audience with a **baseline audience**. The baseline is determined as follows:
  - If a **single country** represents more than 50% of the audience, that country is used as the baseline.
  - Otherwise, the **global baseline** is used.
  - If a **specific segment** is selected, the full audience is used as the baseline.

Each influencer comparison includes:
  - **Affinity (%)** – How well the influencer aligns with the audience.
  - **Baseline Affinity (%)** – The influencer’s affinity within the baseline audience.
  - **Uniqueness Score** – How distinct the influencer is compared to the baseline.

- **Parameters**:
  - `audience_influencers_id` _(string)_: ID of the audience influencers.
  - `baseline_audience_influencers_id` _(string)_: ID of the baseline audience influencers.
  - `cursor` _(number, optional)_: Pagination cursor.
  - `count` _(number, optional)_: Number of items per page (default: 200).
  - `bio_keyword` _(string, optional)_: Filter influencers by **bio keyword**.
  - `entity_type` _(enum: `person` | `brand`, optional)_: Filter by entity type.
  - `followers_min` _(number, optional)_: Minimum number of followers.
  - `followers_max` _(number, optional)_: Maximum number of followers.
  - `categories` _(array of strings, optional)_: Filter influencers by **categories**.
  - `countries` _(array of strings, optional)_: Filter influencers by **country ISO codes**.

- **Response**:
  - List of influencers with **affinity scores, baseline comparison, and uniqueness scores** in JSON format.

---

### 📌 `get-audience-content`
**Description**: Retrieves **audience content engagement details**, including:
  - **Liked Content**: Most popular posts, domains, emojis, hashtags, links, media, and a word cloud.
  - **Shared Content**: Most shared content categorized similarly.
  - **Influential Content**: Content from influential accounts.

Each category contains:
  - `popularPost`: Most engaged posts.
  - `topDomains`: Most mentioned domains.
  - `topEmojis`: Most used emojis.
  - `topHashtags`: Most used hashtags.
  - `topLinks`: Most shared links.
  - `topMedia`: Shared media.
  - `wordcloud`: Most frequently used words.

- **Parameters**:
  - `audience_content_id` _(string)_: The ID of the audience content.

- **Response**:
  - Content engagement data in JSON format.

---

### 📌 `report-summary`
**Description**: Generates a **comprehensive summary** of an Audiense report, including:
  - Report metadata (title, segmentation type)
  - Full audience size
  - Detailed segment information
  - **Top insights** for each segment (bio keywords, demographics, interests)
  - **Top influencers** for each segment with comparison metrics

- **Parameters**:
  - `report_id` _(string)_: The ID of the intelligence report to summarize.

- **Response**:
  - Complete report summary in JSON format with structured data for each segment
  - For pending reports: Status message indicating the report is still processing
  - For reports without segments: Message indicating there are no segments to analyze

## 💡 Predefined Prompts

This server includes a preconfigured prompts
- `audiense-demo`: Helps analyze Audiense reports interactively.
- `segment-matching`: A prompt to match and compare audience segments across Audiense reports, identifying similarities, unique traits, and key insights based on demographics, interests, influencers, and engagement patterns.


**Usage:**
- Accepts a reportName argument to find the most relevant report.
- If an ID is provided, it searches by report ID instead.

Use case: Structured guidance for audience analysis.

## 🛠️ Troubleshooting

### Tools Not Appearing in Claude
1.	Check Claude Desktop logs:

```
tail -f ~/Library/Logs/Claude/mcp*.log
```
2.	Verify environment variables are set correctly.
3.	Ensure the absolute path to index.js is correct.

### Authentication Issues
- Double-check OAuth credentials.
- Ensure the refresh token is still valid.
- Verify that the required API scopes are enabled.

## 📜 Viewing Logs

To check server logs:

### For MacOS/Linux:
```
tail -n 20 -f ~/Library/Logs/Claude/mcp*.log
```

### For Windows:
```
Get-Content -Path ""$env:AppData\Claude\Logs\mcp*.log"" -Wait -Tail 20
```

## 🔐 Security Considerations

- Keep API credentials secure – never expose them in public repositories.
- Use environment variables to manage sensitive data.

## 📄 License

This project is licensed under the Apache 2.0 License. See the LICENSE file for more details.
","Star
 8",2025-05-07 15:22:02.281957
https://github.com/heroku/heroku-mcp-server,heroku/heroku-mcp-server,"# heroku-mcp-server

[![smithery badge](https://smithery.ai/badge/@heroku/heroku-mcp-server)](https://smithery.ai/server/@heroku/heroku-mcp-server)
> The Heroku Platform MCP Server works on Common Runtime, Cedar Private and Shield Spaces, and Fir Private Spaces.

## Overview

The Heroku Platform MCP Server is a specialized Model Context Protocol (MCP) implementation designed to facilitate
seamless interaction between large language models (LLMs) and the Heroku Platform. This server provides a robust set of
tools and capabilities that enable LLMs to read, manage, and operate Heroku Platform resources.

Key Features:

- Direct interaction with Heroku Platform resources through LLM-driven tools
- Secure and authenticated access to Heroku Platform APIs, leveraging the Heroku CLI
- Natural language interface for Heroku Platform interactions

Note: The Heroku Platform MCP Server is currently in early development. As we continue to enhance and refine the
implementation, the available functionality and tools may evolve. We welcome feedback and contributions to help shape
the future of this project.

## Authentication

Generate a Heroku authorization token with one of these methods:

- Use the Heroku CLI command:

  ```sh
    heroku authorizations:create
  ```

- Use an existing token in the CLI

  ```sh
    heroku auth:token
  ```

  Copy the token and use it as your `HEROKU_API_KEY` in the following steps.

- In your [Heroku Dashboard](https://dashboard.heroku.com/account/applications):
  1. Select your avatar, then select **Account Settings**.
  2. Open the Applications tab.
  3. Next to **Authorizations**, click **Create authorization**.

## Configure the Heroku Platform MCP Server

You can configure Claude Desktop, Zed, Cursor, Windsurf and others to work with the Heroku Platform MCP Server.

### [Claude Desktop](https://claude.ai/download)

Add this snippet to your `claude_desktop_config.json`:

```json
{
  ""mcpServers"": {
    ""heroku"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""@heroku/mcp-server""],
      ""env"": {
        ""HEROKU_API_KEY"": ""<YOUR_HEROKU_AUTH_TOKEN>""
      }
    }
  }
}
```

### [Zed](https://github.com/zed-industries/zed)

Add this snippet to your Zed `settings.json`:

```json
{
  ""context_servers"": {
    ""heroku"": {
      ""command"": {
        ""path"": ""npx"",
        ""args"": [""-y"", ""@heroku/mcp-server""],
        ""env"": {
          ""HEROKU_API_KEY"": ""<YOUR_HEROKU_AUTH_TOKEN>""
        }
      }
    }
  }
}
```

### [Cursor](https://www.cursor.com/)

> **Note:** Both the simple and explicit forms work, but the key should be `""heroku""` (not `""heroku-mcp-server""`) for
> maximum compatibility with agent tools.

Add this snippet to your Cursor `mcp.json`:

**Simple form:**

```json
{
  ""mcpServers"": {
    ""heroku"": {
      ""command"": ""npx -y @heroku/mcp-server"",
      ""env"": {
        ""HEROKU_API_KEY"": ""<YOUR_HEROKU_AUTH_TOKEN>""
      }
    }
  }
}
```

**Explicit form:**

```json
{
  ""mcpServers"": {
    ""heroku"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""@heroku/mcp-server""],
      ""env"": {
        ""HEROKU_API_KEY"": ""<YOUR_HEROKU_AUTH_TOKEN>""
      }
    }
  }
}
```

### [Windsurf](https://www.windsurf.com/)

Add this snippet to your Windsurf `mcp_config.json`:

```json
{
  ""mcpServers"": {
    ""heroku"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""@heroku/mcp-server""],
      ""env"": {
        ""HEROKU_API_KEY"": ""<YOUR_HEROKU_AUTH_TOKEN>""
      }
    }
  }
}
```

### [Cline](https://cline.bot)

Add this snippet to your Cline `config.json`:

```json
{
  ""mcpServers"": {
    ""heroku"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""@heroku/mcp-server""],
      ""env"": {
        ""HEROKU_API_KEY"": ""<YOUR_HEROKU_AUTH_TOKEN>""
      }
    }
  }
}
```

### [VSCode](https://code.visualstudio.com/)

Add this snippet to your VSCode `settings.json` or `.vscode/mcp.json`:

```json
{
  ""mcp"": {
    ""servers"": {
      ""heroku"": {
        ""type"": ""stdio"",
        ""command"": ""npx"",
        ""args"": [""-y"", ""@heroku/mcp-server""],
        ""env"": {
          ""HEROKU_API_KEY"": ""<YOUR_HEROKU_AUTH_TOKEN>""
        }
      }
    }
  }
}
```

### [Trae](https://trae.ai)

Add this snippet to your Trae `mcp_settings.json`:

```json
{
  ""mcpServers"": {
    ""heroku"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""@heroku/mcp-server""],
      ""env"": {
        ""HEROKU_API_KEY"": ""<YOUR_HEROKU_AUTH_TOKEN>""
      }
    }
  }
}
```

## Available Tools

### Application Management

- `list_apps` - List all Heroku apps. You can filter apps by personal, collaborator, team, or space.
- `get_app_info` - Get detailed information about an app, including its configuration, dynos, and add-ons.
- `create_app` - Create a new app with customizable settings for region, team, and space.
- `rename_app` - Rename an existing app.
- `transfer_app` - Transfer ownership of an app to another user or team.
- `deploy_to_heroku` - Deploy projects to Heroku with an `app.json` configuration, supporting team deployments, private
  spaces, and environment setups.
- `deploy_one_off_dyno` - Execute code or commands in a sandboxed environment on a Heroku one-off dyno. Supports file
  creation, network access, environment variables, and automatic cleanup. Ideal for running scripts, tests, or temporary
  workloads.

### Process & Dyno Management

- `ps_list` - List all dynos for an app.
- `ps_scale` - Scale the number of dynos up or down, or resize dynos.
- `ps_restart` - Restart specific dynos, process types, or all dynos.

### Add-ons

- `list_addons` - List all add-ons for all apps or for a specific app.
- `get_addon_info` - Get detailed information about a specific add-on.
- `create_addon` - Provision a new add-on for an app.

### Maintenance & Logs

- `maintenance_on` - Enable maintenance mode for an app.
- `maintenance_off` - Disable maintenance mode for an app.
- `get_app_logs` - View application logs.

### Pipeline Management

- `pipelines_create` - Create a new pipeline.
- `pipelines_promote` - Promote apps to the next stage in a pipeline.
- `pipelines_list` - List available pipelines.
- `pipelines_info` - Get detailed pipeline information.

### Team & Space Management

- `list_teams` - List teams you belong to.
- `list_private_spaces` - List available spaces.

### PostgreSQL Database Management

- `pg_psql` - Execute SQL queries against the Heroku PostgreSQL database.
- `pg_info` - Display detailed database information.
- `pg_ps` - View active queries and execution details.
- `pg_locks` - View database locks and identify blocking transactions.
- `pg_outliers` - Identify resource-intensive queries.
- `pg_credentials` - Manage database credentials and access.
- `pg_kill` - Terminate specific database processes.
- `pg_maintenance` - Show database maintenance information.
- `pg_backups` - Manage database backups and schedules.
- `pg_upgrade` - Upgrade PostgreSQL to a newer version.

## Debugging

You can use the [MCP inspector](https://modelcontextprotocol.io/docs/tools/inspector) or the
[VS Code Run and Debug function](https://code.visualstudio.com/docs/debugtest/debugging#_start-a-debugging-session) to
run and debug the server.

1. Link the project as a global CLI using `npm link` from the project root.
2. Build with `npm run build:dev` or watch for file changes and build automatically with `npm run build:watch`.

### Use the MCP Inspector

Use the MCP inspector with no breakpoints in the code:

```
# Breakpoints are not available
npx @modelcontextprotocol/inspector heroku-mcp-server
```

Alternatively, if you installed the package in a specific directory or are actively developing on the Heroku MCP server:

```
cd /path/to/servers
npx @modelcontextprotocol/inspector dist/index.js
```

### Use the VS Code Run and Debug Function

Use the VS Code
[Run and Debug launcher](https://code.visualstudio.com/docs/debugtest/debugging#_start-a-debugging-session) with fully
functional breakpoints in the code:

1. Locate and select the run debug.
2. Select the configuration labeled ""`MCP Server Launcher`"" in the dropdown.
3. Select the run/debug button.

### VS Code / Cursor Debugging Setup

To set up local debugging with breakpoints:

1. Store your Heroku auth token in the VS Code user settings:

   - Open the Command Palette (Cmd/Ctrl + Shift + P).
   - Type `Preferences: Open User Settings (JSON)`.
   - Add the following snippet:

   ```json
   {
     ""heroku.mcp.authToken"": ""your-token-here""
   }
   ```

2. Create or update `.vscode/launch.json`:

   ```json
   {
     ""version"": ""0.2.0"",
     ""configurations"": [
       {
         ""type"": ""node"",
         ""request"": ""launch"",
         ""name"": ""MCP Server Launcher"",
         ""skipFiles"": [""<node_internals>/**""],
         ""program"": ""${workspaceFolder}/node_modules/@modelcontextprotocol/inspector/bin/cli.js"",
         ""outFiles"": [""${workspaceFolder}/**/dist/**/*.js""],
         ""env"": {
           ""HEROKU_API_KEY"": ""${config:heroku.mcp.authToken}"",
           ""DEBUG"": ""true""
         },
         ""args"": [""heroku-mcp-server""],
         ""sourceMaps"": true,
         ""console"": ""integratedTerminal"",
         ""internalConsoleOptions"": ""neverOpen"",
         ""preLaunchTask"": ""npm: build:watch""
       },
       {
         ""type"": ""node"",
         ""request"": ""attach"",
         ""name"": ""Attach to Debug Hook Process"",
         ""port"": 9332,
         ""skipFiles"": [""<node_internals>/**""],
         ""sourceMaps"": true,
         ""outFiles"": [""${workspaceFolder}/dist/**/*.js""]
       },
       {
         ""type"": ""node"",
         ""request"": ""attach"",
         ""name"": ""Attach to REPL Process"",
         ""port"": 9333,
         ""skipFiles"": [""<node_internals>/**""],
         ""sourceMaps"": true,
         ""outFiles"": [""${workspaceFolder}/dist/**/*.js""]
       }
     ],
     ""compounds"": [
       {
         ""name"": ""Attach to MCP Server"",
         ""configurations"": [""Attach to Debug Hook Process"", ""Attach to REPL Process""]
       }
     ]
   }
   ```

3. Create `.vscode/tasks.json`:

   ```json
   {
     ""version"": ""2.0.0"",
     ""tasks"": [
       {
         ""type"": ""npm"",
         ""script"": ""build:watch"",
         ""group"": {
           ""kind"": ""build"",
           ""isDefault"": true
         },
         ""problemMatcher"": [""$tsc""]
       }
     ]
   }
   ```

4. (Optional) Set breakpoints in your TypeScript files.

5. Press F5 or use the **`Run and Debug`** sidebar.

Note: the debugger automatically builds your TypeScript files before launching.

### Installing via Smithery

To install Heroku Platform MCP Server for Claude Desktop automatically via [Smithery](https://smithery.ai/server/@heroku/heroku-mcp-server):

```bash
npx -y @smithery/cli install @heroku/heroku-mcp-server --client claude
```
","Star
 34",2025-05-07 15:22:02.281957
https://github.com/GongRzhe/Langflow-DOC-QA-SERVER,GongRzhe/Langflow-DOC-QA-SERVER,"# Langflow-DOC-QA-SERVER
![](https://badge.mcpx.dev?type=server 'MCP Server')
[![smithery badge](https://smithery.ai/badge/@GongRzhe/Langflow-DOC-QA-SERVER)](https://smithery.ai/server/@GongRzhe/Langflow-DOC-QA-SERVER)

<a href=""https://glama.ai/mcp/servers/@GongRzhe/Langflow-DOC-QA-SERVER"">
  <img width=""380"" height=""200"" src=""https://glama.ai/mcp/servers/@GongRzhe/Langflow-DOC-QA-SERVER/badge"" alt=""Langflow Document Q&A Server MCP server"" />
</a>

A Model Context Protocol server for document Q&A powered by Langflow

This is a TypeScript-based MCP server that implements a document Q&A system. It demonstrates core MCP concepts by providing a simple interface to query documents through a Langflow backend.

## Prerequisites

### 1. Create Langflow Document Q&A Flow
1. Open Langflow and create a new flow from the ""Document Q&A"" template
2. Configure your flow with necessary components (ChatInput, File Upload, LLM, etc.)
3. Save your flow

![image](https://github.com/user-attachments/assets/0df89122-d7a8-4d18-9a39-57af4240b7ac)


### 2. Get Flow API Endpoint
1. Click the ""API"" button in the top right corner of Langflow
2. Copy the API endpoint URL from the cURL command
   Example: `http://127.0.0.1:7860/api/v1/run/<flow-id>?stream=false`
3. Save this URL as it will be needed for the `API_ENDPOINT` configuration

![image](https://github.com/user-attachments/assets/6c9ba5e2-4aa3-4a8c-89c2-adc3d400c828)


## Features

### Tools
- `query_docs` - Query the document Q&A system
  - Takes a query string as input
  - Returns responses from the Langflow backend

## Development

Install dependencies:
```bash
npm install
```

Build the server:
```bash
npm run build
```

For development with auto-rebuild:
```bash
npm run watch
```

## Installation

To use with Claude Desktop, add the server config:

On MacOS: `~/Library/Application Support/Claude/claude_desktop_config.json`
On Windows: `%APPDATA%/Claude/claude_desktop_config.json`

```json
{
  ""mcpServers"": {
    ""langflow-doc-qa-server"": {
      ""command"": ""node"",
      ""args"": [
        ""/path/to/doc-qa-server/build/index.js""
      ],
      ""env"": {
        ""API_ENDPOINT"": ""http://127.0.0.1:7860/api/v1/run/480ec7b3-29d2-4caa-b03b-e74118f35fac""
      }
    }
  }
}
```

![image](https://github.com/user-attachments/assets/b0821378-ed13-4225-81a9-8beab1dc4b48)

### Installing via Smithery

To install Document Q&A Server for Claude Desktop automatically via [Smithery](https://smithery.ai/server/@GongRzhe/Langflow-DOC-QA-SERVER):

```bash
npx -y @smithery/cli install @GongRzhe/Langflow-DOC-QA-SERVER --client claude
```

### Environment Variables

The server supports the following environment variables for configuration:

- `API_ENDPOINT`: The endpoint URL for the Langflow API service. Defaults to `http://127.0.0.1:7860/api/v1/run/480ec7b3-29d2-4caa-b03b-e74118f35fac` if not specified.

### Debugging

Since MCP servers communicate over stdio, debugging can be challenging. We recommend using the [MCP Inspector](https://github.com/modelcontextprotocol/inspector), which is available as a package script:

```bash
npm run inspector
```

The Inspector will provide a URL to access debugging tools in your browser.

## 📜 License

This project is licensed under the MIT License.","Star
 7",2025-05-07 15:22:02.281957
https://github.com/SimonB97/win-cli-mcp-server,SimonB97/win-cli-mcp-server,"# Windows CLI MCP Server
[![NPM Downloads](https://img.shields.io/npm/dt/@simonb97/server-win-cli.svg?style=flat)](https://www.npmjs.com/package/@simonb97/server-win-cli)
[![NPM Version](https://img.shields.io/npm/v/@simonb97/server-win-cli.svg?style=flat)](https://www.npmjs.com/package/@simonb97/server-win-cli?activeTab=versions)
[![smithery badge](https://smithery.ai/badge/@simonb97/server-win-cli)](https://smithery.ai/server/@simonb97/server-win-cli)

[MCP server](https://modelcontextprotocol.io/introduction) for secure command-line interactions on Windows systems, enabling controlled access to PowerShell, CMD, Git Bash shells, and remote systems via SSH. It allows MCP clients (like [Claude Desktop](https://claude.ai/download)) to perform operations on your system, similar to [Open Interpreter](https://github.com/OpenInterpreter/open-interpreter).

>[!IMPORTANT]
> This MCP server provides direct access to your system's command line interface and remote systems via SSH. When enabled, it grants access to your files, environment variables, command execution capabilities, and remote server management.
>
> - Review and restrict allowed paths and SSH connections
> - Enable directory restrictions
> - Configure command blocks
> - Consider security implications
>
> See [Configuration](#configuration) for more details.

- [Features](#features)
- [Usage with Claude Desktop](#usage-with-claude-desktop)
- [Configuration](#configuration)
  - [Configuration Locations](#configuration-locations)
  - [Default Configuration](#default-configuration)
  - [Configuration Settings](#configuration-settings)
    - [Security Settings](#security-settings)
    - [Shell Configuration](#shell-configuration)
    - [SSH Configuration](#ssh-configuration)
- [API](#api)
  - [Tools](#tools)
  - [Resources](#resources)
- [Security Considerations](#security-considerations)
- [License](#license)

## Features

- **Multi-Shell Support**: Execute commands in PowerShell, Command Prompt (CMD), and Git Bash
- **SSH Support**: Execute commands on remote systems via SSH
- **Resource Exposure**: View SSH connections, current directory, and configuration as MCP resources
- **Security Controls**:
  - Command and SSH command blocking (full paths, case variations)
  - Working directory validation
  - Maximum command length limits
  - Command logging and history tracking
  - Smart argument validation
- **Configurable**:
  - Custom security rules
  - Shell-specific settings
  - SSH connection profiles
  - Path restrictions
  - Blocked command lists

See the [API](#api) section for more details on the tools and resources the server provides to MCP clients.

**Note**: The server will only allow operations within configured directories, with allowed commands, and on configured SSH connections.

## Usage with Claude Desktop

Add this to your `claude_desktop_config.json`:

```json
{
  ""mcpServers"": {
    ""windows-cli"": {
      ""command"": ""npx"",
      ""args"": [""-y"", ""@simonb97/server-win-cli""]
    }
  }
}
```

For use with a specific config file, add the `--config` flag:

```json
{
  ""mcpServers"": {
    ""windows-cli"": {
      ""command"": ""npx"",
      ""args"": [
        ""-y"",
        ""@simonb97/server-win-cli"",
        ""--config"",
        ""path/to/your/config.json""
      ]
    }
  }
}
```

After configuring, you can:
- Execute commands directly using the available tools
- View configured SSH connections and server configuration in the Resources section
- Manage SSH connections through the provided tools

## Configuration

The server uses a JSON configuration file to customize its behavior. You can specify settings for security controls, shell configurations, and SSH connections.

1. To create a default config file, either:

**a)** copy `config.json.example` to `config.json`, or

**b)** run:

```bash
npx @simonb97/server-win-cli --init-config ./config.json
```

2. Then set the `--config` flag to point to your config file as described in the [Usage with Claude Desktop](#usage-with-claude-desktop) section.

### Configuration Locations

The server looks for configuration in the following locations (in order):

1. Path specified by `--config` flag
2. ./config.json in current directory
3. ~/.win-cli-mcp/config.json in user's home directory

If no configuration file is found, the server will use a default (restricted) configuration:

### Default Configuration

**Note**: The default configuration is designed to be restrictive and secure. Find more details on each setting in the [Configuration Settings](#configuration-settings) section.

```json
{
  ""security"": {
    ""maxCommandLength"": 2000,
    ""blockedCommands"": [
      ""rm"",
      ""del"",
      ""rmdir"",
      ""format"",
      ""shutdown"",
      ""restart"",
      ""reg"",
      ""regedit"",
      ""net"",
      ""netsh"",
      ""takeown"",
      ""icacls""
    ],
    ""blockedArguments"": [
      ""--exec"",
      ""-e"",
      ""/c"",
      ""-enc"",
      ""-encodedcommand"",
      ""-command"",
      ""--interactive"",
      ""-i"",
      ""--login"",
      ""--system""
    ],
    ""allowedPaths"": [""User's home directory"", ""Current working directory""],
    ""restrictWorkingDirectory"": true,
    ""logCommands"": true,
    ""maxHistorySize"": 1000,
    ""commandTimeout"": 30,
    ""enableInjectionProtection"": true
  },
  ""shells"": {
    ""powershell"": {
      ""enabled"": true,
      ""command"": ""powershell.exe"",
      ""args"": [""-NoProfile"", ""-NonInteractive"", ""-Command""],
      ""blockedOperators"": [""&"", ""|"", "";"", ""`""]
    },
    ""cmd"": {
      ""enabled"": true,
      ""command"": ""cmd.exe"",
      ""args"": [""/c""],
      ""blockedOperators"": [""&"", ""|"", "";"", ""`""]
    },
    ""gitbash"": {
      ""enabled"": true,
      ""command"": ""C:\\Program Files\\Git\\bin\\bash.exe"",
      ""args"": [""-c""],
      ""blockedOperators"": [""&"", ""|"", "";"", ""`""]
    }
  },
  ""ssh"": {
    ""enabled"": false,
    ""defaultTimeout"": 30,
    ""maxConcurrentSessions"": 5,
    ""keepaliveInterval"": 10000,
    ""keepaliveCountMax"": 3,
    ""readyTimeout"": 20000,
    ""connections"": {}
  }
}
```

### Configuration Settings

The configuration file is divided into three main sections: `security`, `shells`, and `ssh`.

#### Security Settings

```json
{
  ""security"": {
    // Maximum allowed length for any command
    ""maxCommandLength"": 1000,

    // Commands to block - blocks both direct use and full paths
    // Example: ""rm"" blocks both ""rm"" and ""C:\\Windows\\System32\\rm.exe""
    // Case-insensitive: ""del"" blocks ""DEL.EXE"", ""del.cmd"", etc.
    ""blockedCommands"": [
      ""rm"", // Delete files
      ""del"", // Delete files
      ""rmdir"", // Delete directories
      ""format"", // Format disks
      ""shutdown"", // Shutdown system
      ""restart"", // Restart system
      ""reg"", // Registry editor
      ""regedit"", // Registry editor
      ""net"", // Network commands
      ""netsh"", // Network commands
      ""takeown"", // Take ownership of files
      ""icacls"" // Change file permissions
    ],

    // Arguments that will be blocked when used with any command
    // Note: Checks each argument independently - ""cd warm_dir"" won't be blocked just because ""rm"" is in blockedCommands
    ""blockedArguments"": [
      ""--exec"", // Execution flags
      ""-e"", // Short execution flags
      ""/c"", // Command execution in some shells
      ""-enc"", // PowerShell encoded commands
      ""-encodedcommand"", // PowerShell encoded commands
      ""-command"", // Direct PowerShell command execution
      ""--interactive"", // Interactive mode which might bypass restrictions
      ""-i"", // Short form of interactive
      ""--login"", // Login shells might have different permissions
      ""--system"" // System level operations
    ],

    // List of directories where commands can be executed
    ""allowedPaths"": [""C:\\Users\\<USER>\\Projects""],

    // If true, commands can only run in allowedPaths
    ""restrictWorkingDirectory"": true,

    // If true, saves command history
    ""logCommands"": true,

    // Maximum number of commands to keep in history
    ""maxHistorySize"": 1000,

    // Timeout for command execution in seconds (default: 30)
    ""commandTimeout"": 30,

    // Enable or disable protection against command injection (covers ;, &, |, \`)
    ""enableInjectionProtection"": true
  }
}
```

#### Shell Configuration

```json
{
  ""shells"": {
    ""powershell"": {
      // Enable/disable this shell
      ""enabled"": true,
      // Path to shell executable
      ""command"": ""powershell.exe"",
      // Default arguments for the shell
      ""args"": [""-NoProfile"", ""-NonInteractive"", ""-Command""],
      // Optional: Specify which command operators to block
      ""blockedOperators"": [""&"", ""|"", "";"", ""`""]  // Block all command chaining
    },
    ""cmd"": {
      ""enabled"": true,
      ""command"": ""cmd.exe"",
      ""args"": [""/c""],
      ""blockedOperators"": [""&"", ""|"", "";"", ""`""]  // Block all command chaining
    },
    ""gitbash"": {
      ""enabled"": true,
      ""command"": ""C:\\Program Files\\Git\\bin\\bash.exe"",
      ""args"": [""-c""],
      ""blockedOperators"": [""&"", ""|"", "";"", ""`""]  // Block all command chaining
    }
  }
}
```

#### SSH Configuration

```json
{
  ""ssh"": {
    // Enable/disable SSH functionality
    ""enabled"": false,

    // Default timeout for SSH commands in seconds
    ""defaultTimeout"": 30,

    // Maximum number of concurrent SSH sessions
    ""maxConcurrentSessions"": 5,

    // Interval for sending keepalive packets (in milliseconds)
    ""keepaliveInterval"": 10000,

    // Maximum number of failed keepalive attempts before disconnecting
    ""keepaliveCountMax"": 3,

    // Timeout for establishing SSH connections (in milliseconds)
    ""readyTimeout"": 20000,

    // SSH connection profiles
    ""connections"": {
      // NOTE: these examples are not set in the default config!
      // Example: Local Raspberry Pi
      ""raspberry-pi"": {
        ""host"": ""raspberrypi.local"", // Hostname or IP address
        ""port"": 22, // SSH port
        ""username"": ""pi"", // SSH username
        ""password"": ""raspberry"", // Password authentication (if not using key)
        ""keepaliveInterval"": 10000, // Override global keepaliveInterval
        ""keepaliveCountMax"": 3, // Override global keepaliveCountMax
        ""readyTimeout"": 20000 // Override global readyTimeout
      },
      // Example: Remote server with key authentication
      ""dev-server"": {
        ""host"": ""dev.example.com"",
        ""port"": 22,
        ""username"": ""admin"",
        ""privateKeyPath"": ""C:\\Users\\<USER>\\.ssh\\id_rsa"", // Path to private key
        ""keepaliveInterval"": 10000,
        ""keepaliveCountMax"": 3,
        ""readyTimeout"": 20000
      }
    }
  }
}
```

## API

### Tools

- **execute_command**

  - Execute a command in the specified shell
  - Inputs:
    - `shell` (string): Shell to use (""powershell"", ""cmd"", or ""gitbash"")
    - `command` (string): Command to execute
    - `workingDir` (optional string): Working directory
  - Returns command output as text, or error message if execution fails

- **get_command_history**

  - Get the history of executed commands
  - Input: `limit` (optional number)
  - Returns timestamped command history with outputs

- **ssh_execute**

  - Execute a command on a remote system via SSH
  - Inputs:
    - `connectionId` (string): ID of the SSH connection to use
    - `command` (string): Command to execute
  - Returns command output as text, or error message if execution fails

- **ssh_disconnect**
  - Disconnect from an SSH server
  - Input:
    - `connectionId` (string): ID of the SSH connection to disconnect
  - Returns confirmation message

- **create_ssh_connection**
  - Create a new SSH connection
  - Inputs:
    - `connectionId` (string): ID for the new SSH connection
    - `connectionConfig` (object): Connection configuration details including host, port, username, and either password or privateKeyPath
  - Returns confirmation message

- **read_ssh_connections**
  - Read all configured SSH connections
  - Returns a list of all SSH connections from the configuration

- **update_ssh_connection**
  - Update an existing SSH connection
  - Inputs:
    - `connectionId` (string): ID of the SSH connection to update
    - `connectionConfig` (object): New connection configuration details
  - Returns confirmation message

- **delete_ssh_connection**
  - Delete an SSH connection
  - Input:
    - `connectionId` (string): ID of the SSH connection to delete
  - Returns confirmation message

- **get_current_directory**
  - Get the current working directory of the server
  - Returns the current working directory path

### Resources

- **SSH Connections**
  - URI format: `ssh://{connectionId}`
  - Contains connection details with sensitive information masked
  - One resource for each configured SSH connection
  - Example: `ssh://raspberry-pi` shows configuration for the ""raspberry-pi"" connection

- **SSH Configuration**
  - URI: `ssh://config`
  - Contains overall SSH configuration and all connections (with passwords masked)
  - Shows settings like defaultTimeout, maxConcurrentSessions, and the list of connections

- **Current Directory**
  - URI: `cli://currentdir`
  - Contains the current working directory of the CLI server
  - Shows the path where commands will execute by default

- **CLI Configuration**
  - URI: `cli://config`
  - Contains the CLI server configuration (excluding sensitive data)
  - Shows security settings, shell configurations, and SSH settings

## Security Considerations

### Built-in Security Features (Always Active)

The following security features are hard-coded into the server and cannot be disabled:

- **Case-insensitive command blocking**: All command blocking is case-insensitive (e.g., ""DEL.EXE"", ""del.cmd"", etc. are all blocked if ""del"" is in blockedCommands)
- **Smart path parsing**: The server parses full command paths to prevent bypass attempts (blocking ""C:\\Windows\\System32\\rm.exe"" if ""rm"" is blocked)
- **Command parsing intelligence**: False positives are avoided (e.g., ""warm_dir"" is not blocked just because ""rm"" is in blockedCommands)
- **Input validation**: All user inputs are validated before execution
- **Shell process management**: Processes are properly terminated after execution or timeout
- **Sensitive data masking**: Passwords are automatically masked in resources (replaced with ********)

### Configurable Security Features (Active by Default)

These security features are configurable through the config.json file:

- **Command blocking**: Commands specified in `blockedCommands` array are blocked (default includes dangerous commands like rm, del, format)
- **Argument blocking**: Arguments specified in `blockedArguments` array are blocked (default includes potentially dangerous flags)
- **Command injection protection**: Prevents command chaining (enabled by default through `enableInjectionProtection: true`)
- **Working directory restriction**: Limits command execution to specified directories (enabled by default through `restrictWorkingDirectory: true`)
- **Command length limit**: Restricts maximum command length (default: 2000 characters)
- **Command timeout**: Terminates commands that run too long (default: 30 seconds)
- **Command logging**: Records command history (enabled by default through `logCommands: true`)

### Important Security Warnings

These are not features but important security considerations to be aware of:

- **Environment access**: Commands may have access to environment variables, which could contain sensitive information
- **File system access**: Commands can read/write files within allowed paths - carefully configure `allowedPaths` to prevent access to sensitive data

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
","Star
 165",2025-05-07 15:22:02.281957
https://github.com/v-3/discordmcp,v-3/discordmcp,"# Discord MCP Server

A Model Context Protocol (MCP) server that enables LLMs to interact with Discord channels, allowing them to send and read messages through Discord's API. Using this server, LLMs like Claude can directly interact with Discord channels while maintaining user control and security.

## Features

- Send messages to Discord channels
- Read recent messages from channels
- Automatic server and channel discovery
- Support for both channel names and IDs
- Proper error handling and validation

## Prerequisites

- Node.js 16.x or higher
- A Discord bot token
- The bot must be invited to your server with proper permissions:
  - Read Messages/View Channels
  - Send Messages
  - Read Message History

## Setup

1. Clone this repository:
```bash
git clone https://github.com/yourusername/discordmcp.git
cd discordmcp
```

2. Install dependencies:
```bash
npm install
```

3. Create a `.env` file in the root directory with your Discord bot token:
```
DISCORD_TOKEN=your_discord_bot_token_here
```

4. Build the server:
```bash
npm run build
```

## Usage with Claude for Desktop

1. Open your Claude for Desktop configuration file:
   - macOS: `~/Library/Application Support/Claude/claude_desktop_config.json`
   - Windows: `%APPDATA%\Claude\claude_desktop_config.json`

2. Add the Discord MCP server configuration:
```json
{
  ""mcpServers"": {
    ""discord"": {
      ""command"": ""node"",
      ""args"": [""path/to/discordmcp/build/index.js""],
      ""env"": {
        ""DISCORD_TOKEN"": ""your_discord_bot_token_here""
      }
    }
  }
}
```

3. Restart Claude for Desktop

## Available Tools

### send-message
Sends a message to a specified Discord channel.

Parameters:
- `server` (optional): Server name or ID (required if bot is in multiple servers)
- `channel`: Channel name (e.g., ""general"") or ID
- `message`: Message content to send

Example:
```json
{
  ""channel"": ""general"",
  ""message"": ""Hello from MCP!""
}
```

### read-messages
Reads recent messages from a specified Discord channel.

Parameters:
- `server` (optional): Server name or ID (required if bot is in multiple servers)
- `channel`: Channel name (e.g., ""general"") or ID
- `limit` (optional): Number of messages to fetch (default: 50, max: 100)

Example:
```json
{
  ""channel"": ""general"",
  ""limit"": 10
}
```

## Development

1. Install development dependencies:
```bash
npm install --save-dev typescript @types/node
```

2. Start the server in development mode:
```bash
npm run dev
```

## Testing

You can test the server using the MCP Inspector:

```bash
npx @modelcontextprotocol/inspector node build/index.js
```

## Examples

Here are some example interactions you can try with Claude after setting up the Discord MCP server:

1. ""Can you read the last 5 messages from the general channel?""
2. ""Please send a message to the announcements channel saying 'Meeting starts in 10 minutes'""
3. ""What were the most recent messages in the development channel about the latest release?""

Claude will use the appropriate tools to interact with Discord while asking for your approval before sending any messages.

## Security Considerations

- The bot requires proper Discord permissions to function
- All message sending operations require explicit user approval
- Environment variables should be properly secured
- Token should never be committed to version control
- Channel access is limited to channels the bot has been given access to

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

If you encounter any issues or have questions:
1. Check the GitHub Issues section
2. Consult the MCP documentation at https://modelcontextprotocol.io
3. Open a new issue with detailed reproduction steps","Star
 72",2025-05-07 15:22:02.281957
https://github.com/kenjihikmatullah/productboard-mcp,kenjihikmatullah/productboard-mcp,"# Productboard MCP Server

Integrate the Productboard API into agentic workflows via MCP


## Tools

1. `get_companies`
2. `get_company_detail`
3. `get_components`
4. `get_component_detail`
5. `get_features`
6. `get_feature_detail`
7. `get_feature_statuses`
8. `get_notes`
9. `get_products`
10. `get_product_detail`


## Setup

### Access Token
Obtain your access token referring to [this guidance](https://developer.productboard.com/reference/authentication#public-api-access-token)

### Usage with Claude Desktop
To use this with Claude Desktop, add the following to your `claude_desktop_config.json`:

### NPX

```json
{
  ""mcpServers"": {
    ""productboard"": {
      ""command"": ""npx"",
      ""args"": [
        ""-y"",
        ""productboard-mcp""
      ],
      ""env"": {
        ""PRODUCTBOARD_ACCESS_TOKEN"": ""<YOUR_TOKEN>""
      }
    }
  }
}
```

## License

This MCP server is licensed under the MIT License. This means you are free to use, modify, and distribute the software, subject to the terms and conditions of the MIT License. For more details, please see the LICENSE file in the project repository.","Star
 6",2025-05-07 15:22:02.281957
https://github.com/aarora79/aws-cost-explorer-mcp-server,aarora79/aws-cost-explorer-mcp-server,"# AWS Cost Explorer and Amazon Bedrock Model Invocation Logs MCP Server & Client

An MCP server for getting AWS spend data via Cost Explorer and Amazon Bedrock usage data via [`Model invocation logs`](https://docs.aws.amazon.com/bedrock/latest/userguide/model-invocation-logging.html) in Amazon Cloud Watch through [Anthropic's MCP (Model Control Protocol)](https://www.anthropic.com/news/model-context-protocol). See section on [""secure"" remote MCP server](#secure-remote-mcp-server) to see how you can run your MCP server over HTTPS.

```mermaid
flowchart LR
    User([User]) --> UserApp[User Application]
    UserApp --> |Queries| Host[Host]
    
    subgraph ""Claude Desktop""
        Host --> MCPClient[MCP Client]
    end
    
    MCPClient --> |MCP Protocol over HTTPS| MCPServer[AWS Cost Explorer MCP Server]
    
    subgraph ""AWS Services""
        MCPServer --> |API Calls| CostExplorer[(AWS Cost Explorer)]
        MCPServer --> |API Calls| CloudWatchLogs[(AWS CloudWatch Logs)]
    end
```

You can run the MCP server locally and access it via the Claude Desktop or you could also run a Remote MCP server on Amazon EC2 and access it via a MCP client built into a LangGraph Agent.

🚨You can also use this MCP server to get AWS spend information from other accounts as long as the IAM role used by the MCP server can assume roles in those other accounts🚨

### Demo video

[![AWS Cost Explorer MCP Server Deep Dive](https://img.youtube.com/vi/WuVOmYLRFmI/maxresdefault.jpg)](https://youtu.be/WuVOmYLRFmI)

## Overview

This tool provides a convenient way to analyze and visualize AWS cloud spending data using Anthropic's Claude model as an interactive interface. It functions as an MCP server that exposes AWS Cost Explorer API functionality to Claude Desktop, allowing you to ask questions about your AWS spend in natural language.

## Features

- **Amazon EC2 Spend Analysis**: View detailed breakdowns of EC2 spending for the last day
- **Amazon Bedrock Spend Analysis**: View breakdown by region, users and models over the last 30 days
- **Service Spend Reports**: Analyze spending across all AWS services for the last 30 days
- **Detailed Cost Breakdown**: Get granular cost data by day, region, service, and instance type
- **Interactive Interface**: Use Claude to query your cost data through natural language

## Requirements

- Python 3.12
- AWS credentials with Cost Explorer access
- Anthropic API access (for Claude integration)
- [Optional] Amazon Bedrock access (for LangGraph Agent)
- [Optional] Amazon EC2 for running a remote MCP server

## Installation

1. Install `uv`:
   ```bash
   # On macOS and Linux
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```
   
   
   ```powershell
   # On Windows
   powershell -ExecutionPolicy ByPass -c ""irm https://astral.sh/uv/install.ps1 | iex""
   ```
   Additional installation options are documented [here](https://docs.astral.sh/uv/getting-started/installation/)

2. Clone this repository: (assuming this will be updated to point to aws-samples?)
   ```
   git clone https://github.com/aarora79/aws-cost-explorer-mcp.git
   cd aws-cost-explorer-mcp
   ```

3. Set up the Python virtual environment and install dependencies:
   ```
   uv venv --python 3.12 && source .venv/bin/activate && uv pip install --requirement pyproject.toml
   ```
   
4. Configure your AWS credentials:
   ```
   mkdir -p ~/.aws
   # Set up your credentials in ~/.aws/credentials and ~/.aws/config
   ```
   If you useAWS IAM Identity Center, follow the [docs](https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-sso.html) to configure your short-term credentials

## Usage

### Prerequisites

1. Setup [model invocation logs](https://docs.aws.amazon.com/bedrock/latest/userguide/model-invocation-logging.html#setup-cloudwatch-logs-destination) in Amazon CloudWatch.
1. Ensure that the IAM user/role being used has full read-only access to Amazon Cost Explorer and Amazon CloudWatch, this is required for the MCP server to retrieve data from these services.
See [here](https://docs.aws.amazon.com/awsaccountbilling/latest/aboutv2/billing-example-policies.html) and [here](https://docs.aws.amazon.com/aws-managed-policy/latest/reference/CloudWatchLogsReadOnlyAccess.html) for sample policy examples that you can use & modify as per your requirements.
1. To allow your MCP server to access AWS spend information from other accounts set the the `CROSS_ACCOUNT_ROLE_NAME` parameter while starting the server and now you can provide the account AWS account id for another account while interacting with your agent and then agent will pass the account id to the server.

### Local setup

Uses `stdio` as a transport for MCP, both the MCP server and client are running on your local machine.

#### Starting the Server (local)

Run the server using:

```
export MCP_TRANSPORT=stdio
export BEDROCK_LOG_GROUP_NAME=YOUR_BEDROCK_CW_LOG_GROUP_NAME
export CROSS_ACCOUNT_ROLE_NAME=ROLE_NAME_FOR_THE_ROLE_TO_ASSUME_IN_OTHER_ACCOUNTS # can be ignored if you do not want AWS spend info from other accounts
python server.py
```

#### Claude Desktop Configuration

There are two ways to configure this tool with Claude Desktop:

##### Option 1: Using Docker

Add the following to your Claude Desktop configuration file. The file can be found out these paths depending upon you operating system.

- macOS: ~/Library/Application Support/Claude/claude_desktop_config.json.
- Windows: %APPDATA%\Claude\claude_desktop_config.json.
- Linux: ~/.config/Claude/claude_desktop_config.json.

```json
{
  ""mcpServers"": {
    ""aws-cost-explorer"": {
      ""command"": ""docker"",
      ""args"": [ ""run"", ""-i"", ""--rm"", ""-e"", ""AWS_ACCESS_KEY_ID"", ""-e"", ""AWS_SECRET_ACCESS_KEY"", ""-e"", ""AWS_REGION"", ""-e"", ""BEDROCK_LOG_GROUP_NAME"", ""-e"", ""MCP_TRANSPORT"", ""-e"", ""CROSS_ACCOUNT_ROLE_NAME"", ""aws-cost-explorer-mcp:latest"" ],
      ""env"": {
        ""AWS_ACCESS_KEY_ID"": ""YOUR_ACCESS_KEY_ID"",
        ""AWS_SECRET_ACCESS_KEY"": ""YOUR_SECRET_ACCESS_KEY"",
        ""AWS_REGION"": ""us-east-1"",
        ""BEDROCK_LOG_GROUP_NAME"": ""YOUR_CLOUDWATCH_BEDROCK_MODEL_INVOCATION_LOG_GROUP_NAME"",
        ""CROSS_ACCOUNT_ROLE_NAME"": ""ROLE_NAME_FOR_THE_ROLE_TO_ASSUME_IN_OTHER_ACCOUNTS"",
        ""MCP_TRANSPORT"": ""stdio""
      }
    }
  }
}
```

> **IMPORTANT**: Replace `YOUR_ACCESS_KEY_ID` and `YOUR_SECRET_ACCESS_KEY` with your actual AWS credentials. Never commit actual credentials to version control.

##### Option 2: Using UV (without Docker)

If you prefer to run the server directly without Docker, you can use UV:

```json
{
  ""mcpServers"": {
    ""aws_cost_explorer"": {
      ""command"": ""uv"",
      ""args"": [
          ""--directory"",
          ""/path/to/aws-cost-explorer-mcp-server"",
          ""run"",
          ""server.py""
      ],
      ""env"": {
        ""AWS_ACCESS_KEY_ID"": ""YOUR_ACCESS_KEY_ID"",
        ""AWS_SECRET_ACCESS_KEY"": ""YOUR_SECRET_ACCESS_KEY"",
        ""AWS_REGION"": ""us-east-1"",
        ""BEDROCK_LOG_GROUP_NAME"": ""YOUR_CLOUDWATCH_BEDROCK_MODEL_INVOCATION_LOG_GROUP_NAME"",
        ""CROSS_ACCOUNT_ROLE_NAME"": ""ROLE_NAME_FOR_THE_ROLE_TO_ASSUME_IN_OTHER_ACCOUNTS"",
        ""MCP_TRANSPORT"": ""stdio""
      }
    }
  }
}
```

Make sure to replace the directory path with the actual path to your repository on your system.

### Remote setup

Uses `sse` as a transport for MCP, the MCP servers on EC2 and the client is running on your local machine. Note that Claude Desktop does not support remote MCP servers at this time (see [this](https://github.com/orgs/modelcontextprotocol/discussions/16) GitHub issue).

#### Starting the Server (remote)

You can start a remote MCP server on Amazon EC2 by following the same instructions as above. Make sure to set the `MCP_TRANSPORT` as `sse` (server side events) as shown below. **Note that the MCP uses JSON-RPC 2.0 as its wire format, therefore the protocol itself does not include authorization and authentication (see [this GitHub issue](https://github.com/modelcontextprotocol/specification/discussions/102)), do not send or receive sensitive data over MCP**.

Run the server using:

```
export MCP_TRANSPORT=sse
export BEDROCK_LOG_GROUP_NAME=YOUR_BEDROCK_CW_LOG_GROUP_NAME
export CROSS_ACCOUNT_ROLE_NAME=ROLE_NAME_FOR_THE_ROLE_TO_ASSUME_IN_OTHER_ACCOUNTS # can be ignored if you do not want AWS spend info from other accounts
python server.py
```

1. The MCP server will start listening on TCP port 8000.
1. Configure an ingress rule in the security group associated with your EC2 instance to allow access to TCP port 8000 from your local machine (where you are running the MCP client/LangGraph based app) to your EC2 instance.

>Also see section on running a [""secure"" remote MCP server](#secure-remote-mcp-server) i.e. a server to which your MCP clients can connect over HTTPS.

#### Testing with a CLI MCP client

You can test your remote MCP server with the `mcp_sse_client.py` script. Running this script will print the list of tools available from the MCP server and an output for the `get_bedrock_daily_usage_stats` tool.

```{.bashrc}
# set the hostname for your MCP server
MCP_SERVER_HOSTNAME=YOUR_MCP_SERVER_EC2_HOSTNAME
# or localhost if your MCP server is running locally
# MCP_SERVER_HOSTNAME=localhost 
AWS_ACCOUNT_ID=AWS_ACCOUNT_ID_TO_GET_INFO_ABOUT # if set to empty or if the --aws-account-id switch is not specified then it gets the info about the AWS account MCP server is running in
python mcp_sse_client.py --host $MCP_SERVER_HOSTNAME --aws-account-id $AWS_ACCOUNT_ID
```


#### Testing with Chainlit app

The `app.py` file in this repo provides a Chainlit app (chatbot) which creates a LangGraph agent that uses the [`LangChain MCP Adapter`](https://github.com/langchain-ai/langchain-mcp-adapters) to import the tools provided by the MCP server as tools in a LangGraph Agent. The Agent is then able to use an LLM to respond to user questions and use the tools available to it as needed. Thus if the user asks a question such as ""_What was my Bedrock usage like in the last one week?_"" then the Agent will use the tools available to it via the remote MCP server to answer that question. We use Claude 3.5 Haiku model available via Amazon Bedrock to power this agent.

Run the Chainlit app using:

```{.bashrc}
chainlit run app.py --port 8080 
```

A browser window should open up on `localhost:8080` and you should be able to use the chatbot to get details about your AWS spend.

### Available Tools

The server exposes the following tools that Claude can use:

1. **`get_ec2_spend_last_day()`**: Retrieves EC2 spending data for the previous day
1. **`get_detailed_breakdown_by_day(days=7)`**: Delivers a comprehensive analysis of costs by region, service, and instance type
1. **`get_bedrock_daily_usage_stats(days=7, region='us-east-1', log_group_name='BedrockModelInvocationLogGroup')`**: Delivers a per-day breakdown of model usage by region and users.
1. **`get_bedrock_hourly_usage_stats(days=7, region='us-east-1', log_group_name='BedrockModelInvocationLogGroup')`**: Delivers a per-day per-hour breakdown of model usage by region and users.

### Example Queries

Once connected to Claude through an MCP-enabled interface, you can ask questions like:

- ""Help me understand my Bedrock spend over the last few weeks""
- ""What was my EC2 spend yesterday?""
- ""Show me my top 5 AWS services by cost for the last month""
- ""Analyze my spending by region for the past 14 days""
- ""Which instance types are costing me the most money?""
- ""Which services had the highest month-over-month cost increase?""

## Docker Support

A Dockerfile is included for containerized deployment:

```
docker build -t aws-cost-explorer-mcp .
docker run -v ~/.aws:/root/.aws aws-cost-explorer-mcp
```

## Development

### Project Structure

- `server.py`: Main server implementation with MCP tools
- `pyproject.toml`: Project dependencies and metadata
- `Dockerfile`: Container definition for deployments

### Adding New Cost Analysis Tools

To extend the functionality:

1. Add new functions to `server.py`
2. Annotate them with `@mcp.tool()`
3. Implement the AWS Cost Explorer API calls
4. Format the results for easy readability

## Secure ""remote"" MCP server

We can use [`nginx`](https://nginx.org/) as a reverse-proxy so that it can provide an HTTPS endpoint for connecting to the MCP server. Remote MCP clients can connect to `nginx` over HTTPS and then it can proxy traffic internally to `http://localhost:8000`. The following steps describe how to do this.

1. Enable access to TCP port 443 from the IP address of your MCP client (your laptop, or anywhere) in the inbound rules in the security group associated with your EC2 instance.

1. You would need to have an HTTPS certificate and private key to proceed. Let's say you use `your-mcp-server-domain-name.com` as the domain for your MCP server then you will need an SSL cert for `your-mcp-server-domain-name.com` and it will be accessible to MCP clients as `https://your-mcp-server-domain-name.com/sse`. _While you can use a self-signed cert but it would require disabling SSL verification on the MCP client, we DO NOT recommend you do that_. If you are hosting your MCP server on EC2 then you could generate an SSL cert using [no-ip](https://www.noip.com/) or [Let' Encrypt](https://letsencrypt.org/) or other similar services. Place the SSL cert and private key files in `/etc/ssl/certs` and `/etc/ssl/privatekey` folders respectively on your EC2 machine.

1. Install `nginx` on your EC2 machine using the following commands.

    ```{.bashrc}
    sudo apt-get install nginx
    sudo nginx -t
    sudo systemctl reload nginx
    ```

1. Get the hostname for your EC2 instance, this would be needed for configuring the `nginx` reverse proxy.

    ```{.bashrc}
    TOKEN=$(curl -X PUT ""http://***************/latest/api/token"" -H ""X-aws-ec2-metadata-token-ttl-seconds: 21600"") && curl -H ""X-aws-ec2-metadata-token: $TOKEN"" -s http://***************/latest/meta-data/public-hostname
    ```

1. Copy the following content into a new file `/etc/nginx/conf.d/ec2.conf`. Replace `YOUR_EC2_HOSTNAME`, `/etc/ssl/certs/cert.pem` and `/etc/ssl/privatekey/privkey.pem` with values appropriate for your setup.

   ```{.bashrc}
   server {
    listen 80;
    server_name YOUR_EC2_HOSTNAME;

    # Optional: Redirect HTTP to HTTPS
    return 301 https://$host$request_uri;
    }

    server {
        listen 443 ssl;
        server_name YOUR_EC2_HOSTNAME;

        # Self-signed certificate paths
        ssl_certificate     /etc/ssl/certs/cert.pem;
        ssl_certificate_key /etc/ssl/privatekey/privkey.pem; 

        # Optional: Good practice
        ssl_protocols       TLSv1.2 TLSv1.3;
        ssl_ciphers         HIGH:!aNULL:!MD5;

        location / {
            # Reverse proxy to your local app (e.g., port 8000)
            proxy_pass http://127.0.0.1:8000;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
    }

   ```

1. Restart `nginx`.

    ```{.bashrc}
    sudo systemctl start nginx
    ```

1. Start your MCP server as usual as described in the [remote setup](#remote-setup) section.

1. Your MCP server is now accessible over HTTPS as `https://your-mcp-server-domain-name.com/sse` to your MCP client.

1. On the client side now (say on your laptop or in your Agent) configure your MCP client to communicate to your MCP server as follows.

    ```{.bashrc}
    MCP_SERVER_HOSTNAME=YOUR_MCP_SERVER_DOMAIN_NAME
    AWS_ACCOUNT_ID=AWS_ACCOUNT_ID_TO_GET_INFO_ABOUT # if set to empty or if the --aws-account-id switch is not specified then it gets the info about the AWS account MCP server is running in
    python mcp_sse_client.py --host $MCP_SERVER_HOSTNAME --port 443 --aws-account-id $AWS_ACCOUNT_ID
    ```

    Similarly you could run the chainlit app to talk to remote MCP server over HTTPS.

    ```{.bashrc}
    export MCP_SERVER_URL=YOUR_MCP_SERVER_DOMAIN_NAME
    export MCP_SERVER_PORT=443
    chainlit run app.py --port 8080
    ```

    Similarly you could run the LangGraph Agent to talk to remote MCP server over HTTPS.

    ```{.bashrc}    
    python langgraph_agent_mcp_sse_client.py --host $MCP_SERVER_HOSTNAME --port 443 --aws-account-id $AWS_ACCOUNT_ID
    ```

## License

[MIT License](LICENSE)

## Acknowledgments

- This tool uses Anthropic's MCP framework
- Powered by AWS Cost Explorer API
- Built with [FastMCP](https://github.com/jlowin/fastmcp) for server implementation
- README was generated by providing a text dump of the repo via [GitIngest](https://gitingest.com/) to Claude
","Star
 96",2025-05-07 15:22:02.281957
https://github.com/diegobit/aranet4-mcp-server,diegobit/aranet4-mcp-server,"# aranet4-mcp-server

MCP server to manage your Aranet4 CO2 sensor. Built upon [Aranet4-Python](https://github.com/Anrijs/Aranet4-Python).

> [!TIP]
> For the standalone python version without MCP logic, see [aranet4-archiver](https://github.com/diegobit/aranet4-archiver?tab=readme-ov-file).

![Example screenshot of the Aranet4 MCP Server running](img/claude-example-1.jpg)

## Features:
- Scan for nearby devices.
- Fetch new data from embedded device memory and save to a local sqlite db for tracking and later viewing. For automatic updates, see at the bottom.
- Ask questions about recent measurements or about a specific past date.
- *[For MCP clients that support images]* Ask data to be plotted to also have a nice visualization!
- **Assisted configuration!** 💫 After installation, just ask `init aranet4` in your client to set up everything for the mcp server to work with your device.

## Installation

1. Clone repo:

    ```
    <NAME_EMAIL>:diegobit/aranet4-mcp-server.git`
    cd aranet4-mcp-server
    ```

2. Prepare environment:

    - **Recommended (with [uv](https://docs.astral.sh/uv/))**: Nothing to do. The provided `pyproject.toml` handles dependencied and virtual environments.
    - **Alternative (with pip)**: install with `pip install .`

3. Add to MCP client configuration:

    ```json
    ""aranet4"": {
      ""command"": ""{{PATH_TO_UV}}"", // run `which uv`
        ""args"": [
          ""--directory"",
          ""{{PATH_TO_SRC}}/aranet4-mcp-server/"",
          ""run"",
          ""src/server.py""
        ]
    }
    ```

    - Claude Desktop MacOS config file path: `~/Library/Application Support/Claude/claude_desktop_config.json`
    - Cursor MacOS config file path: `~/.cursor/mcp.json`

4. Configure:

    - **Recommended (AI assisted config!):** start your client and ask `init aranet4` to get a guided configuration.
    - **Alternative (manual):** edit file `config.yaml`. You need to provide the mac address and the device name. You can get the mac address with `aranetctl --scan` from [Aranet4-Python](https://github.com/Anrijs/Aranet4-Python) (installed with this repo dependencies).

## Dockerfile

Dockerfile is available. Remember to pass env variables or update `config.yaml`.

## List of tools

**Configuration and utils**:
- `init_aranet4_config`: assisted configuration of device.
- `scan_devices`: scan nearby bluetooth aranet4 devices.
- `get_configuration_and_db_stats`: get current config.yaml and general stats from the local sqlite3 db.
- `set_configuration`: set values in config.yaml.

**To update historical data**:
- `fetch_new_data`: fetch new data from configured nearby aranet4 device and save to local db.

**To query historical data**:
- `get_recent_data`: get recent data from local db. Can specify how many measurements. 
- `get_data_by_timerange`: get data in specific timerange from local db. Can specify how many measurements (careful, if the range is big and the limit is low, datapoints will be skipped).

  For both, ask to receive a plot to have it generated and displayed.

## Automatic data fetch job

If you want your local db to always be updated, you can setup a cronjob or a launch agent that fetches data automatically every few hours. In MacOS, do as follows:

1. Configure absolute paths in `com.diegobit.aranet4-fetch.plist`.
2. Install LaunchAgent:
   ```bash
   cp com.diegobit.aranet4-fetch.plist ~/Library/LaunchAgents/
   launchctl load ~/Library/LaunchAgents/com.diegobit.aranet4-fetch.plist
   ```

For other platforms, just run `fetch-job.py` periodically however you prefer.

","Star
 3",2025-05-07 15:22:02.281957
https://github.com/geropl/git-mcp-go,geropl/git-mcp-go,"# Git MCP Server (Go)

A Model Context Protocol (MCP) server for Git repository interaction and automation, written in Go. This server provides tools to read, search, and manipulate Git repositories via Large Language Models.

## Features

This MCP server provides the following Git operations as tools:

- **git_status**: Shows the working tree status
- **git_diff_unstaged**: Shows changes in the working directory that are not yet staged
- **git_diff_staged**: Shows changes that are staged for commit
- **git_diff**: Shows differences between branches or commits
- **git_commit**: Records changes to the repository
- **git_add**: Adds file contents to the staging area
- **git_reset**: Unstages all staged changes
- **git_log**: Shows the commit logs
- **git_create_branch**: Creates a new branch from an optional base branch
- **git_checkout**: Switches branches
- **git_show**: Shows the contents of a commit
- **git_init**: Initialize a new Git repository
- **git_push**: Pushes local commits to a remote repository (requires `--write-access` flag)
- **git_list_repositories**: Lists all available Git repositories

## Installation

### Prerequisites

- Go 1.18 or higher
- Git installed on your system

### Download Prebuilt Binaries

You can download prebuilt binaries for your platform from the [GitHub Releases](https://github.com/geropl/git-mcp-go/releases) page.

### Building from Source

```bash
# Clone the repository
git clone https://github.com/geropl/git-mcp-go.git
cd git-mcp-go

# Build the server
go build -o git-mcp-go .
```

### Install with `go install`

```bash
go install github.com/geropl/git-mcp-go@latest
```

## Usage

### Command Line Structure

The Git MCP Server uses a command-line structure with subcommands:

```
git-mcp-go
├── serve [flags] [repository-paths...]
│   ├── --repository, -r <paths>                  # Repository paths (multiple ways to specify)
│   ├── --mode <shell|go-git>
│   ├── --write-access
│   └── --verbose, -v
└── setup [flags] [repository-paths...]
    ├── --repository, -r <paths>                  # Repository paths (multiple ways to specify)
    ├── --mode <shell|go-git>
    ├── --write-access
    ├── --auto-approve <tool-list|allow-read-only|allow-local-only>
    └── --tool <cline,roo-code>
```

### Multi-Repository Support

The Git MCP Server can now monitor and operate on multiple repositories simultaneously. You can specify repositories in several ways:

1. Using the `-r/--repository` flag:
   - With comma-separated paths: `-r=/path/to/repo1,/path/to/repo2`
   - With multiple flag instances: `-r=/path/to/repo1 -r=/path/to/repo2`
2. As positional arguments: `serve /path/to/repo1 /path/to/repo2`
3. A combination of both approaches

When using multiple repositories, the server will default to the first repository for operations where a specific repository is not specified.

### `serve` Command

The `serve` command starts the Git MCP server:

```bash
# Run with verbose logging
./git-mcp-go serve -v /path/to/repo1 /path/to/repo2 /path/to/repo3

# Run with go-git implementation
./git-mcp-go serve --mode go-git -r=/path/to/repo1,/path/to/repo2

# Enable write access for remote operations
./git-mcp-go serve -r=/path/to/repo1,/path/to/repo2 --write-access
```

The `--mode` flag allows you to choose between two different implementations:

- **shell**: Uses the Git CLI commands via shell execution (default)
- **go-git**: Uses the go-git library for Git operations where possible

The `--write-access` flag enables operations that modify remote state (currently only the push operation). By default, this is disabled for safety.

### `setup` Command

The `setup` command sets up the Git MCP server for use with an AI assistant. It copies itself to `~/mcp-servers/git-mcp-go` and modifies the tools config (cline: `cline_mcp_settings.json`) to use that binary.

```bash
# Set up for Cline with a single repository
./git-mcp-go setup -r /path/to/git/repository

# Set up with repositories as arguments
./git-mcp-go setup /path/to/repo1 /path/to/repo2 /path/to/repo3

# Set up with write access enabled
./git-mcp-go setup -r=/path/to/repo1,/path/to/repo2 --write-access

# Set up with auto-approval for read-only tools
./git-mcp-go setup -r=/path/to/repo1,/path/to/repo2 --auto-approve=allow-read-only

# Set up with specific tools auto-approved
./git-mcp-go setup -r=/path/to/repo1,/path/to/repo2 --auto-approve=git_status,git_log

# Set up with write access and auto-approval for read-only tools
./git-mcp-go setup -r=/path/to/repo1,/path/to/repo2 --write-access --auto-approve=allow-read-only
```

The `--auto-approve` flag allows you to specify which tools should be auto-approved (not require explicit user approval):

- **allow-read-only**: Auto-approve all read-only tools (git_status, git_diff_unstaged, git_diff_staged, git_log, git_show, git_diff)
- **allow-local-only**: Auto-approve all local-only tools (incl. git_commit, git_add, git_reset, but not git_push)
- **comma-separated list**: Auto-approve specific tools (e.g., git_status,git_log)

## Repository Management

### The `git_list_repositories` Tool

This tool lists all available Git repositories that the server is monitoring. It shows:

- The total number of repositories
- The path to each repository
- The repository name (derived from the directory name)

Example output:

```
Available repositories (3):

1. repo1 (/path/to/repo1)
2. repo2 (/path/to/repo2)
3. another-project (/path/to/another-project)
```

### Repository Selection

When running commands that require a repository path:

1. If a specific `repo_path` is provided in the command, it will be used.
2. If no `repo_path` is provided and multiple repositories are configured, the first repository will be used as the default.
3. Each command output will indicate which repository was used for the operation.

## Installation

### Automatic Installation and Configuration

The easiest way to install and register the Git MCP server with Cline is to use the setup command:

```bash
# Download linux binary for the latest release
RELEASE=""$(curl -s https://api.github.com/repos/geropl/git-mcp-go/releases/latest)""
DOWNLOAD_URL=""$(echo $RELEASE | jq -r '.assets[] | select(.name | contains(""linux-amd64"")) | .browser_download_url')""
curl -L -o ./git-mcp-go $DOWNLOAD_URL
chmod +x ./git-mcp-go

# Setup the mcp server with a single repository
./git-mcp-go setup -r /path/to/git/repository --tool=cline --auto-approve=allow-local-only

# Setup the mcp server with multiple repositories
./git-mcp-go setup -r=/path/to/repo1,/path/to/repo2 --tool=cline --auto-approve=allow-local-only

rm -f ./git-mcp-go
```

The setup command will:
1. Copy the executable to the Cline MCP directory
2. Create a registration script that configures Cline to use the Git MCP server

### Manual Configuration

Alternatively, you can manually add this to your `claude_desktop_config.json`:

```json
""mcpServers"": {
  ""git"": {
    ""command"": ""/path/to/git-mcp-go"",
    ""args"": [""serve"", ""-r=/path/to/repo1,/path/to/repo2"", ""--mode"", ""shell""]
  }
}
```

```json
""mcpServers"": {
  ""git"": {
    ""command"": ""/path/to/git-mcp-go"",
    ""args"": [""serve"", ""-r"", ""/path/to/git/repository""]
  }
}
```

## Implementation Details

This server is implemented using:

- [mcp-go](https://github.com/mark3labs/mcp-go): Go SDK for the Model Context Protocol
- [go-git](https://github.com/go-git/go-git): Pure Go implementation of Git (used for the `go-git` mode)

For operations not supported by go-git, the server falls back to using the Git CLI.

## Development

### Testing

The server includes comprehensive tests for all Git operations. The tests are designed to run against both implementation modes:

```bash
# Run all tests
go test ./pkg -v

# Run specific tests
go test ./pkg -v -run TestGitOperations/push
```

The test suite creates temporary repositories for each test case and verifies that the operations work correctly in both modes.

### Continuous Integration

This project uses GitHub Actions for continuous integration and deployment:

- Automated tests run on every pull request to the main branch
- Releases are created when a tag with the format `v*` is pushed
- Each release includes binaries for multiple platforms:
  - Linux (amd64, arm64)
  - macOS (amd64, arm64)
  - Windows (amd64)

To create a new release:
```bash
# Tag the current commit
git tag v1.0.0

# Push the tag to GitHub
git push origin v1.0.0
```

## License

This project is licensed under the MIT License.
","Star
 5",2025-05-07 15:22:02.281957
