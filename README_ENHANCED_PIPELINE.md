# Enhanced MCP Automated Pipeline

A comprehensive automated web scraping and content generation pipeline with Redis and CSV dual storage, state management, and resumability features.

## 🚀 Features

### **Data Storage Requirements**
- ✅ **Dual Storage**: Store all target URLs in both Redis (for fast access) and CSV (for persistence/backup)
- ✅ **Content Storage**: Store all scraped content in both Redis (for caching) and CSV (for data persistence)
- ✅ **State Management**: Maintain state information to track scraping progress and completion status

### **Scraping Functionality**
- ✅ **Fresh Start**: Ability to start scraping from the beginning (full run)
- ✅ **Resume Capability**: Ability to resume scraping from the last processed URL when interrupted
- ✅ **Progress Tracking**: Track which URLs have been successfully scraped vs. failed/pending
- ✅ **Error Handling**: Handle errors gracefully and continue with remaining URLs

### **Content Generation Features**
- ✅ **Full Regeneration**: Generate new content from all scraped data (full regeneration run)
- ✅ **Resume Generation**: Resume content generation from where it was previously stopped
- ✅ **Repository Processing**: Process the entire repository of scraped content when requested
- ✅ **Generation State**: Maintain generation progress state for resumability

### **Admin Panel Integration**
- ✅ **Daily Operations**: Simple interface to trigger daily scraping operations
- ✅ **Mode Selection**: Allow selection between "start fresh" vs "resume from last position" modes
- ✅ **Progress Display**: Display progress status and completion metrics
- ✅ **Dual Workflows**: Enable both scraping and content generation workflows

## 🏗️ Architecture

```
Enhanced MCP Pipeline
├── pipeline/
│   ├── core/
│   │   ├── pipeline_manager.py      # Core pipeline orchestration
│   │   ├── state_manager.py         # Redis + CSV state management
│   │   ├── url_manager.py           # Dual storage URL management
│   │   └── error_handler.py         # Comprehensive error handling
│   ├── scrapers/
│   │   └── resumable_scraper.py     # Enhanced scraping engine
│   ├── generators/
│   │   └── resumable_generator.py   # Resumable content generation
│   ├── orchestrator/
│   │   └── pipeline_orchestrator.py # Workflow orchestration
│   └── admin/
│       └── enhanced_admin_panel.py  # Web-based admin interface
├── templates/
│   └── enhanced_dashboard.html      # Enhanced dashboard UI
├── pipeline_runner.py               # Main CLI runner
└── config/
    └── pipeline_config.json         # Configuration file
```

## 🛠️ Installation

### Prerequisites
- Python 3.8+
- Redis Server (optional, falls back to CSV-only mode)
- Required Python packages (see requirements.txt)

### Setup
1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Start Redis** (optional but recommended):
   ```bash
   redis-server
   ```

3. **Configure Pipeline**:
   ```bash
   cp config/pipeline_config.example.json config/pipeline_config.json
   # Edit configuration as needed
   ```

## 🎮 Usage

### Command Line Interface

#### 1. **Run Full Pipeline** (Scraping + Content Generation)
```bash
python pipeline_runner.py --mode full --batch-size 10 --delay 2.0
```

#### 2. **Run Scraping Only**
```bash
python pipeline_runner.py --mode scrape --urls https://github.com/user/repo1 https://github.com/user/repo2
```

#### 3. **Run Content Generation Only**
```bash
python pipeline_runner.py --mode generate --batch-size 5
```

#### 4. **Resume from Last Checkpoint**
```bash
python pipeline_runner.py --mode full --resume
```

#### 5. **Setup Daily Automation**
```bash
python pipeline_runner.py --mode daily
```

#### 6. **Check Pipeline Status**
```bash
python pipeline_runner.py --mode status
```

### Web Admin Panel

#### 1. **Start Enhanced Admin Panel**:
```bash
python pipeline/admin/enhanced_admin_panel.py
```

#### 2. **Access Dashboard**:
- **Enhanced Dashboard**: http://localhost:9000
- **Pipeline Management**: http://localhost:9000/pipelines
- **API Endpoints**: http://localhost:9000/api/enhanced/status

### Admin Panel Features

#### **Dashboard Overview**
- System status and metrics
- Real-time pipeline monitoring
- Progress visualization with charts
- Error statistics and trends

#### **Pipeline Management**
- Create new scraping/content generation pipelines
- Start, pause, resume, and stop operations
- Monitor progress with real-time updates
- View detailed pipeline statistics

#### **Automation Controls**
- Setup daily automated runs
- Configure scheduling and parameters
- Manage workflow definitions
- Cleanup old pipeline data

## 📊 Monitoring & Logging

### **Real-time Monitoring**
- Pipeline progress tracking
- Error rate monitoring
- Performance metrics
- Resource usage statistics

### **Comprehensive Logging**
- Detailed operation logs in `logs/`
- Error tracking and classification
- Performance metrics
- State change history

### **Error Handling**
- Automatic retry with exponential backoff
- Error classification and severity assessment
- Recovery action determination
- Comprehensive error reporting

## 🔧 Configuration

### **Pipeline Configuration** (`config/pipeline_config.json`)
```json
{
  "redis_url": "redis://localhost:6379",
  "redis_db": 0,
  "csv_backup_enabled": true,
  "max_retries": 3,
  "retry_delay": 5,
  "batch_size": 10,
  "concurrent_workers": 3,
  "checkpoint_interval": 100,
  "data_dir": "data",
  "logs_dir": "logs",
  "enable_monitoring": true,
  "auto_resume": true
}
```

### **Workflow Configuration**
```json
{
  "daily_automation": {
    "schedule": {
      "daily": true,
      "time": "02:00"
    },
    "auto_discover": true,
    "auto_cleanup": true,
    "cleanup_days": 30,
    "generate_reports": true
  }
}
```

## 📈 Performance Features

### **Resumability**
- **Checkpoint System**: Automatic checkpoints every N operations
- **State Persistence**: Redis + CSV dual storage for reliability
- **Progress Tracking**: Detailed progress monitoring and recovery
- **Graceful Interruption**: Handle interruptions without data loss

### **Scalability**
- **Concurrent Processing**: Configurable worker pools
- **Batch Processing**: Efficient batch operations
- **Rate Limiting**: Configurable delays and throttling
- **Resource Management**: Memory and CPU optimization

### **Reliability**
- **Dual Storage**: Redis for speed, CSV for persistence
- **Error Recovery**: Comprehensive error handling and retry logic
- **Data Validation**: Input validation and sanitization
- **Monitoring**: Real-time monitoring and alerting

## 🔄 Workflow Types

### **1. Scrape Only**
- Process URLs for web scraping
- Store results in dual storage
- Track progress and handle errors
- Generate scraping reports

### **2. Generate Only**
- Process scraped data for content generation
- Use existing CrewAI infrastructure
- Resume from interruptions
- Output structured content

### **3. Scrape Then Generate**
- Complete pipeline workflow
- Automatic transition between phases
- Coordinated state management
- End-to-end processing

### **4. Daily Full Pipeline**
- Automated daily execution
- URL discovery and cleanup
- Report generation
- Maintenance operations

### **5. Continuous Scraping**
- Long-running scraping operations
- Periodic execution cycles
- Resource management
- Progress monitoring

## 🛡️ Error Handling

### **Error Classification**
- **Network Errors**: Connection, timeout, DNS issues
- **Rate Limiting**: 429 responses, API limits
- **Authentication**: 401/403 errors
- **Parsing Errors**: JSON, XML, HTML parsing issues
- **Resource Errors**: Memory, disk, CPU constraints
- **System Errors**: File permissions, missing files

### **Recovery Actions**
- **Retry**: Automatic retry with backoff
- **Skip**: Skip problematic items
- **Pause**: Temporary pause for rate limits
- **Stop**: Stop on critical errors
- **Escalate**: Human intervention required
- **Fallback**: Use alternative methods

## 📋 API Reference

### **Enhanced Admin Panel API**

#### **Get System Status**
```http
GET /api/enhanced/status
```

#### **Create Pipeline**
```http
POST /api/enhanced/pipeline/create
Content-Type: application/json

{
  "type": "scrape",
  "urls": ["https://github.com/user/repo"],
  "config": {
    "batch_size": 10,
    "delay": 2.0
  }
}
```

#### **Control Pipeline**
```http
POST /api/enhanced/pipeline/{pipeline_id}/{action}
Content-Type: application/json

{
  "type": "scrape"
}
```

#### **Get Pipeline Status**
```http
GET /api/enhanced/pipeline/{pipeline_id}/status?type=scrape
```

## 🚀 Quick Start

1. **Basic Setup**:
   ```bash
   # Install dependencies
   pip install -r requirements.txt
   
   # Start Redis (optional)
   redis-server
   
   # Run full pipeline
   python pipeline_runner.py --mode full
   ```

2. **With Admin Panel**:
   ```bash
   # Start admin panel
   python pipeline/admin/enhanced_admin_panel.py
   
   # Open browser to http://localhost:9000
   # Create and manage pipelines through the web interface
   ```

3. **Daily Automation**:
   ```bash
   # Setup daily automation
   python pipeline_runner.py --mode daily
   
   # Pipeline will run automatically at 2 AM daily
   ```

## 📞 Support

For issues, questions, or contributions:
- Check the logs in `logs/` directory
- Review error statistics in the admin panel
- Monitor pipeline status through the dashboard
- Use the comprehensive error handling system

## 🎯 Key Benefits

✅ **Robust**: Handles interruptions and resumes seamlessly  
✅ **Scalable**: Configurable concurrency and batch processing  
✅ **Reliable**: Dual storage ensures data persistence  
✅ **Monitorable**: Real-time progress and error tracking  
✅ **Automated**: Daily scheduling and maintenance  
✅ **User-friendly**: Web-based admin panel for easy management  
✅ **Error-resilient**: Comprehensive error handling and recovery  
✅ **Flexible**: Multiple workflow types and configurations  

The Enhanced MCP Pipeline provides a production-ready solution for automated web scraping and content generation with enterprise-grade reliability and monitoring capabilities.
