#!/usr/bin/env python3
"""
Configuration Management System
Comprehensive configuration management with environment-specific settings,
validation, hot-reloading, and deployment configuration for the enhanced MCP pipeline.
"""

import os
import json
import yaml
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict, field
from enum import Enum
import copy


class Environment(Enum):
    """Deployment environments"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"


@dataclass
class RedisConfig:
    """Redis configuration"""
    url: str = "redis://localhost:6379"
    db: int = 0
    password: Optional[str] = None
    ssl: bool = False
    socket_timeout: int = 5
    socket_connect_timeout: int = 5
    max_connections: int = 10
    retry_on_timeout: bool = True


@dataclass
class DatabaseConfig:
    """Database configuration for future use"""
    enabled: bool = False
    url: str = "sqlite:///pipeline.db"
    pool_size: int = 5
    max_overflow: int = 10
    echo: bool = False


@dataclass
class ScrapingConfig:
    """Scraping-specific configuration"""
    batch_size: int = 10
    concurrent_workers: int = 3
    delay_between_requests: float = 2.0
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 5.0
    user_agent: str = "Enhanced MCP Pipeline Bot 1.0"
    respect_robots_txt: bool = True
    enable_caching: bool = True
    cache_duration: int = 3600


@dataclass
class ContentGenerationConfig:
    """Content generation configuration"""
    batch_size: int = 3
    timeout: int = 300
    max_retries: int = 2
    enable_quality_check: bool = True
    min_content_length: int = 100
    max_content_length: int = 10000
    input_file: str = "data/completed_data.csv"
    output_file: str = "data/generated_content.json"


@dataclass
class MonitoringConfig:
    """Monitoring and metrics configuration"""
    enabled: bool = True
    collection_interval: int = 60
    retention_days: int = 7
    buffer_size: int = 1000
    alert_thresholds: Dict[str, float] = field(default_factory=lambda: {
        'cpu_percent': 80.0,
        'memory_percent': 85.0,
        'disk_percent': 90.0,
        'error_rate': 0.1,
        'response_time': 30.0
    })


@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = "INFO"
    logs_dir: str = "logs"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    enable_compression: bool = True
    enable_structured_logging: bool = True
    enable_real_time_streaming: bool = True


@dataclass
class AdminPanelConfig:
    """Admin panel configuration"""
    host: str = "0.0.0.0"
    port: int = 9000
    debug: bool = False
    secret_key: str = "change-this-in-production"
    session_timeout: int = 3600
    enable_authentication: bool = False
    allowed_ips: List[str] = field(default_factory=list)
    rate_limiting: Dict[str, Any] = field(default_factory=lambda: {
        'enabled': True,
        'requests_per_minute': 60
    })


@dataclass
class SecurityConfig:
    """Security configuration"""
    enable_ssl: bool = False
    ssl_cert_path: Optional[str] = None
    ssl_key_path: Optional[str] = None
    api_key_required: bool = False
    api_keys: List[str] = field(default_factory=list)
    cors_enabled: bool = True
    cors_origins: List[str] = field(default_factory=lambda: ["*"])


@dataclass
class PipelineConfig:
    """Main pipeline configuration"""
    environment: Environment = Environment.DEVELOPMENT
    data_dir: str = "data"
    checkpoint_interval: int = 100
    auto_resume: bool = True
    enable_monitoring: bool = True
    
    # Component configurations
    redis: RedisConfig = field(default_factory=RedisConfig)
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    scraping: ScrapingConfig = field(default_factory=ScrapingConfig)
    content_generation: ContentGenerationConfig = field(default_factory=ContentGenerationConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    admin_panel: AdminPanelConfig = field(default_factory=AdminPanelConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = asdict(self)
        data['environment'] = data['environment'].value
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PipelineConfig':
        """Create from dictionary"""
        # Handle environment enum
        if 'environment' in data:
            data['environment'] = Environment(data['environment'])
        
        # Create nested configs
        config = cls()
        
        for key, value in data.items():
            if hasattr(config, key):
                if key == 'redis' and isinstance(value, dict):
                    config.redis = RedisConfig(**value)
                elif key == 'database' and isinstance(value, dict):
                    config.database = DatabaseConfig(**value)
                elif key == 'scraping' and isinstance(value, dict):
                    config.scraping = ScrapingConfig(**value)
                elif key == 'content_generation' and isinstance(value, dict):
                    config.content_generation = ContentGenerationConfig(**value)
                elif key == 'monitoring' and isinstance(value, dict):
                    config.monitoring = MonitoringConfig(**value)
                elif key == 'logging' and isinstance(value, dict):
                    config.logging = LoggingConfig(**value)
                elif key == 'admin_panel' and isinstance(value, dict):
                    config.admin_panel = AdminPanelConfig(**value)
                elif key == 'security' and isinstance(value, dict):
                    config.security = SecurityConfig(**value)
                else:
                    setattr(config, key, value)
        
        return config


class ConfigManager:
    """Configuration management system"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        self.config_file = self.config_dir / "pipeline_config.json"
        self.env_config_file = None
        self.current_config = None
        
        self.logger = logging.getLogger(__name__)
        
        # Load configuration
        self._load_configuration()

    def _load_configuration(self):
        """Load configuration from files and environment"""
        try:
            # Start with default configuration
            self.current_config = PipelineConfig()
            
            # Load from main config file
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                self.current_config = PipelineConfig.from_dict(config_data)
                self.logger.info(f"Loaded configuration from {self.config_file}")
            
            # Determine environment
            env_name = os.getenv('PIPELINE_ENV', self.current_config.environment.value)
            try:
                environment = Environment(env_name)
                self.current_config.environment = environment
            except ValueError:
                self.logger.warning(f"Invalid environment '{env_name}', using default")
            
            # Load environment-specific config
            env_config_file = self.config_dir / f"config_{self.current_config.environment.value}.json"
            if env_config_file.exists():
                with open(env_config_file, 'r', encoding='utf-8') as f:
                    env_config_data = json.load(f)
                self._merge_config(env_config_data)
                self.env_config_file = env_config_file
                self.logger.info(f"Loaded environment config from {env_config_file}")
            
            # Override with environment variables
            self._load_from_environment()
            
            # Validate configuration
            self._validate_configuration()
            
        except Exception as e:
            self.logger.error(f"Failed to load configuration: {e}")
            # Use default configuration
            self.current_config = PipelineConfig()

    def _merge_config(self, env_config: Dict[str, Any]):
        """Merge environment-specific configuration"""
        try:
            current_dict = self.current_config.to_dict()
            
            # Deep merge
            def deep_merge(base: Dict, override: Dict) -> Dict:
                result = copy.deepcopy(base)
                for key, value in override.items():
                    if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                        result[key] = deep_merge(result[key], value)
                    else:
                        result[key] = value
                return result
            
            merged_dict = deep_merge(current_dict, env_config)
            self.current_config = PipelineConfig.from_dict(merged_dict)
            
        except Exception as e:
            self.logger.error(f"Failed to merge environment configuration: {e}")

    def _load_from_environment(self):
        """Load configuration from environment variables"""
        try:
            # Redis configuration
            if os.getenv('REDIS_URL'):
                self.current_config.redis.url = os.getenv('REDIS_URL')
            if os.getenv('REDIS_DB'):
                self.current_config.redis.db = int(os.getenv('REDIS_DB'))
            if os.getenv('REDIS_PASSWORD'):
                self.current_config.redis.password = os.getenv('REDIS_PASSWORD')
            
            # Admin panel configuration
            if os.getenv('ADMIN_HOST'):
                self.current_config.admin_panel.host = os.getenv('ADMIN_HOST')
            if os.getenv('ADMIN_PORT'):
                self.current_config.admin_panel.port = int(os.getenv('ADMIN_PORT'))
            if os.getenv('SECRET_KEY'):
                self.current_config.admin_panel.secret_key = os.getenv('SECRET_KEY')
            
            # Data directory
            if os.getenv('DATA_DIR'):
                self.current_config.data_dir = os.getenv('DATA_DIR')
            
            # Logging level
            if os.getenv('LOG_LEVEL'):
                self.current_config.logging.level = os.getenv('LOG_LEVEL')
            
            # Security settings
            if os.getenv('ENABLE_SSL'):
                self.current_config.security.enable_ssl = os.getenv('ENABLE_SSL').lower() == 'true'
            if os.getenv('SSL_CERT_PATH'):
                self.current_config.security.ssl_cert_path = os.getenv('SSL_CERT_PATH')
            if os.getenv('SSL_KEY_PATH'):
                self.current_config.security.ssl_key_path = os.getenv('SSL_KEY_PATH')
            
        except Exception as e:
            self.logger.error(f"Failed to load environment variables: {e}")

    def _validate_configuration(self):
        """Validate configuration values"""
        try:
            config = self.current_config
            
            # Validate batch sizes
            if config.scraping.batch_size <= 0:
                raise ValueError("Scraping batch size must be positive")
            if config.content_generation.batch_size <= 0:
                raise ValueError("Content generation batch size must be positive")
            
            # Validate timeouts
            if config.scraping.timeout <= 0:
                raise ValueError("Scraping timeout must be positive")
            if config.content_generation.timeout <= 0:
                raise ValueError("Content generation timeout must be positive")
            
            # Validate delays
            if config.scraping.delay_between_requests < 0:
                raise ValueError("Delay between requests cannot be negative")
            
            # Validate directories
            data_dir = Path(config.data_dir)
            data_dir.mkdir(exist_ok=True)
            
            logs_dir = Path(config.logging.logs_dir)
            logs_dir.mkdir(exist_ok=True)
            
            # Validate admin panel port
            if not (1 <= config.admin_panel.port <= 65535):
                raise ValueError("Admin panel port must be between 1 and 65535")
            
            # Validate monitoring thresholds
            for threshold_name, threshold_value in config.monitoring.alert_thresholds.items():
                if not (0 <= threshold_value <= 100) and 'percent' in threshold_name:
                    raise ValueError(f"Percentage threshold {threshold_name} must be between 0 and 100")
            
            self.logger.info("Configuration validation passed")
            
        except Exception as e:
            self.logger.error(f"Configuration validation failed: {e}")
            raise

    def get_config(self) -> PipelineConfig:
        """Get current configuration"""
        return self.current_config

    def update_config(self, updates: Dict[str, Any], save: bool = True) -> bool:
        """Update configuration with new values"""
        try:
            # Create updated configuration
            current_dict = self.current_config.to_dict()
            
            # Deep merge updates
            def deep_merge(base: Dict, override: Dict) -> Dict:
                result = copy.deepcopy(base)
                for key, value in override.items():
                    if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                        result[key] = deep_merge(result[key], value)
                    else:
                        result[key] = value
                return result
            
            updated_dict = deep_merge(current_dict, updates)
            updated_config = PipelineConfig.from_dict(updated_dict)
            
            # Validate updated configuration
            old_config = self.current_config
            self.current_config = updated_config
            try:
                self._validate_configuration()
            except Exception as e:
                # Restore old configuration on validation failure
                self.current_config = old_config
                raise e
            
            # Save if requested
            if save:
                self.save_config()
            
            self.logger.info("Configuration updated successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to update configuration: {e}")
            return False

    def save_config(self, file_path: Optional[str] = None) -> bool:
        """Save current configuration to file"""
        try:
            if file_path:
                save_path = Path(file_path)
            else:
                save_path = self.config_file
            
            save_path.parent.mkdir(exist_ok=True)
            
            config_dict = self.current_config.to_dict()
            
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Configuration saved to {save_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save configuration: {e}")
            return False

    def export_config(self, format: str = 'json', file_path: Optional[str] = None) -> str:
        """Export configuration in specified format"""
        try:
            config_dict = self.current_config.to_dict()
            
            if format.lower() == 'json':
                content = json.dumps(config_dict, indent=2, ensure_ascii=False)
                extension = '.json'
            elif format.lower() == 'yaml':
                content = yaml.dump(config_dict, default_flow_style=False, allow_unicode=True)
                extension = '.yaml'
            else:
                raise ValueError(f"Unsupported format: {format}")
            
            if file_path:
                export_path = Path(file_path)
            else:
                timestamp = self.current_config.environment.value
                export_path = self.config_dir / f"exported_config_{timestamp}{extension}"
            
            export_path.parent.mkdir(exist_ok=True)
            
            with open(export_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.logger.info(f"Configuration exported to {export_path}")
            return str(export_path)
            
        except Exception as e:
            self.logger.error(f"Failed to export configuration: {e}")
            return ""

    def create_environment_config(self, environment: Environment, 
                                overrides: Dict[str, Any] = None) -> bool:
        """Create environment-specific configuration"""
        try:
            env_config_file = self.config_dir / f"config_{environment.value}.json"
            
            # Start with base configuration
            base_config = self.get_environment_defaults(environment)
            
            # Apply overrides
            if overrides:
                def deep_merge(base: Dict, override: Dict) -> Dict:
                    result = copy.deepcopy(base)
                    for key, value in override.items():
                        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                            result[key] = deep_merge(result[key], value)
                        else:
                            result[key] = value
                    return result
                
                base_config = deep_merge(base_config, overrides)
            
            # Save environment config
            with open(env_config_file, 'w', encoding='utf-8') as f:
                json.dump(base_config, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Created environment configuration for {environment.value}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create environment configuration: {e}")
            return False

    def get_environment_defaults(self, environment: Environment) -> Dict[str, Any]:
        """Get default configuration for environment"""
        if environment == Environment.PRODUCTION:
            return {
                'logging': {
                    'level': 'INFO',
                    'enable_structured_logging': True
                },
                'admin_panel': {
                    'debug': False,
                    'enable_authentication': True
                },
                'security': {
                    'enable_ssl': True,
                    'api_key_required': True,
                    'cors_origins': []
                },
                'monitoring': {
                    'enabled': True,
                    'collection_interval': 30
                },
                'scraping': {
                    'delay_between_requests': 3.0,
                    'respect_robots_txt': True
                }
            }
        elif environment == Environment.STAGING:
            return {
                'logging': {
                    'level': 'DEBUG'
                },
                'admin_panel': {
                    'debug': True
                },
                'monitoring': {
                    'collection_interval': 60
                }
            }
        elif environment == Environment.DEVELOPMENT:
            return {
                'logging': {
                    'level': 'DEBUG'
                },
                'admin_panel': {
                    'debug': True,
                    'enable_authentication': False
                },
                'scraping': {
                    'delay_between_requests': 1.0
                }
            }
        else:  # TESTING
            return {
                'logging': {
                    'level': 'WARNING'
                },
                'scraping': {
                    'batch_size': 2,
                    'delay_between_requests': 0.1
                },
                'content_generation': {
                    'batch_size': 1
                }
            }

    def reload_config(self) -> bool:
        """Reload configuration from files"""
        try:
            self._load_configuration()
            self.logger.info("Configuration reloaded successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to reload configuration: {e}")
            return False

    def get_config_summary(self) -> Dict[str, Any]:
        """Get configuration summary for display"""
        try:
            config = self.current_config
            
            return {
                'environment': config.environment.value,
                'data_dir': config.data_dir,
                'redis_url': config.redis.url,
                'admin_panel_port': config.admin_panel.port,
                'scraping_batch_size': config.scraping.batch_size,
                'content_generation_batch_size': config.content_generation.batch_size,
                'monitoring_enabled': config.monitoring.enabled,
                'logging_level': config.logging.level,
                'auto_resume': config.auto_resume,
                'checkpoint_interval': config.checkpoint_interval
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get configuration summary: {e}")
            return {'error': str(e)}
