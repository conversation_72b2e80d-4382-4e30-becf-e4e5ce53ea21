#!/usr/bin/env python3
"""
Pipeline Orchestration System
Central orchestrator that manages the entire pipeline workflow,
coordinates between scraping and content generation, and handles scheduling.
"""

import asyncio
import logging
import schedule
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path
import json
import threading
from enum import Enum

# Import pipeline components
from ..core.pipeline_manager import PipelineManager, PipelineConfig, PipelineState
from ..core.state_manager import StateManager
from ..core.url_manager import URLManager, URLStatus, URLPriority
from ..scrapers.resumable_scraper import ResumableScraper
from ..generators.resumable_generator import ResumableContentGenerator


class WorkflowType(Enum):
    """Types of automated workflows"""
    SCRAPE_ONLY = "scrape_only"
    GENERATE_ONLY = "generate_only"
    SCRAPE_THEN_GENERATE = "scrape_then_generate"
    CONTINUOUS_SCRAPE = "continuous_scrape"
    DAILY_FULL_PIPELINE = "daily_full_pipeline"


class WorkflowStatus(Enum):
    """Workflow execution status"""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    SCHEDULED = "scheduled"


class WorkflowDefinition:
    """Definition of an automated workflow"""
    
    def __init__(self, workflow_id: str, workflow_type: WorkflowType, 
                 config: Dict[str, Any] = None):
        self.workflow_id = workflow_id
        self.workflow_type = workflow_type
        self.config = config or {}
        self.created_at = datetime.now()
        self.last_run = None
        self.next_run = None
        self.status = WorkflowStatus.IDLE
        self.run_count = 0
        self.success_count = 0
        self.failure_count = 0

    def to_dict(self) -> Dict[str, Any]:
        # Filter out non-serializable objects from config
        serializable_config = {}
        if self.config:
            for key, value in self.config.items():
                try:
                    # Test if value is JSON serializable
                    json.dumps(value)
                    serializable_config[key] = value
                except (TypeError, ValueError):
                    # Skip non-serializable objects
                    continue

        return {
            'workflow_id': self.workflow_id,
            'workflow_type': self.workflow_type.value,
            'config': serializable_config,
            'created_at': self.created_at.isoformat(),
            'last_run': self.last_run.isoformat() if self.last_run else None,
            'next_run': self.next_run.isoformat() if self.next_run else None,
            'status': self.status.value,
            'run_count': self.run_count,
            'success_count': self.success_count,
            'failure_count': self.failure_count
        }


class PipelineOrchestrator:
    """Central orchestrator for managing pipeline workflows"""
    
    def __init__(self, config: PipelineConfig = None):
        self.config = config or PipelineConfig()
        
        # Core managers
        self.pipeline_manager = PipelineManager(self.config)
        self.state_manager = StateManager()
        self.url_manager = URLManager()
        
        # Active components
        self.active_scrapers = {}
        self.active_generators = {}
        self.workflows = {}
        
        # Scheduling
        self.scheduler_thread = None
        self.scheduler_running = False
        
        # Event callbacks
        self.on_workflow_complete: Optional[Callable] = None
        self.on_workflow_failed: Optional[Callable] = None
        self.on_pipeline_complete: Optional[Callable] = None
        
        self.logger = logging.getLogger(__name__)

    async def initialize(self) -> bool:
        """Initialize the orchestrator"""
        try:
            self.logger.info("Initializing Pipeline Orchestrator")
            
            # Load existing workflows
            await self._load_workflows()
            
            # Start scheduler
            self._start_scheduler()
            
            self.logger.info("Pipeline Orchestrator initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize orchestrator: {e}")
            return False

    async def create_workflow(self, workflow_id: str, workflow_type: WorkflowType,
                            config: Dict[str, Any] = None) -> bool:
        """Create a new automated workflow"""
        try:
            if workflow_id in self.workflows:
                self.logger.warning(f"Workflow {workflow_id} already exists")
                return False
            
            workflow = WorkflowDefinition(workflow_id, workflow_type, config)
            self.workflows[workflow_id] = workflow
            
            # Schedule if needed
            if config and config.get('schedule'):
                await self._schedule_workflow(workflow)
            
            await self._save_workflows()
            
            self.logger.info(f"Created workflow {workflow_id} of type {workflow_type.value}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create workflow: {e}")
            return False

    async def run_workflow(self, workflow_id: str, force: bool = False) -> bool:
        """Execute a workflow"""
        try:
            if workflow_id not in self.workflows:
                self.logger.error(f"Workflow {workflow_id} not found")
                return False
            
            workflow = self.workflows[workflow_id]
            
            if workflow.status == WorkflowStatus.RUNNING and not force:
                self.logger.warning(f"Workflow {workflow_id} is already running")
                return False
            
            workflow.status = WorkflowStatus.RUNNING
            workflow.last_run = datetime.now()
            workflow.run_count += 1
            
            self.logger.info(f"Starting workflow {workflow_id}")
            
            # Execute based on workflow type
            success = await self._execute_workflow(workflow)
            
            if success:
                workflow.status = WorkflowStatus.COMPLETED
                workflow.success_count += 1
                self.logger.info(f"Workflow {workflow_id} completed successfully")
                
                if self.on_workflow_complete:
                    await self.on_workflow_complete(workflow)
            else:
                workflow.status = WorkflowStatus.FAILED
                workflow.failure_count += 1
                self.logger.error(f"Workflow {workflow_id} failed")
                
                if self.on_workflow_failed:
                    await self.on_workflow_failed(workflow)
            
            await self._save_workflows()
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to run workflow {workflow_id}: {e}")
            if workflow_id in self.workflows:
                self.workflows[workflow_id].status = WorkflowStatus.FAILED
                self.workflows[workflow_id].failure_count += 1
            return False

    async def _execute_workflow(self, workflow: WorkflowDefinition) -> bool:
        """Execute a specific workflow based on its type"""
        try:
            if workflow.workflow_type == WorkflowType.SCRAPE_ONLY:
                return await self._execute_scrape_workflow(workflow)
            
            elif workflow.workflow_type == WorkflowType.GENERATE_ONLY:
                return await self._execute_generate_workflow(workflow)
            
            elif workflow.workflow_type == WorkflowType.SCRAPE_THEN_GENERATE:
                return await self._execute_scrape_then_generate_workflow(workflow)
            
            elif workflow.workflow_type == WorkflowType.CONTINUOUS_SCRAPE:
                return await self._execute_continuous_scrape_workflow(workflow)
            
            elif workflow.workflow_type == WorkflowType.DAILY_FULL_PIPELINE:
                return await self._execute_daily_full_pipeline_workflow(workflow)
            
            else:
                self.logger.error(f"Unknown workflow type: {workflow.workflow_type}")
                return False
                
        except Exception as e:
            self.logger.error(f"Workflow execution failed: {e}")
            return False

    async def _execute_scrape_workflow(self, workflow: WorkflowDefinition) -> bool:
        """Execute scraping-only workflow"""
        try:
            config = workflow.config
            urls = config.get('urls', [])
            
            if not urls:
                # Load URLs from master data
                urls = await self._load_urls_from_master_data()
            
            if not urls:
                self.logger.warning("No URLs available for scraping")
                return True  # Not a failure, just nothing to do
            
            # Create scraper
            scraper_config = config.get('scraper_config', {})
            scraper = ResumableScraper(workflow.workflow_id, scraper_config)
            
            # Initialize and start
            await scraper.initialize(urls, resume=config.get('resume', False))
            self.active_scrapers[workflow.workflow_id] = scraper
            
            success = await scraper.start_scraping(resume=config.get('resume', False))
            
            # Cleanup
            await scraper.cleanup()
            self.active_scrapers.pop(workflow.workflow_id, None)
            
            return success
            
        except Exception as e:
            self.logger.error(f"Scrape workflow failed: {e}")
            return False

    async def _execute_generate_workflow(self, workflow: WorkflowDefinition) -> bool:
        """Execute content generation workflow"""
        try:
            config = workflow.config
            
            # Create generator
            generator_config = config.get('generator_config', {})
            generator = ResumableContentGenerator(workflow.workflow_id, generator_config)
            
            # Initialize and start
            await generator.initialize(resume=config.get('resume', False))
            self.active_generators[workflow.workflow_id] = generator
            
            success = await generator.start_generation(resume=config.get('resume', False))
            
            # Cleanup
            await generator.cleanup()
            self.active_generators.pop(workflow.workflow_id, None)
            
            return success
            
        except Exception as e:
            self.logger.error(f"Generate workflow failed: {e}")
            return False

    async def _execute_scrape_then_generate_workflow(self, workflow: WorkflowDefinition) -> bool:
        """Execute scraping followed by content generation"""
        try:
            # First, run scraping
            scrape_success = await self._execute_scrape_workflow(workflow)
            
            if not scrape_success:
                self.logger.error("Scraping phase failed, skipping content generation")
                return False
            
            # Wait a bit for data to settle
            await asyncio.sleep(5)
            
            # Then run content generation
            generate_success = await self._execute_generate_workflow(workflow)
            
            return generate_success
            
        except Exception as e:
            self.logger.error(f"Scrape-then-generate workflow failed: {e}")
            return False

    async def _execute_continuous_scrape_workflow(self, workflow: WorkflowDefinition) -> bool:
        """Execute continuous scraping workflow"""
        try:
            config = workflow.config
            interval_hours = config.get('interval_hours', 6)
            max_iterations = config.get('max_iterations', 0)  # 0 = infinite
            
            iteration = 0
            
            while True:
                if max_iterations > 0 and iteration >= max_iterations:
                    break
                
                # Check if workflow should stop
                if workflow.status != WorkflowStatus.RUNNING:
                    break
                
                # Run scraping iteration
                scrape_success = await self._execute_scrape_workflow(workflow)
                
                if not scrape_success:
                    self.logger.warning(f"Scraping iteration {iteration + 1} failed")
                
                iteration += 1
                
                # Wait for next iteration
                if max_iterations == 0 or iteration < max_iterations:
                    await asyncio.sleep(interval_hours * 3600)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Continuous scrape workflow failed: {e}")
            return False

    async def _execute_daily_full_pipeline_workflow(self, workflow: WorkflowDefinition) -> bool:
        """Execute daily full pipeline workflow"""
        try:
            # This is essentially scrape-then-generate with additional features
            config = workflow.config
            
            # 1. Discover new URLs (if enabled)
            if config.get('auto_discover', True):
                await self._auto_discover_urls()
            
            # 2. Run scraping
            scrape_success = await self._execute_scrape_workflow(workflow)
            
            # 3. Run content generation (even if scraping partially failed)
            generate_success = await self._execute_generate_workflow(workflow)
            
            # 4. Cleanup old data (if enabled)
            if config.get('auto_cleanup', True):
                cleanup_days = config.get('cleanup_days', 30)
                await self.state_manager.cleanup_completed_states(cleanup_days)
                await self.url_manager.cleanup_completed_urls('master', cleanup_days)
            
            # 5. Generate reports (if enabled)
            if config.get('generate_reports', False):
                await self._generate_daily_report(workflow)
            
            return scrape_success and generate_success
            
        except Exception as e:
            self.logger.error(f"Daily full pipeline workflow failed: {e}")
            return False

    async def _load_urls_from_master_data(self) -> List[str]:
        """Load URLs from master data file"""
        try:
            master_file = Path('data/master_data.csv')
            if master_file.exists():
                import pandas as pd
                df = pd.read_csv(master_file)
                return df['url'].tolist()
            return []
        except Exception as e:
            self.logger.error(f"Failed to load URLs from master data: {e}")
            return []

    async def _auto_discover_urls(self) -> int:
        """Auto-discover new URLs from sources"""
        try:
            # This would integrate with the existing URL discovery system
            # For now, just log the intent
            self.logger.info("Auto-discovering new URLs...")
            return 0
        except Exception as e:
            self.logger.error(f"Auto-discovery failed: {e}")
            return 0

    async def _generate_daily_report(self, workflow: WorkflowDefinition) -> bool:
        """Generate daily pipeline report"""
        try:
            # Generate a summary report
            report = {
                'workflow_id': workflow.workflow_id,
                'date': datetime.now().isoformat(),
                'run_count': workflow.run_count,
                'success_count': workflow.success_count,
                'failure_count': workflow.failure_count,
                'statistics': await self._get_pipeline_statistics()
            }
            
            # Save report
            reports_dir = Path('data/reports')
            reports_dir.mkdir(exist_ok=True)
            
            report_file = reports_dir / f"daily_report_{datetime.now().strftime('%Y%m%d')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Generated daily report: {report_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to generate daily report: {e}")
            return False

    async def _get_pipeline_statistics(self) -> Dict[str, Any]:
        """Get overall pipeline statistics"""
        try:
            all_states = await self.state_manager.get_all_pipeline_states()
            
            stats = {
                'total_pipelines': len(all_states),
                'by_status': {},
                'by_type': {},
                'total_tasks': 0,
                'completed_tasks': 0,
                'failed_tasks': 0
            }
            
            for state in all_states.values():
                status = state.get('status', 'unknown')
                task_type = state.get('task_type', 'unknown')
                
                stats['by_status'][status] = stats['by_status'].get(status, 0) + 1
                stats['by_type'][task_type] = stats['by_type'].get(task_type, 0) + 1
                
                stats['total_tasks'] += state.get('total_tasks', 0)
                stats['completed_tasks'] += state.get('completed_tasks', 0)
                stats['failed_tasks'] += state.get('failed_tasks', 0)
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get pipeline statistics: {e}")
            return {}

    async def _schedule_workflow(self, workflow: WorkflowDefinition) -> None:
        """Schedule a workflow for automatic execution"""
        try:
            schedule_config = workflow.config.get('schedule', {})
            
            if schedule_config.get('daily'):
                time_str = schedule_config.get('time', '02:00')
                schedule.every().day.at(time_str).do(
                    lambda: asyncio.create_task(self.run_workflow(workflow.workflow_id))
                )
                
            elif schedule_config.get('hourly'):
                interval = schedule_config.get('interval', 1)
                schedule.every(interval).hours.do(
                    lambda: asyncio.create_task(self.run_workflow(workflow.workflow_id))
                )
                
            elif schedule_config.get('weekly'):
                day = schedule_config.get('day', 'monday')
                time_str = schedule_config.get('time', '02:00')
                getattr(schedule.every(), day).at(time_str).do(
                    lambda: asyncio.create_task(self.run_workflow(workflow.workflow_id))
                )
            
            workflow.status = WorkflowStatus.SCHEDULED
            self.logger.info(f"Scheduled workflow {workflow.workflow_id}")
            
        except Exception as e:
            self.logger.error(f"Failed to schedule workflow: {e}")

    def _start_scheduler(self) -> None:
        """Start the background scheduler thread"""
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            return
        
        self.scheduler_running = True
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        
        self.logger.info("Started workflow scheduler")

    def _scheduler_loop(self) -> None:
        """Background scheduler loop"""
        while self.scheduler_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
            except Exception as e:
                self.logger.error(f"Scheduler error: {e}")
                time.sleep(60)

    async def _save_workflows(self) -> None:
        """Save workflows to file"""
        try:
            workflows_file = Path('data/workflows.json')
            workflows_file.parent.mkdir(exist_ok=True)
            
            workflows_data = {
                wf_id: wf.to_dict() for wf_id, wf in self.workflows.items()
            }
            
            with open(workflows_file, 'w', encoding='utf-8') as f:
                json.dump(workflows_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            self.logger.error(f"Failed to save workflows: {e}")

    async def _load_workflows(self) -> None:
        """Load workflows from file"""
        try:
            workflows_file = Path('data/workflows.json')
            if not workflows_file.exists():
                return
            
            with open(workflows_file, 'r', encoding='utf-8') as f:
                workflows_data = json.load(f)
            
            for wf_id, wf_data in workflows_data.items():
                workflow = WorkflowDefinition(
                    wf_id,
                    WorkflowType(wf_data['workflow_type']),
                    wf_data['config']
                )
                
                # Restore state
                workflow.created_at = datetime.fromisoformat(wf_data['created_at'])
                if wf_data['last_run']:
                    workflow.last_run = datetime.fromisoformat(wf_data['last_run'])
                if wf_data['next_run']:
                    workflow.next_run = datetime.fromisoformat(wf_data['next_run'])
                workflow.status = WorkflowStatus(wf_data['status'])
                workflow.run_count = wf_data['run_count']
                workflow.success_count = wf_data['success_count']
                workflow.failure_count = wf_data['failure_count']
                
                self.workflows[wf_id] = workflow
                
                # Re-schedule if needed
                if workflow.status == WorkflowStatus.SCHEDULED:
                    await self._schedule_workflow(workflow)
            
            self.logger.info(f"Loaded {len(self.workflows)} workflows")
            
        except Exception as e:
            self.logger.error(f"Failed to load workflows: {e}")

    async def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific workflow"""
        if workflow_id not in self.workflows:
            return None
        
        workflow = self.workflows[workflow_id]
        status = workflow.to_dict()
        
        # Add runtime information
        if workflow_id in self.active_scrapers:
            scraper_stats = await self.active_scrapers[workflow_id].get_statistics()
            status['scraper_status'] = scraper_stats
        
        if workflow_id in self.active_generators:
            generator_stats = await self.active_generators[workflow_id].get_statistics()
            status['generator_status'] = generator_stats
        
        return status

    async def get_all_workflows(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all workflows"""
        result = {}
        for wf_id in self.workflows:
            result[wf_id] = await self.get_workflow_status(wf_id)
        return result

    async def pause_workflow(self, workflow_id: str) -> bool:
        """Pause a running workflow"""
        try:
            if workflow_id not in self.workflows:
                return False
            
            workflow = self.workflows[workflow_id]
            workflow.status = WorkflowStatus.PAUSED
            
            # Pause active components
            if workflow_id in self.active_scrapers:
                await self.active_scrapers[workflow_id].pause()
            
            if workflow_id in self.active_generators:
                await self.active_generators[workflow_id].pause()
            
            await self._save_workflows()
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to pause workflow: {e}")
            return False

    async def stop_workflow(self, workflow_id: str) -> bool:
        """Stop a running workflow"""
        try:
            if workflow_id not in self.workflows:
                return False
            
            workflow = self.workflows[workflow_id]
            workflow.status = WorkflowStatus.IDLE
            
            # Stop active components
            if workflow_id in self.active_scrapers:
                await self.active_scrapers[workflow_id].stop()
                self.active_scrapers.pop(workflow_id, None)
            
            if workflow_id in self.active_generators:
                await self.active_generators[workflow_id].stop()
                self.active_generators.pop(workflow_id, None)
            
            await self._save_workflows()
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to stop workflow: {e}")
            return False

    async def delete_workflow(self, workflow_id: str) -> bool:
        """Delete a workflow"""
        try:
            if workflow_id not in self.workflows:
                return False
            
            # Stop if running
            await self.stop_workflow(workflow_id)
            
            # Remove from workflows
            del self.workflows[workflow_id]
            
            await self._save_workflows()
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to delete workflow: {e}")
            return False

    async def shutdown(self) -> None:
        """Shutdown the orchestrator"""
        try:
            self.logger.info("Shutting down Pipeline Orchestrator")
            
            # Stop scheduler
            self.scheduler_running = False
            if self.scheduler_thread:
                self.scheduler_thread.join(timeout=5)
            
            # Stop all active components
            for scraper in self.active_scrapers.values():
                await scraper.stop()
                await scraper.cleanup()
            
            for generator in self.active_generators.values():
                await generator.stop()
                await generator.cleanup()
            
            # Save final state
            await self._save_workflows()
            
            # Close managers
            await self.state_manager.close()
            await self.url_manager.close()
            
            self.logger.info("Pipeline Orchestrator shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
