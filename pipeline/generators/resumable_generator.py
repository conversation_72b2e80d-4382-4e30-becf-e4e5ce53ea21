#!/usr/bin/env python3
"""
Resumable Content Generation System
Advanced content generation pipeline with resumability, batch processing,
and state management. Integrates with existing CrewAI infrastructure.
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path
import sys
import os
import pandas as pd

# Add content processor to path
sys.path.append(str(Path(__file__).parent.parent.parent / "content_processor"))

# Import existing content generation functionality
try:
    from content_processor.processor.content_processor import ContentProcessor
    from content_processor.agents.content_agents import create_content_agents
    from content_processor.tasks.content_tasks import create_content_tasks
except ImportError as e:
    logging.error(f"Failed to import content generation modules: {e}")
    # Create mock classes for development
    class ContentProcessor:
        def __init__(self, *args, **kwargs):
            pass
        def process_all(self):
            pass

# Import our pipeline components
from ..core.state_manager import StateManager, StateSnapshot
from ..core.url_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>, URLStatus, URLPriority, URLItem


class GenerationResult:
    """Container for content generation results"""
    
    def __init__(self, url: str, success: bool, content: Dict[str, Any] = None, 
                 error: str = None, duration: float = 0):
        self.url = url
        self.success = success
        self.content = content or {}
        self.error = error
        self.duration = duration
        self.timestamp = datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        return {
            'url': self.url,
            'success': self.success,
            'content': self.content,
            'error': self.error,
            'duration': self.duration,
            'timestamp': self.timestamp.isoformat()
        }


class ResumableContentGenerator:
    """Enhanced content generation engine with resumability and state management"""
    
    def __init__(self, pipeline_id: str, config: Dict[str, Any] = None):
        self.pipeline_id = pipeline_id
        self.config = config or {}
        
        # Configuration with defaults
        self.batch_size = self.config.get('batch_size', 3)
        self.max_retries = self.config.get('max_retries', 2)
        self.timeout = self.config.get('timeout', 300)  # 5 minutes per item
        self.checkpoint_interval = self.config.get('checkpoint_interval', 10)
        self.input_file = self.config.get('input_file', 'data/completed_data.csv')
        self.output_file = self.config.get('output_file', 'data/generated_content.json')
        
        # Initialize managers
        self.state_manager = StateManager()
        self.url_manager = URLManager()
        
        # Content processor
        self.content_processor = None
        
        # State tracking
        self.is_running = False
        self.is_paused = False
        self.processed_count = 0
        self.failed_count = 0
        self.skipped_count = 0
        self.start_time = None
        
        # Results storage
        self.results = []
        self.generated_content = []
        
        # Setup logging
        self.logger = logging.getLogger(f"{__name__}.{pipeline_id}")
        
        # Callbacks
        self.on_progress_callback: Optional[Callable] = None
        self.on_result_callback: Optional[Callable] = None
        self.on_error_callback: Optional[Callable] = None

    async def initialize(self, input_data: List[Dict[str, Any]] = None, 
                        resume: bool = False) -> bool:
        """Initialize the content generator"""
        try:
            self.logger.info(f"Initializing content generator for pipeline {self.pipeline_id}")
            
            # Load input data
            if input_data is None:
                input_data = await self._load_input_data()
            
            if not input_data:
                self.logger.error("No input data available for content generation")
                return False
            
            # Initialize content processor
            try:
                self.content_processor = ContentProcessor(
                    input_file=self.input_file,
                    output_file=self.output_file,
                    batch_size=self.batch_size,
                    event_driven=False
                )
                self.logger.info("Content processor initialized successfully")
            except Exception as e:
                self.logger.error(f"Failed to initialize content processor: {e}")
                return False
            
            if not resume:
                # Fresh start - create URL items for content generation
                urls = [item.get('repo_url', '') for item in input_data if item.get('repo_url')]
                
                added_count = await self.url_manager.add_urls(
                    urls=urls,
                    collection=f"{self.pipeline_id}_content",
                    source="content_generator_init",
                    priority=URLPriority.NORMAL
                )
                
                # Create initial pipeline state
                await self.state_manager.create_pipeline_state(
                    pipeline_id=f"{self.pipeline_id}_content",
                    total_tasks=len(input_data),
                    task_type='generate',
                    metadata={
                        'generator_config': self.config,
                        'total_items': len(input_data),
                        'added_urls': added_count,
                        'input_file': self.input_file,
                        'output_file': self.output_file
                    }
                )
                
                self.logger.info(f"Fresh start: Prepared {len(input_data)} items for content generation")
            else:
                # Resume - check existing state
                state = await self.state_manager.get_pipeline_state(f"{self.pipeline_id}_content")
                if not state:
                    self.logger.error(f"Cannot resume: No existing state for pipeline {self.pipeline_id}_content")
                    return False
                
                # Get checkpoint if available
                checkpoint = await self.state_manager.get_latest_checkpoint(f"{self.pipeline_id}_content")
                if checkpoint:
                    self.processed_count = checkpoint.completed_count
                    self.failed_count = checkpoint.failed_count
                    self.logger.info(f"Resuming from checkpoint: {self.processed_count} completed, {self.failed_count} failed")
                
                self.logger.info(f"Resuming content generation for pipeline {self.pipeline_id}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize content generator: {e}")
            return False

    async def _load_input_data(self) -> List[Dict[str, Any]]:
        """Load input data from file"""
        try:
            input_path = Path(self.input_file)
            
            if not input_path.exists():
                self.logger.error(f"Input file not found: {self.input_file}")
                return []
            
            if input_path.suffix.lower() == '.csv':
                df = pd.read_csv(input_path)
                return df.to_dict('records')
            elif input_path.suffix.lower() == '.json':
                with open(input_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data if isinstance(data, list) else [data]
            else:
                self.logger.error(f"Unsupported input file format: {input_path.suffix}")
                return []
                
        except Exception as e:
            self.logger.error(f"Failed to load input data: {e}")
            return []

    async def start_generation(self, resume: bool = False) -> bool:
        """Start the content generation process"""
        try:
            if self.is_running:
                self.logger.warning("Content generator is already running")
                return False
            
            self.is_running = True
            self.is_paused = False
            self.start_time = datetime.now()
            
            # Mark pipeline as started
            await self.state_manager.mark_pipeline_started(f"{self.pipeline_id}_content")
            
            self.logger.info(f"Starting content generation for pipeline {self.pipeline_id}")
            
            # Load existing generated content to avoid duplicates
            existing_content = await self._load_existing_content()
            existing_urls = {item.get('content', {}).get('github_url', '') for item in existing_content}
            
            # Main generation loop
            while self.is_running and not self.is_paused:
                # Get next batch of URLs for content generation
                next_urls = await self.url_manager.get_next_urls(
                    collection=f"{self.pipeline_id}_content",
                    count=self.batch_size,
                    status=URLStatus.PENDING
                )
                
                if not next_urls:
                    # Check for retry URLs
                    retry_urls = await self.url_manager.get_next_urls(
                        collection=f"{self.pipeline_id}_content",
                        count=self.batch_size,
                        status=URLStatus.RETRY
                    )
                    
                    if not retry_urls:
                        self.logger.info("No more items to process for content generation")
                        break
                    
                    next_urls = retry_urls
                
                # Filter out already generated content
                filtered_urls = [
                    url_item for url_item in next_urls 
                    if url_item.url not in existing_urls
                ]
                
                if not filtered_urls:
                    # Mark remaining URLs as skipped
                    for url_item in next_urls:
                        await self.url_manager.update_url_status(
                            f"{self.pipeline_id}_content", url_item.url, 
                            URLStatus.SKIPPED, "Already generated"
                        )
                        self.skipped_count += 1
                    continue
                
                # Process batch
                await self._process_generation_batch(filtered_urls)
                
                # Create checkpoint periodically
                if self.processed_count % self.checkpoint_interval == 0:
                    await self._create_checkpoint()
                
                # Update progress
                await self._update_progress()
                
                # Small delay between batches
                await asyncio.sleep(1)
            
            # Final checkpoint and completion
            await self._create_checkpoint()
            
            if not self.is_paused:
                await self.state_manager.mark_pipeline_completed(f"{self.pipeline_id}_content")
                self.logger.info(f"Content generation completed for pipeline {self.pipeline_id}")
            
            self.is_running = False
            return True
            
        except Exception as e:
            self.logger.error(f"Content generation failed: {e}")
            await self.state_manager.mark_pipeline_failed(f"{self.pipeline_id}_content", str(e))
            self.is_running = False
            return False

    async def _load_existing_content(self) -> List[Dict[str, Any]]:
        """Load existing generated content"""
        try:
            output_path = Path(self.output_file)
            if output_path.exists():
                with open(output_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            self.logger.warning(f"Failed to load existing content: {e}")
            return []

    async def _process_generation_batch(self, url_items: List[URLItem]) -> None:
        """Process a batch of URLs for content generation"""
        try:
            self.logger.debug(f"Processing content generation batch of {len(url_items)} items")
            
            # Get input data for these URLs
            input_data = await self._load_input_data()
            batch_data = []
            
            for url_item in url_items:
                # Find matching input data
                matching_data = next(
                    (item for item in input_data if item.get('repo_url') == url_item.url),
                    None
                )
                if matching_data:
                    batch_data.append(matching_data)
            
            if not batch_data:
                self.logger.warning("No matching input data found for batch")
                return
            
            # Process each item in the batch
            for i, data_item in enumerate(batch_data):
                if not self.is_running or self.is_paused:
                    break
                
                url_item = url_items[i]
                result = await self._process_single_item(url_item, data_item)
                
                if result:
                    self.results.append(result)
                    if result.success:
                        self.processed_count += 1
                        self.generated_content.append(result.content)
                    else:
                        self.failed_count += 1
            
            # Save generated content
            if self.generated_content:
                await self._save_generated_content()
            
        except Exception as e:
            self.logger.error(f"Batch processing failed: {e}")

    async def _process_single_item(self, url_item: URLItem, data_item: Dict[str, Any]) -> Optional[GenerationResult]:
        """Process a single item for content generation"""
        start_time = time.time()
        
        try:
            # Mark as in progress
            await self.url_manager.update_url_status(
                f"{self.pipeline_id}_content", url_item.url, URLStatus.IN_PROGRESS
            )
            
            self.logger.debug(f"Generating content for {url_item.url}")
            
            # Use the content processor to generate content
            # This is a simplified version - you might need to adapt based on your ContentProcessor implementation
            generated_content = await self._generate_content_for_item(data_item)
            
            if not generated_content:
                raise ValueError("Content generation returned empty result")
            
            # Mark as completed
            await self.url_manager.update_url_status(
                f"{self.pipeline_id}_content", url_item.url, URLStatus.COMPLETED
            )
            
            duration = time.time() - start_time
            result = GenerationResult(
                url=url_item.url,
                success=True,
                content=generated_content,
                duration=duration
            )
            
            # Call result callback if set
            if self.on_result_callback:
                await self.on_result_callback(result)
            
            self.logger.debug(f"Successfully generated content for {url_item.url} in {duration:.2f}s")
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            error_msg = str(e)
            
            # Determine if we should retry
            if url_item.attempts < self.max_retries:
                await self.url_manager.update_url_status(
                    f"{self.pipeline_id}_content", url_item.url, URLStatus.RETRY, error_msg
                )
                self.logger.warning(f"Failed to generate content for {url_item.url} (attempt {url_item.attempts}): {error_msg}")
            else:
                await self.url_manager.update_url_status(
                    f"{self.pipeline_id}_content", url_item.url, URLStatus.FAILED, error_msg
                )
                self.logger.error(f"Failed to generate content for {url_item.url} after {url_item.attempts} attempts: {error_msg}")
            
            result = GenerationResult(
                url=url_item.url,
                success=False,
                error=error_msg,
                duration=duration
            )
            
            # Call error callback if set
            if self.on_error_callback:
                await self.on_error_callback(result)
            
            return result

    async def _generate_content_for_item(self, data_item: Dict[str, Any]) -> Dict[str, Any]:
        """Generate content for a single data item"""
        try:
            # This is a simplified implementation
            # In practice, you would integrate with your existing ContentProcessor
            
            # Create a temporary file with just this item
            temp_data = [data_item]
            
            # Use the content processor (this is a mock implementation)
            # You'll need to adapt this based on your actual ContentProcessor API
            if hasattr(self.content_processor, 'process_single_item'):
                return await self.content_processor.process_single_item(data_item)
            else:
                # Fallback: create basic content structure
                return {
                    'id': data_item.get('slug', '').replace('/', '-'),
                    'title': f"{data_item.get('slug', 'Unknown')} - MCP Server",
                    'github_url': data_item.get('repo_url', ''),
                    'github_repo': data_item.get('slug', ''),
                    'github_stars': data_item.get('stars', 0),
                    'language': data_item.get('language', 'Unknown'),
                    'description': data_item.get('description_short', ''),
                    'readme_content': data_item.get('readme', ''),
                    'last_content_update': datetime.now().isoformat(),
                    'generated_by': 'resumable_generator'
                }
                
        except Exception as e:
            self.logger.error(f"Content generation failed for item: {e}")
            raise

    async def _save_generated_content(self) -> None:
        """Save generated content to output file"""
        try:
            # Load existing content
            existing_content = await self._load_existing_content()
            
            # Combine with new content
            all_content = existing_content + [
                {'content': item} for item in self.generated_content
            ]
            
            # Remove duplicates based on URL
            seen_urls = set()
            unique_content = []
            for item in all_content:
                url = item.get('content', {}).get('github_url', '')
                if url and url not in seen_urls:
                    unique_content.append(item)
                    seen_urls.add(url)
            
            # Save to file
            output_path = Path(self.output_file)
            output_path.parent.mkdir(exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(unique_content, f, indent=2, ensure_ascii=False)
            
            self.logger.debug(f"Saved {len(unique_content)} content items to {self.output_file}")
            
            # Clear the buffer
            self.generated_content = []
            
        except Exception as e:
            self.logger.error(f"Failed to save generated content: {e}")

    async def _create_checkpoint(self) -> None:
        """Create a checkpoint for resumability"""
        try:
            last_url = self.results[-1].url if self.results else None
            
            await self.state_manager.create_checkpoint(
                pipeline_id=f"{self.pipeline_id}_content",
                current_task_index=self.processed_count,
                last_processed_url=last_url or "",
                metadata={
                    'processed_count': self.processed_count,
                    'failed_count': self.failed_count,
                    'skipped_count': self.skipped_count,
                    'batch_size': self.batch_size,
                    'checkpoint_time': datetime.now().isoformat(),
                    'generated_items': len(self.generated_content)
                }
            )
            
            self.logger.debug(f"Created checkpoint at {self.processed_count} processed items")
            
        except Exception as e:
            self.logger.error(f"Failed to create checkpoint: {e}")

    async def _update_progress(self) -> None:
        """Update progress in state manager"""
        try:
            await self.state_manager.update_progress(
                pipeline_id=f"{self.pipeline_id}_content",
                completed_tasks=self.processed_count,
                failed_tasks=self.failed_count,
                skipped_tasks=self.skipped_count
            )
            
            # Call progress callback if set
            if self.on_progress_callback:
                progress_data = {
                    'processed': self.processed_count,
                    'failed': self.failed_count,
                    'skipped': self.skipped_count,
                    'total': self.processed_count + self.failed_count + self.skipped_count
                }
                await self.on_progress_callback(progress_data)
            
        except Exception as e:
            self.logger.error(f"Failed to update progress: {e}")

    async def pause(self) -> bool:
        """Pause the content generation process"""
        try:
            self.is_paused = True
            await self._create_checkpoint()
            await self._save_generated_content()
            self.logger.info(f"Paused content generation for pipeline {self.pipeline_id}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to pause content generation: {e}")
            return False

    async def resume(self) -> bool:
        """Resume the content generation process"""
        try:
            self.is_paused = False
            self.logger.info(f"Resumed content generation for pipeline {self.pipeline_id}")
            return await self.start_generation(resume=True)
        except Exception as e:
            self.logger.error(f"Failed to resume content generation: {e}")
            return False

    async def stop(self) -> bool:
        """Stop the content generation process"""
        try:
            self.is_running = False
            self.is_paused = False
            await self._create_checkpoint()
            await self._save_generated_content()
            self.logger.info(f"Stopped content generation for pipeline {self.pipeline_id}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to stop content generation: {e}")
            return False

    async def get_statistics(self) -> Dict[str, Any]:
        """Get current content generation statistics"""
        try:
            url_stats = await self.url_manager.get_statistics(f"{self.pipeline_id}_content")
            state = await self.state_manager.get_pipeline_state(f"{self.pipeline_id}_content")
            
            elapsed_time = 0
            if self.start_time:
                elapsed_time = (datetime.now() - self.start_time).total_seconds()
            
            return {
                'pipeline_id': self.pipeline_id,
                'is_running': self.is_running,
                'is_paused': self.is_paused,
                'processed_count': self.processed_count,
                'failed_count': self.failed_count,
                'skipped_count': self.skipped_count,
                'elapsed_time': elapsed_time,
                'url_statistics': url_stats,
                'pipeline_state': state,
                'results_count': len(self.results),
                'generated_content_count': len(self.generated_content)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get statistics: {e}")
            return {}

    async def cleanup(self) -> None:
        """Cleanup resources"""
        try:
            await self.state_manager.close()
            await self.url_manager.close()
        except Exception as e:
            self.logger.error(f"Cleanup failed: {e}")

    def set_progress_callback(self, callback: Callable) -> None:
        """Set progress callback function"""
        self.on_progress_callback = callback

    def set_result_callback(self, callback: Callable) -> None:
        """Set result callback function"""
        self.on_result_callback = callback

    def set_error_callback(self, callback: Callable) -> None:
        """Set error callback function"""
        self.on_error_callback = callback
