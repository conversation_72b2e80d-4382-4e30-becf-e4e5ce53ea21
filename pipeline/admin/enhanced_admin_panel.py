#!/usr/bin/env python3
"""
Enhanced Admin Panel for Automated Pipeline
Comprehensive web interface for managing scraping and content generation pipelines
with real-time monitoring, control features, and resumability management.
"""

import os
import json
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
import sys

from flask import Flask, render_template, jsonify, request, redirect, url_for, flash
import pandas as pd

# Add pipeline modules to path
sys.path.append(str(Path(__file__).parent.parent))

# Import pipeline components
from core.pipeline_manager import PipelineManager, PipelineConfig, PipelineState
from core.state_manager import StateManager
from core.url_manager import URLManager, URLStatus, URLPriority
from scrapers.resumable_scraper import ResumableScraper
from generators.resumable_generator import ResumableContentGenerator

# Import existing admin panel functionality
sys.path.append(str(Path(__file__).parent.parent.parent))
from admin_panel import PipelineStatus as LegacyPipelineStatus


class EnhancedPipelineManager:
    """Enhanced pipeline manager with admin panel integration"""
    
    def __init__(self):
        self.config = PipelineConfig()
        self.pipeline_manager = PipelineManager(self.config)
        self.state_manager = StateManager()
        self.url_manager = URLManager()
        
        # Active pipeline instances
        self.active_scrapers = {}
        self.active_generators = {}
        
        # Legacy status for backward compatibility
        self.legacy_status = LegacyPipelineStatus()
        
        self.logger = logging.getLogger(__name__)

    async def create_scraping_pipeline(self, pipeline_id: str, urls: List[str], 
                                     config: Dict[str, Any] = None) -> bool:
        """Create a new scraping pipeline"""
        try:
            # Create pipeline in manager
            success = await self.pipeline_manager.create_pipeline(
                pipeline_id=pipeline_id,
                urls=urls,
                task_type='scrape'
            )
            
            if success:
                # Create scraper instance
                scraper_config = config or {}
                scraper = ResumableScraper(pipeline_id, scraper_config)
                await scraper.initialize(urls, resume=False)
                
                self.active_scrapers[pipeline_id] = scraper
                self.logger.info(f"Created scraping pipeline {pipeline_id} with {len(urls)} URLs")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to create scraping pipeline: {e}")
            return False

    async def create_content_pipeline(self, pipeline_id: str, 
                                    config: Dict[str, Any] = None) -> bool:
        """Create a new content generation pipeline"""
        try:
            # Load input data
            input_file = config.get('input_file', 'data/completed_data.csv') if config else 'data/completed_data.csv'
            
            # Create generator instance
            generator_config = config or {}
            generator = ResumableContentGenerator(pipeline_id, generator_config)
            success = await generator.initialize(resume=False)
            
            if success:
                self.active_generators[pipeline_id] = generator
                self.logger.info(f"Created content generation pipeline {pipeline_id}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to create content pipeline: {e}")
            return False

    async def start_pipeline(self, pipeline_id: str, pipeline_type: str, 
                           resume: bool = False) -> bool:
        """Start a pipeline (scraping or content generation)"""
        try:
            if pipeline_type == 'scrape':
                if pipeline_id in self.active_scrapers:
                    scraper = self.active_scrapers[pipeline_id]
                    if resume:
                        return await scraper.resume()
                    else:
                        return await scraper.start_scraping(resume=False)
                else:
                    self.logger.error(f"Scraper {pipeline_id} not found")
                    return False
                    
            elif pipeline_type == 'generate':
                if pipeline_id in self.active_generators:
                    generator = self.active_generators[pipeline_id]
                    if resume:
                        return await generator.resume()
                    else:
                        return await generator.start_generation(resume=False)
                else:
                    self.logger.error(f"Generator {pipeline_id} not found")
                    return False
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to start pipeline {pipeline_id}: {e}")
            return False

    async def pause_pipeline(self, pipeline_id: str, pipeline_type: str) -> bool:
        """Pause a running pipeline"""
        try:
            if pipeline_type == 'scrape' and pipeline_id in self.active_scrapers:
                return await self.active_scrapers[pipeline_id].pause()
            elif pipeline_type == 'generate' and pipeline_id in self.active_generators:
                return await self.active_generators[pipeline_id].pause()
            return False
        except Exception as e:
            self.logger.error(f"Failed to pause pipeline {pipeline_id}: {e}")
            return False

    async def stop_pipeline(self, pipeline_id: str, pipeline_type: str) -> bool:
        """Stop a running pipeline"""
        try:
            if pipeline_type == 'scrape' and pipeline_id in self.active_scrapers:
                return await self.active_scrapers[pipeline_id].stop()
            elif pipeline_type == 'generate' and pipeline_id in self.active_generators:
                return await self.active_generators[pipeline_id].stop()
            return False
        except Exception as e:
            self.logger.error(f"Failed to stop pipeline {pipeline_id}: {e}")
            return False

    async def get_pipeline_status(self, pipeline_id: str, pipeline_type: str) -> Dict[str, Any]:
        """Get detailed pipeline status"""
        try:
            if pipeline_type == 'scrape' and pipeline_id in self.active_scrapers:
                return await self.active_scrapers[pipeline_id].get_statistics()
            elif pipeline_type == 'generate' and pipeline_id in self.active_generators:
                return await self.active_generators[pipeline_id].get_statistics()
            else:
                # Try to get status from state manager
                return await self.pipeline_manager.get_pipeline_status(pipeline_id)
        except Exception as e:
            self.logger.error(f"Failed to get pipeline status: {e}")
            return {'error': str(e)}

    async def get_all_pipelines(self) -> Dict[str, Any]:
        """Get status of all pipelines"""
        try:
            all_states = await self.state_manager.get_all_pipeline_states()
            
            pipelines = {
                'scraping': {},
                'content_generation': {},
                'summary': {
                    'total': len(all_states),
                    'running': 0,
                    'completed': 0,
                    'failed': 0,
                    'paused': 0
                }
            }
            
            for pipeline_id, state in all_states.items():
                task_type = state.get('task_type', 'unknown')
                status = state.get('status', 'unknown')
                
                pipeline_info = {
                    'id': pipeline_id,
                    'status': status,
                    'task_type': task_type,
                    'created_at': state.get('created_at'),
                    'progress_percentage': state.get('progress_percentage', 0),
                    'total_tasks': state.get('total_tasks', 0),
                    'completed_tasks': state.get('completed_tasks', 0),
                    'failed_tasks': state.get('failed_tasks', 0)
                }
                
                if task_type == 'scrape':
                    pipelines['scraping'][pipeline_id] = pipeline_info
                elif task_type == 'generate':
                    pipelines['content_generation'][pipeline_id] = pipeline_info
                
                # Update summary
                if status == 'running':
                    pipelines['summary']['running'] += 1
                elif status == 'completed':
                    pipelines['summary']['completed'] += 1
                elif status == 'failed':
                    pipelines['summary']['failed'] += 1
                elif status == 'paused':
                    pipelines['summary']['paused'] += 1
            
            return pipelines
            
        except Exception as e:
            self.logger.error(f"Failed to get all pipelines: {e}")
            return {'error': str(e)}

    async def cleanup_old_pipelines(self, days_old: int = 7) -> int:
        """Clean up old completed pipelines"""
        try:
            cleaned_count = await self.state_manager.cleanup_completed_states(days_old)
            
            # Also cleanup URL manager
            for collection in ['scraping', 'content_generation']:
                await self.url_manager.cleanup_completed_urls(collection, days_old)
            
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup old pipelines: {e}")
            return 0

    def get_legacy_status(self) -> Dict[str, Any]:
        """Get legacy status for backward compatibility"""
        try:
            return {
                "scraping": self.legacy_status.get_scraping_status(),
                "content_generation": self.legacy_status.get_content_generation_status(),
                "system": self.legacy_status.get_system_status(),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        except Exception as e:
            self.logger.error(f"Failed to get legacy status: {e}")
            return {'error': str(e)}


# Initialize enhanced pipeline manager
enhanced_manager = EnhancedPipelineManager()

# Create Flask app
app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')


@app.route('/')
def enhanced_dashboard():
    """Enhanced dashboard with pipeline management"""
    return render_template('enhanced_dashboard.html')


@app.route('/pipelines')
def pipelines_page():
    """Pipelines management page"""
    return render_template('pipelines.html')


@app.route('/api/enhanced/status')
async def api_enhanced_status():
    """Enhanced status API endpoint"""
    try:
        pipelines = await enhanced_manager.get_all_pipelines()
        legacy_status = enhanced_manager.get_legacy_status()
        
        return jsonify({
            'pipelines': pipelines,
            'legacy': legacy_status,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/enhanced/pipeline/create', methods=['POST'])
async def api_create_pipeline():
    """Create a new pipeline"""
    try:
        data = request.get_json()
        pipeline_type = data.get('type')  # 'scrape' or 'generate'
        pipeline_id = data.get('pipeline_id', f"{pipeline_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        config = data.get('config', {})
        
        if pipeline_type == 'scrape':
            urls = data.get('urls', [])
            if not urls:
                return jsonify({'error': 'URLs required for scraping pipeline'}), 400
            
            success = await enhanced_manager.create_scraping_pipeline(pipeline_id, urls, config)
        elif pipeline_type == 'generate':
            success = await enhanced_manager.create_content_pipeline(pipeline_id, config)
        else:
            return jsonify({'error': 'Invalid pipeline type'}), 400
        
        if success:
            return jsonify({
                'success': True,
                'pipeline_id': pipeline_id,
                'message': f'{pipeline_type.title()} pipeline created successfully'
            })
        else:
            return jsonify({'error': 'Failed to create pipeline'}), 500
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/enhanced/pipeline/<pipeline_id>/<action>', methods=['POST'])
async def api_pipeline_action(pipeline_id: str, action: str):
    """Perform action on pipeline (start, pause, stop, resume)"""
    try:
        data = request.get_json() or {}
        pipeline_type = data.get('type', 'scrape')
        
        if action == 'start':
            success = await enhanced_manager.start_pipeline(pipeline_id, pipeline_type, resume=False)
        elif action == 'resume':
            success = await enhanced_manager.start_pipeline(pipeline_id, pipeline_type, resume=True)
        elif action == 'pause':
            success = await enhanced_manager.pause_pipeline(pipeline_id, pipeline_type)
        elif action == 'stop':
            success = await enhanced_manager.stop_pipeline(pipeline_id, pipeline_type)
        else:
            return jsonify({'error': f'Invalid action: {action}'}), 400
        
        if success:
            return jsonify({
                'success': True,
                'message': f'Pipeline {action} successful'
            })
        else:
            return jsonify({'error': f'Failed to {action} pipeline'}), 500
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/enhanced/pipeline/<pipeline_id>/status')
async def api_pipeline_status(pipeline_id: str):
    """Get detailed pipeline status"""
    try:
        pipeline_type = request.args.get('type', 'scrape')
        status = await enhanced_manager.get_pipeline_status(pipeline_id, pipeline_type)
        return jsonify(status)
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/enhanced/cleanup', methods=['POST'])
async def api_cleanup():
    """Cleanup old pipelines"""
    try:
        data = request.get_json() or {}
        days_old = data.get('days_old', 7)
        
        cleaned_count = await enhanced_manager.cleanup_old_pipelines(days_old)
        
        return jsonify({
            'success': True,
            'cleaned_count': cleaned_count,
            'message': f'Cleaned up {cleaned_count} old pipelines'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500


# Legacy endpoints for backward compatibility
@app.route('/api/status')
def api_legacy_status():
    """Legacy status endpoint"""
    return jsonify(enhanced_manager.get_legacy_status())


@app.route('/content')
def content_management():
    """Content management page (legacy)"""
    return render_template('content_management.html')


@app.route('/content/<content_id>')
def content_details(content_id):
    """Content details page (legacy)"""
    return render_template('content_details.html', content_id=content_id)


@app.route('/config-generator')
def config_generator():
    """MCP Configuration Generator (legacy)"""
    return render_template('configuration_generator.html')


if __name__ == '__main__':
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("🚀 Starting Enhanced MCP Pipeline Admin Panel...")
    print("📊 Enhanced Dashboard: http://localhost:9000")
    print("🔧 Pipeline Management: http://localhost:9000/pipelines")
    print("📈 Real-time monitoring and control available")
    
    app.run(debug=True, host='0.0.0.0', port=9000)
