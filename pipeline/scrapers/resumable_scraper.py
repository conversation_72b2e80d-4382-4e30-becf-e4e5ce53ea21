#!/usr/bin/env python3
"""
Resumable Scraping Engine
Advanced scraping system with resumability, error handling, and state management.
Integrates with the existing scraping infrastructure while adding enhanced features.
"""

import asyncio
import logging
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path
import sys
import os

# Add the scrapper scripts to path
sys.path.append(str(Path(__file__).parent.parent.parent / "scrapper" / "scripts"))

# Import existing scraping functionality
try:
    from fetch_readme_and_stars_noapi import (
        parse_github_url, fetch_readme_raw, fetch_stars_from_html, 
        CSV_FIELDS, append_and_sort_completed
    )
    from enhanced_content_analyzer import EnhancedContentAnalyzer
except ImportError as e:
    logging.error(f"Failed to import scraping modules: {e}")
    raise

# Import our pipeline components
from ..core.state_manager import StateManager, StateSnapshot
from ..core.url_manager import URLManager, URLStatus, URLPriority, URLItem


class ScrapingResult:
    """Container for scraping results"""
    
    def __init__(self, url: str, success: bool, data: Dict[str, Any] = None, 
                 error: str = None, duration: float = 0):
        self.url = url
        self.success = success
        self.data = data or {}
        self.error = error
        self.duration = duration
        self.timestamp = datetime.now()

    def to_dict(self) -> Dict[str, Any]:
        return {
            'url': self.url,
            'success': self.success,
            'data': self.data,
            'error': self.error,
            'duration': self.duration,
            'timestamp': self.timestamp.isoformat()
        }


class ResumableScraper:
    """Enhanced scraping engine with resumability and state management"""
    
    def __init__(self, pipeline_id: str, config: Dict[str, Any] = None):
        self.pipeline_id = pipeline_id
        self.config = config or {}
        
        # Configuration with defaults
        self.batch_size = self.config.get('batch_size', 10)
        self.delay_between_requests = self.config.get('delay', 2.0)
        self.max_retries = self.config.get('max_retries', 3)
        self.timeout = self.config.get('timeout', 30)
        self.checkpoint_interval = self.config.get('checkpoint_interval', 50)
        self.concurrent_workers = self.config.get('concurrent_workers', 3)
        
        # Initialize managers
        self.state_manager = StateManager()
        self.url_manager = URLManager()
        
        # Initialize content analyzer
        self.content_analyzer = EnhancedContentAnalyzer()
        
        # State tracking
        self.is_running = False
        self.is_paused = False
        self.current_batch = []
        self.processed_count = 0
        self.failed_count = 0
        self.skipped_count = 0
        self.start_time = None
        
        # Results storage
        self.results = []
        self.failed_urls = []
        
        # Setup logging
        self.logger = logging.getLogger(f"{__name__}.{pipeline_id}")
        
        # Callbacks
        self.on_progress_callback: Optional[Callable] = None
        self.on_result_callback: Optional[Callable] = None
        self.on_error_callback: Optional[Callable] = None

    async def initialize(self, urls: List[str], resume: bool = False) -> bool:
        """Initialize the scraper with URLs"""
        try:
            self.logger.info(f"Initializing scraper for pipeline {self.pipeline_id}")
            
            if not resume:
                # Fresh start - add all URLs to the URL manager
                added_count = await self.url_manager.add_urls(
                    urls=urls,
                    collection=self.pipeline_id,
                    source="scraper_init",
                    priority=URLPriority.NORMAL
                )
                
                # Create initial pipeline state
                await self.state_manager.create_pipeline_state(
                    pipeline_id=self.pipeline_id,
                    total_tasks=len(urls),
                    task_type='scrape',
                    metadata={
                        'scraper_config': self.config,
                        'total_urls': len(urls),
                        'added_urls': added_count
                    }
                )
                
                self.logger.info(f"Fresh start: Added {added_count} URLs to pipeline")
            else:
                # Resume - check existing state
                state = await self.state_manager.get_pipeline_state(self.pipeline_id)
                if not state:
                    self.logger.error(f"Cannot resume: No existing state for pipeline {self.pipeline_id}")
                    return False
                
                # Get checkpoint if available
                checkpoint = await self.state_manager.get_latest_checkpoint(self.pipeline_id)
                if checkpoint:
                    self.processed_count = checkpoint.completed_count
                    self.failed_count = checkpoint.failed_count
                    self.logger.info(f"Resuming from checkpoint: {self.processed_count} completed, {self.failed_count} failed")
                
                self.logger.info(f"Resuming pipeline {self.pipeline_id}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize scraper: {e}")
            return False

    async def start_scraping(self, resume: bool = False) -> bool:
        """Start the scraping process"""
        try:
            if self.is_running:
                self.logger.warning("Scraper is already running")
                return False
            
            self.is_running = True
            self.is_paused = False
            self.start_time = datetime.now()
            
            # Mark pipeline as started
            await self.state_manager.mark_pipeline_started(self.pipeline_id)
            
            self.logger.info(f"Starting scraping for pipeline {self.pipeline_id}")
            
            # Main scraping loop
            while self.is_running and not self.is_paused:
                # Get next batch of URLs
                next_urls = await self.url_manager.get_next_urls(
                    collection=self.pipeline_id,
                    count=self.batch_size,
                    status=URLStatus.PENDING
                )
                
                if not next_urls:
                    # Check for retry URLs
                    retry_urls = await self.url_manager.get_next_urls(
                        collection=self.pipeline_id,
                        count=self.batch_size,
                        status=URLStatus.RETRY
                    )
                    
                    if not retry_urls:
                        self.logger.info("No more URLs to process")
                        break
                    
                    next_urls = retry_urls
                
                # Process batch
                await self._process_batch(next_urls)
                
                # Create checkpoint periodically
                if self.processed_count % self.checkpoint_interval == 0:
                    await self._create_checkpoint()
                
                # Update progress
                await self._update_progress()
                
                # Small delay between batches
                if self.delay_between_requests > 0:
                    await asyncio.sleep(self.delay_between_requests)
            
            # Final checkpoint and completion
            await self._create_checkpoint()
            
            if not self.is_paused:
                await self.state_manager.mark_pipeline_completed(self.pipeline_id)
                self.logger.info(f"Scraping completed for pipeline {self.pipeline_id}")
            
            self.is_running = False
            return True
            
        except Exception as e:
            self.logger.error(f"Scraping failed: {e}")
            await self.state_manager.mark_pipeline_failed(self.pipeline_id, str(e))
            self.is_running = False
            return False

    async def _process_batch(self, url_items: List[URLItem]) -> None:
        """Process a batch of URLs"""
        try:
            self.logger.debug(f"Processing batch of {len(url_items)} URLs")
            
            # Create semaphore for concurrent processing
            semaphore = asyncio.Semaphore(self.concurrent_workers)
            
            # Process URLs concurrently
            tasks = [
                self._process_single_url(url_item, semaphore)
                for url_item in url_items
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Handle results
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    self.logger.error(f"Task failed for {url_items[i].url}: {result}")
                    await self.url_manager.update_url_status(
                        self.pipeline_id, url_items[i].url, 
                        URLStatus.FAILED, str(result)
                    )
                    self.failed_count += 1
                elif result:
                    self.results.append(result)
                    if result.success:
                        self.processed_count += 1
                    else:
                        self.failed_count += 1
            
        except Exception as e:
            self.logger.error(f"Batch processing failed: {e}")

    async def _process_single_url(self, url_item: URLItem, semaphore: asyncio.Semaphore) -> Optional[ScrapingResult]:
        """Process a single URL with rate limiting"""
        async with semaphore:
            start_time = time.time()
            
            try:
                # Mark as in progress
                await self.url_manager.update_url_status(
                    self.pipeline_id, url_item.url, URLStatus.IN_PROGRESS
                )
                
                # Parse GitHub URL
                owner, repo = parse_github_url(url_item.url)
                if not owner or not repo:
                    raise ValueError(f"Invalid GitHub URL: {url_item.url}")
                
                self.logger.debug(f"Scraping {owner}/{repo}")
                
                # Fetch data using existing functions
                readme = fetch_readme_raw(owner, repo)
                stars = fetch_stars_from_html(owner, repo)
                
                # Create result data
                result_data = {
                    'repo_url': url_item.url,
                    'slug': f"{owner}/{repo}",
                    'readme': readme,
                    'stars': stars,
                    'stored_at': datetime.now().isoformat()
                }
                
                # Apply enhanced content analysis
                result_data = self.content_analyzer.analyze_repository(result_data)
                
                # Store in completed data (using existing function)
                append_and_sort_completed(result_data, 'data/completed_data.csv')
                
                # Mark as completed
                await self.url_manager.update_url_status(
                    self.pipeline_id, url_item.url, URLStatus.COMPLETED
                )
                
                duration = time.time() - start_time
                result = ScrapingResult(
                    url=url_item.url,
                    success=True,
                    data=result_data,
                    duration=duration
                )
                
                # Call result callback if set
                if self.on_result_callback:
                    await self.on_result_callback(result)
                
                self.logger.debug(f"Successfully scraped {url_item.url} in {duration:.2f}s")
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                error_msg = str(e)
                
                # Determine if we should retry
                if url_item.attempts < self.max_retries:
                    await self.url_manager.update_url_status(
                        self.pipeline_id, url_item.url, URLStatus.RETRY, error_msg
                    )
                    self.logger.warning(f"Failed to scrape {url_item.url} (attempt {url_item.attempts}): {error_msg}")
                else:
                    await self.url_manager.update_url_status(
                        self.pipeline_id, url_item.url, URLStatus.FAILED, error_msg
                    )
                    self.logger.error(f"Failed to scrape {url_item.url} after {url_item.attempts} attempts: {error_msg}")
                
                result = ScrapingResult(
                    url=url_item.url,
                    success=False,
                    error=error_msg,
                    duration=duration
                )
                
                # Call error callback if set
                if self.on_error_callback:
                    await self.on_error_callback(result)
                
                return result
            
            finally:
                # Rate limiting delay
                if self.delay_between_requests > 0:
                    await asyncio.sleep(self.delay_between_requests)

    async def _create_checkpoint(self) -> None:
        """Create a checkpoint for resumability"""
        try:
            last_url = self.results[-1].url if self.results else None
            
            await self.state_manager.create_checkpoint(
                pipeline_id=self.pipeline_id,
                current_task_index=self.processed_count,
                last_processed_url=last_url or "",
                metadata={
                    'processed_count': self.processed_count,
                    'failed_count': self.failed_count,
                    'skipped_count': self.skipped_count,
                    'batch_size': self.batch_size,
                    'checkpoint_time': datetime.now().isoformat()
                }
            )
            
            self.logger.debug(f"Created checkpoint at {self.processed_count} processed URLs")
            
        except Exception as e:
            self.logger.error(f"Failed to create checkpoint: {e}")

    async def _update_progress(self) -> None:
        """Update progress in state manager"""
        try:
            await self.state_manager.update_progress(
                pipeline_id=self.pipeline_id,
                completed_tasks=self.processed_count,
                failed_tasks=self.failed_count,
                skipped_tasks=self.skipped_count
            )
            
            # Call progress callback if set
            if self.on_progress_callback:
                progress_data = {
                    'processed': self.processed_count,
                    'failed': self.failed_count,
                    'skipped': self.skipped_count,
                    'total': self.processed_count + self.failed_count + self.skipped_count
                }
                await self.on_progress_callback(progress_data)
            
        except Exception as e:
            self.logger.error(f"Failed to update progress: {e}")

    async def pause(self) -> bool:
        """Pause the scraping process"""
        try:
            self.is_paused = True
            await self._create_checkpoint()
            self.logger.info(f"Paused scraping for pipeline {self.pipeline_id}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to pause scraping: {e}")
            return False

    async def resume(self) -> bool:
        """Resume the scraping process"""
        try:
            self.is_paused = False
            self.logger.info(f"Resumed scraping for pipeline {self.pipeline_id}")
            return await self.start_scraping(resume=True)
        except Exception as e:
            self.logger.error(f"Failed to resume scraping: {e}")
            return False

    async def stop(self) -> bool:
        """Stop the scraping process"""
        try:
            self.is_running = False
            self.is_paused = False
            await self._create_checkpoint()
            self.logger.info(f"Stopped scraping for pipeline {self.pipeline_id}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to stop scraping: {e}")
            return False

    async def get_statistics(self) -> Dict[str, Any]:
        """Get current scraping statistics"""
        try:
            url_stats = await self.url_manager.get_statistics(self.pipeline_id)
            state = await self.state_manager.get_pipeline_state(self.pipeline_id)
            
            elapsed_time = 0
            if self.start_time:
                elapsed_time = (datetime.now() - self.start_time).total_seconds()
            
            return {
                'pipeline_id': self.pipeline_id,
                'is_running': self.is_running,
                'is_paused': self.is_paused,
                'processed_count': self.processed_count,
                'failed_count': self.failed_count,
                'skipped_count': self.skipped_count,
                'elapsed_time': elapsed_time,
                'url_statistics': url_stats,
                'pipeline_state': state,
                'results_count': len(self.results)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get statistics: {e}")
            return {}

    async def cleanup(self) -> None:
        """Cleanup resources"""
        try:
            await self.state_manager.close()
            await self.url_manager.close()
        except Exception as e:
            self.logger.error(f"Cleanup failed: {e}")

    def set_progress_callback(self, callback: Callable) -> None:
        """Set progress callback function"""
        self.on_progress_callback = callback

    def set_result_callback(self, callback: Callable) -> None:
        """Set result callback function"""
        self.on_result_callback = callback

    def set_error_callback(self, callback: Callable) -> None:
        """Set error callback function"""
        self.on_error_callback = callback
