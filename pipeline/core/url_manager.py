#!/usr/bin/env python3
"""
Enhanced URL Management System
Dual storage (Redis + CSV) for target URLs with status tracking,
priority queuing, and efficient retrieval mechanisms.
"""

import json
import csv
import hashlib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum
import pandas as pd

# Redis imports with fallback
try:
    import redis
    import aioredis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logging.warning("Redis not available. Using CSV-only URL management.")


class URLStatus(Enum):
    """URL processing status"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    RETRY = "retry"
    BLACKLISTED = "blacklisted"


class URLPriority(Enum):
    """URL priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class URLItem:
    """URL item with metadata and tracking"""
    url: str
    status: URLStatus
    priority: URLPriority
    source: str  # Where the URL came from
    category: str = "unknown"
    added_at: datetime = None
    updated_at: datetime = None
    last_attempt_at: datetime = None
    completed_at: datetime = None
    attempts: int = 0
    max_attempts: int = 3
    error_message: str = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.added_at is None:
            self.added_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
        if self.metadata is None:
            self.metadata = {}

    @property
    def url_hash(self) -> str:
        """Generate unique hash for URL"""
        return hashlib.md5(self.url.encode()).hexdigest()

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        data = asdict(self)
        # Convert enums and datetime objects
        data['status'] = data['status'].value
        data['priority'] = data['priority'].value
        for field in ['added_at', 'updated_at', 'last_attempt_at', 'completed_at']:
            if data[field]:
                data[field] = data[field].isoformat()
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'URLItem':
        """Create URLItem from dictionary"""
        # Convert strings back to enums and datetime objects
        data['status'] = URLStatus(data['status'])
        data['priority'] = URLPriority(data['priority'])
        for field in ['added_at', 'updated_at', 'last_attempt_at', 'completed_at']:
            if data.get(field):
                data[field] = datetime.fromisoformat(data[field])
        return cls(**data)


class URLManager:
    """Enhanced URL management with dual storage and priority queuing"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379", 
                 redis_db: int = 2, data_dir: str = "data"):
        self.redis_url = redis_url
        self.redis_db = redis_db
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        self.redis_client = None
        self.async_redis_client = None
        
        # Initialize Redis
        self._init_redis()
        
        # Local cache
        self.url_cache = {}
        self.blacklist = set()
        
        self.logger = logging.getLogger(__name__)

    def _init_redis(self):
        """Initialize Redis connections"""
        if not REDIS_AVAILABLE:
            return
        
        try:
            self.redis_client = redis.from_url(
                self.redis_url,
                db=self.redis_db,
                decode_responses=True,
                socket_timeout=5
            )
            self.redis_client.ping()
            self.logger.info("Connected to Redis for URL management")
        except Exception as e:
            self.logger.warning(f"Redis connection failed: {e}")
            self.redis_client = None

    async def _get_async_redis(self):
        """Get async Redis client"""
        if not REDIS_AVAILABLE or not self.redis_client:
            return None
        
        if self.async_redis_client is None:
            try:
                self.async_redis_client = aioredis.from_url(
                    self.redis_url,
                    db=self.redis_db,
                    decode_responses=True
                )
                await self.async_redis_client.ping()
            except Exception as e:
                self.logger.warning(f"Async Redis connection failed: {e}")
                self.async_redis_client = None
        
        return self.async_redis_client

    def _get_csv_path(self, collection: str) -> Path:
        """Get CSV file path for collection"""
        return self.data_dir / f"urls_{collection}.csv"

    def _get_json_path(self, collection: str) -> Path:
        """Get JSON file path for collection"""
        return self.data_dir / f"urls_{collection}.json"

    async def add_urls(self, urls: List[str], collection: str = "master",
                      source: str = "manual", category: str = "unknown",
                      priority: URLPriority = URLPriority.NORMAL) -> int:
        """Add multiple URLs to the collection"""
        try:
            added_count = 0
            url_items = []
            
            # Load existing URLs to avoid duplicates
            existing_urls = await self.get_all_urls(collection)
            existing_url_set = {item.url for item in existing_urls}
            
            for url in urls:
                if url not in existing_url_set:
                    url_item = URLItem(
                        url=url,
                        status=URLStatus.PENDING,
                        priority=priority,
                        source=source,
                        category=category
                    )
                    url_items.append(url_item)
                    existing_url_set.add(url)
                    added_count += 1
            
            if url_items:
                # Add to existing URLs
                all_urls = existing_urls + url_items
                await self._store_urls(collection, all_urls)
                
                self.logger.info(f"Added {added_count} new URLs to collection '{collection}'")
            
            return added_count
            
        except Exception as e:
            self.logger.error(f"Failed to add URLs: {e}")
            return 0

    async def _store_urls(self, collection: str, url_items: List[URLItem]) -> bool:
        """Store URLs in both Redis and CSV"""
        try:
            # Convert to dictionaries
            url_dicts = [item.to_dict() for item in url_items]
            
            # Store in Redis
            redis_client = await self._get_async_redis()
            if redis_client:
                try:
                    # Store as hash for efficient access
                    pipe = redis_client.pipeline()
                    for item in url_items:
                        pipe.hset(
                            f"url:{collection}:{item.url_hash}",
                            mapping=item.to_dict()
                        )
                        # Add to priority queues
                        pipe.zadd(
                            f"queue:{collection}:{item.priority.name.lower()}",
                            {item.url_hash: item.added_at.timestamp()}
                        )
                        # Add to status sets
                        pipe.sadd(f"status:{collection}:{item.status.value}", item.url_hash)
                    
                    await pipe.execute()
                    self.logger.debug(f"Stored {len(url_items)} URLs in Redis collection '{collection}'")
                except Exception as e:
                    self.logger.warning(f"Redis storage failed: {e}")
            
            # Store in CSV/JSON as backup
            csv_path = self._get_csv_path(collection)
            json_path = self._get_json_path(collection)
            
            # Save as JSON for full metadata
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(url_dicts, f, indent=2, ensure_ascii=False)
            
            # Save as CSV for easy viewing
            if url_dicts:
                df = pd.DataFrame(url_dicts)
                df.to_csv(csv_path, index=False)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to store URLs: {e}")
            return False

    async def get_all_urls(self, collection: str) -> List[URLItem]:
        """Get all URLs from collection"""
        try:
            # Try Redis first
            redis_client = await self._get_async_redis()
            if redis_client:
                try:
                    keys = await redis_client.keys(f"url:{collection}:*")
                    if keys:
                        url_items = []
                        for key in keys:
                            url_data = await redis_client.hgetall(key)
                            if url_data:
                                url_items.append(URLItem.from_dict(url_data))
                        
                        self.logger.debug(f"Loaded {len(url_items)} URLs from Redis collection '{collection}'")
                        return url_items
                except Exception as e:
                    self.logger.warning(f"Redis retrieval failed: {e}")
            
            # Fallback to JSON file
            json_path = self._get_json_path(collection)
            if json_path.exists():
                with open(json_path, 'r', encoding='utf-8') as f:
                    url_dicts = json.load(f)
                    url_items = [URLItem.from_dict(data) for data in url_dicts]
                    self.logger.debug(f"Loaded {len(url_items)} URLs from JSON file '{collection}'")
                    return url_items
            
            # Fallback to CSV file
            csv_path = self._get_csv_path(collection)
            if csv_path.exists():
                df = pd.read_csv(csv_path)
                url_items = []
                for _, row in df.iterrows():
                    url_dict = row.to_dict()
                    # Handle NaN values
                    for k, v in url_dict.items():
                        if pd.isna(v):
                            url_dict[k] = None
                    url_items.append(URLItem.from_dict(url_dict))
                
                self.logger.debug(f"Loaded {len(url_items)} URLs from CSV file '{collection}'")
                return url_items
            
            return []
            
        except Exception as e:
            self.logger.error(f"Failed to get URLs from collection '{collection}': {e}")
            return []

    async def get_next_urls(self, collection: str, count: int = 10,
                          priority: URLPriority = None,
                          status: URLStatus = URLStatus.PENDING) -> List[URLItem]:
        """Get next URLs to process with priority ordering"""
        try:
            redis_client = await self._get_async_redis()
            
            if redis_client:
                try:
                    # Get from priority queues
                    priorities = [priority] if priority else list(URLPriority)
                    # Sort by priority (highest first)
                    priorities.sort(key=lambda p: p.value, reverse=True)
                    
                    url_hashes = []
                    for p in priorities:
                        if len(url_hashes) >= count:
                            break
                        
                        # Get URLs from this priority level with the specified status
                        queue_key = f"queue:{collection}:{p.name.lower()}"
                        status_key = f"status:{collection}:{status.value}"
                        
                        # Intersect priority queue with status set
                        temp_key = f"temp:{collection}:{p.name.lower()}:{status.value}"
                        await redis_client.zinterstore(temp_key, [queue_key, status_key])
                        
                        # Get oldest URLs from this priority/status combination
                        remaining = count - len(url_hashes)
                        batch = await redis_client.zrange(temp_key, 0, remaining - 1)
                        url_hashes.extend(batch)
                        
                        # Clean up temp key
                        await redis_client.delete(temp_key)
                    
                    # Fetch URL details
                    url_items = []
                    for url_hash in url_hashes:
                        url_data = await redis_client.hgetall(f"url:{collection}:{url_hash}")
                        if url_data:
                            url_items.append(URLItem.from_dict(url_data))
                    
                    return url_items
                    
                except Exception as e:
                    self.logger.warning(f"Redis queue retrieval failed: {e}")
            
            # Fallback to file-based retrieval
            all_urls = await self.get_all_urls(collection)
            
            # Filter by status
            filtered_urls = [url for url in all_urls if url.status == status]
            
            # Filter by priority if specified
            if priority:
                filtered_urls = [url for url in filtered_urls if url.priority == priority]
            
            # Sort by priority (highest first) and then by added_at (oldest first)
            filtered_urls.sort(key=lambda u: (-u.priority.value, u.added_at))
            
            return filtered_urls[:count]
            
        except Exception as e:
            self.logger.error(f"Failed to get next URLs: {e}")
            return []

    async def update_url_status(self, collection: str, url: str, 
                              status: URLStatus, error_message: str = None) -> bool:
        """Update URL status"""
        try:
            url_hash = hashlib.md5(url.encode()).hexdigest()
            
            # Update in Redis
            redis_client = await self._get_async_redis()
            if redis_client:
                try:
                    url_key = f"url:{collection}:{url_hash}"
                    
                    # Get current data
                    current_data = await redis_client.hgetall(url_key)
                    if current_data:
                        # Update status and metadata
                        updates = {
                            'status': status.value,
                            'updated_at': datetime.now().isoformat()
                        }
                        
                        if status == URLStatus.IN_PROGRESS:
                            updates['last_attempt_at'] = datetime.now().isoformat()
                            updates['attempts'] = str(int(current_data.get('attempts', 0)) + 1)
                        elif status == URLStatus.COMPLETED:
                            updates['completed_at'] = datetime.now().isoformat()
                        
                        if error_message:
                            updates['error_message'] = error_message
                        
                        await redis_client.hset(url_key, mapping=updates)
                        
                        # Update status sets
                        old_status = current_data.get('status', 'pending')
                        await redis_client.srem(f"status:{collection}:{old_status}", url_hash)
                        await redis_client.sadd(f"status:{collection}:{status.value}", url_hash)
                        
                        self.logger.debug(f"Updated URL status in Redis: {url} -> {status.value}")
                except Exception as e:
                    self.logger.warning(f"Redis status update failed: {e}")
            
            # Update in file storage
            all_urls = await self.get_all_urls(collection)
            updated = False
            
            for url_item in all_urls:
                if url_item.url == url:
                    url_item.status = status
                    url_item.updated_at = datetime.now()
                    
                    if status == URLStatus.IN_PROGRESS:
                        url_item.last_attempt_at = datetime.now()
                        url_item.attempts += 1
                    elif status == URLStatus.COMPLETED:
                        url_item.completed_at = datetime.now()
                    
                    if error_message:
                        url_item.error_message = error_message
                    
                    updated = True
                    break
            
            if updated:
                await self._store_urls(collection, all_urls)
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to update URL status: {e}")
            return False

    async def get_statistics(self, collection: str) -> Dict[str, int]:
        """Get URL collection statistics"""
        try:
            redis_client = await self._get_async_redis()
            
            if redis_client:
                try:
                    stats = {}
                    for status in URLStatus:
                        count = await redis_client.scard(f"status:{collection}:{status.value}")
                        stats[status.value] = count
                    
                    stats['total'] = sum(stats.values())
                    return stats
                except Exception as e:
                    self.logger.warning(f"Redis statistics failed: {e}")
            
            # Fallback to file-based statistics
            all_urls = await self.get_all_urls(collection)
            stats = {}
            
            for status in URLStatus:
                stats[status.value] = sum(1 for url in all_urls if url.status == status)
            
            stats['total'] = len(all_urls)
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get statistics: {e}")
            return {}

    async def cleanup_completed_urls(self, collection: str, days_old: int = 30) -> int:
        """Clean up old completed URLs"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_old)
            all_urls = await self.get_all_urls(collection)
            
            # Filter out old completed URLs
            cleaned_urls = []
            removed_count = 0
            
            for url_item in all_urls:
                if (url_item.status == URLStatus.COMPLETED and 
                    url_item.completed_at and 
                    url_item.completed_at < cutoff_date):
                    removed_count += 1
                    # Also remove from Redis if available
                    redis_client = await self._get_async_redis()
                    if redis_client:
                        url_hash = url_item.url_hash
                        await redis_client.delete(f"url:{collection}:{url_hash}")
                        await redis_client.srem(f"status:{collection}:{url_item.status.value}", url_hash)
                        for priority in URLPriority:
                            await redis_client.zrem(f"queue:{collection}:{priority.name.lower()}", url_hash)
                else:
                    cleaned_urls.append(url_item)
            
            if removed_count > 0:
                await self._store_urls(collection, cleaned_urls)
                self.logger.info(f"Cleaned up {removed_count} old URLs from collection '{collection}'")
            
            return removed_count
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup URLs: {e}")
            return 0

    async def close(self):
        """Close Redis connections"""
        if self.async_redis_client:
            await self.async_redis_client.close()
        if self.redis_client:
            self.redis_client.close()
