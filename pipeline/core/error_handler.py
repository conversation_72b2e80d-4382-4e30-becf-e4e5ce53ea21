#!/usr/bin/env python3
"""
Comprehensive Error Handling and Recovery System
Robust error handling, retry mechanisms, failure tracking, and automatic recovery
features for both scraping and content generation pipelines.
"""

import asyncio
import logging
import traceback
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Union
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum
import json


class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for classification"""
    NETWORK = "network"
    PARSING = "parsing"
    RATE_LIMIT = "rate_limit"
    AUTHENTICATION = "authentication"
    RESOURCE = "resource"
    CONFIGURATION = "configuration"
    DATA = "data"
    SYSTEM = "system"
    UNKNOWN = "unknown"


class RecoveryAction(Enum):
    """Available recovery actions"""
    RETRY = "retry"
    SKIP = "skip"
    PAUSE = "pause"
    STOP = "stop"
    ESCALATE = "escalate"
    FALLBACK = "fallback"


@dataclass
class ErrorRecord:
    """Record of an error occurrence"""
    error_id: str
    timestamp: datetime
    component: str  # scraper, generator, orchestrator, etc.
    pipeline_id: str
    url: str
    error_type: str
    error_message: str
    stack_trace: str
    severity: ErrorSeverity
    category: ErrorCategory
    recovery_action: RecoveryAction
    retry_count: int = 0
    resolved: bool = False
    resolution_time: Optional[datetime] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['timestamp'] = data['timestamp'].isoformat()
        if data['resolution_time']:
            data['resolution_time'] = data['resolution_time'].isoformat()
        data['severity'] = data['severity'].value
        data['category'] = data['category'].value
        data['recovery_action'] = data['recovery_action'].value
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ErrorRecord':
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        if data.get('resolution_time'):
            data['resolution_time'] = datetime.fromisoformat(data['resolution_time'])
        data['severity'] = ErrorSeverity(data['severity'])
        data['category'] = ErrorCategory(data['category'])
        data['recovery_action'] = RecoveryAction(data['recovery_action'])
        return cls(**data)


class RetryStrategy:
    """Configurable retry strategy"""
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0, 
                 max_delay: float = 60.0, exponential_backoff: bool = True,
                 jitter: bool = True):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_backoff = exponential_backoff
        self.jitter = jitter

    def get_delay(self, attempt: int) -> float:
        """Calculate delay for given attempt"""
        if self.exponential_backoff:
            delay = self.base_delay * (2 ** attempt)
        else:
            delay = self.base_delay
        
        delay = min(delay, self.max_delay)
        
        if self.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)  # Add 0-50% jitter
        
        return delay

    def should_retry(self, attempt: int, error: Exception) -> bool:
        """Determine if should retry based on attempt count and error type"""
        if attempt >= self.max_retries:
            return False
        
        # Don't retry certain types of errors
        if isinstance(error, (KeyboardInterrupt, SystemExit)):
            return False
        
        # Don't retry authentication errors
        if "authentication" in str(error).lower() or "unauthorized" in str(error).lower():
            return False
        
        return True


class ErrorClassifier:
    """Classifies errors into categories and determines severity"""
    
    def __init__(self):
        self.classification_rules = {
            # Network errors
            'connection': (ErrorCategory.NETWORK, ErrorSeverity.MEDIUM),
            'timeout': (ErrorCategory.NETWORK, ErrorSeverity.MEDIUM),
            'dns': (ErrorCategory.NETWORK, ErrorSeverity.MEDIUM),
            'ssl': (ErrorCategory.NETWORK, ErrorSeverity.HIGH),
            
            # Rate limiting
            'rate limit': (ErrorCategory.RATE_LIMIT, ErrorSeverity.MEDIUM),
            'too many requests': (ErrorCategory.RATE_LIMIT, ErrorSeverity.MEDIUM),
            '429': (ErrorCategory.RATE_LIMIT, ErrorSeverity.MEDIUM),
            
            # Authentication
            'unauthorized': (ErrorCategory.AUTHENTICATION, ErrorSeverity.HIGH),
            'forbidden': (ErrorCategory.AUTHENTICATION, ErrorSeverity.HIGH),
            '401': (ErrorCategory.AUTHENTICATION, ErrorSeverity.HIGH),
            '403': (ErrorCategory.AUTHENTICATION, ErrorSeverity.HIGH),
            
            # Parsing errors
            'json': (ErrorCategory.PARSING, ErrorSeverity.LOW),
            'xml': (ErrorCategory.PARSING, ErrorSeverity.LOW),
            'html': (ErrorCategory.PARSING, ErrorSeverity.LOW),
            'parse': (ErrorCategory.PARSING, ErrorSeverity.LOW),
            
            # Resource errors
            'memory': (ErrorCategory.RESOURCE, ErrorSeverity.HIGH),
            'disk': (ErrorCategory.RESOURCE, ErrorSeverity.HIGH),
            'cpu': (ErrorCategory.RESOURCE, ErrorSeverity.MEDIUM),
            
            # System errors
            'permission': (ErrorCategory.SYSTEM, ErrorSeverity.HIGH),
            'file not found': (ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM),
            'no such file': (ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM),
        }

    def classify_error(self, error: Exception, context: str = "") -> tuple[ErrorCategory, ErrorSeverity]:
        """Classify an error and determine its severity"""
        error_text = f"{str(error)} {context}".lower()
        
        for keyword, (category, severity) in self.classification_rules.items():
            if keyword in error_text:
                return category, severity
        
        # Default classification
        return ErrorCategory.UNKNOWN, ErrorSeverity.MEDIUM

    def determine_recovery_action(self, category: ErrorCategory, 
                                severity: ErrorSeverity, 
                                retry_count: int) -> RecoveryAction:
        """Determine appropriate recovery action"""
        if severity == ErrorSeverity.CRITICAL:
            return RecoveryAction.STOP
        
        if category == ErrorCategory.AUTHENTICATION:
            return RecoveryAction.STOP
        
        if category == ErrorCategory.RATE_LIMIT:
            if retry_count < 3:
                return RecoveryAction.RETRY
            else:
                return RecoveryAction.PAUSE
        
        if category == ErrorCategory.NETWORK:
            if retry_count < 5:
                return RecoveryAction.RETRY
            else:
                return RecoveryAction.SKIP
        
        if category in [ErrorCategory.PARSING, ErrorCategory.DATA]:
            if retry_count < 2:
                return RecoveryAction.RETRY
            else:
                return RecoveryAction.SKIP
        
        if category == ErrorCategory.RESOURCE:
            return RecoveryAction.PAUSE
        
        # Default action
        if retry_count < 3:
            return RecoveryAction.RETRY
        else:
            return RecoveryAction.SKIP


class ErrorHandler:
    """Comprehensive error handling and recovery system"""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        self.error_log_file = self.data_dir / "error_log.json"
        self.error_records = []
        
        self.classifier = ErrorClassifier()
        self.retry_strategies = {
            ErrorCategory.NETWORK: RetryStrategy(max_retries=5, base_delay=2.0),
            ErrorCategory.RATE_LIMIT: RetryStrategy(max_retries=3, base_delay=10.0, max_delay=300.0),
            ErrorCategory.PARSING: RetryStrategy(max_retries=2, base_delay=1.0),
            ErrorCategory.AUTHENTICATION: RetryStrategy(max_retries=0),  # Don't retry auth errors
            ErrorCategory.RESOURCE: RetryStrategy(max_retries=2, base_delay=30.0),
            'default': RetryStrategy(max_retries=3, base_delay=1.0)
        }
        
        # Callbacks
        self.on_error_callback: Optional[Callable] = None
        self.on_recovery_callback: Optional[Callable] = None
        self.on_critical_error_callback: Optional[Callable] = None
        
        self.logger = logging.getLogger(__name__)
        
        # Load existing error records
        self._load_error_records()

    async def handle_error(self, error: Exception, component: str, 
                         pipeline_id: str, url: str = "", 
                         context: Dict[str, Any] = None) -> ErrorRecord:
        """Handle an error and determine recovery action"""
        try:
            # Generate unique error ID
            error_id = f"{component}_{pipeline_id}_{int(time.time())}"
            
            # Classify error
            category, severity = self.classifier.classify_error(error, str(context or {}))
            
            # Get retry count for this URL/component combination
            retry_count = self._get_retry_count(component, pipeline_id, url)
            
            # Determine recovery action
            recovery_action = self.classifier.determine_recovery_action(
                category, severity, retry_count
            )
            
            # Create error record
            error_record = ErrorRecord(
                error_id=error_id,
                timestamp=datetime.now(),
                component=component,
                pipeline_id=pipeline_id,
                url=url,
                error_type=type(error).__name__,
                error_message=str(error),
                stack_trace=traceback.format_exc(),
                severity=severity,
                category=category,
                recovery_action=recovery_action,
                retry_count=retry_count,
                metadata=context or {}
            )
            
            # Store error record
            self.error_records.append(error_record)
            await self._save_error_records()
            
            # Log error
            self.logger.error(
                f"Error in {component} (pipeline: {pipeline_id}): "
                f"{error} | Category: {category.value} | "
                f"Severity: {severity.value} | Action: {recovery_action.value}"
            )
            
            # Call error callback
            if self.on_error_callback:
                await self.on_error_callback(error_record)
            
            # Handle critical errors
            if severity == ErrorSeverity.CRITICAL and self.on_critical_error_callback:
                await self.on_critical_error_callback(error_record)
            
            return error_record
            
        except Exception as e:
            self.logger.error(f"Error in error handler: {e}")
            # Return a basic error record
            return ErrorRecord(
                error_id="error_handler_failure",
                timestamp=datetime.now(),
                component=component,
                pipeline_id=pipeline_id,
                url=url,
                error_type=type(error).__name__,
                error_message=str(error),
                stack_trace="",
                severity=ErrorSeverity.HIGH,
                category=ErrorCategory.SYSTEM,
                recovery_action=RecoveryAction.ESCALATE
            )

    async def execute_with_retry(self, func: Callable, *args, 
                               component: str, pipeline_id: str, 
                               url: str = "", context: Dict[str, Any] = None,
                               custom_retry_strategy: RetryStrategy = None) -> Any:
        """Execute a function with automatic retry logic"""
        last_error = None
        
        for attempt in range(10):  # Max attempts across all strategies
            try:
                result = await func(*args) if asyncio.iscoroutinefunction(func) else func(*args)
                
                # If we had previous errors for this operation, mark them as resolved
                await self._mark_errors_resolved(component, pipeline_id, url)
                
                return result
                
            except Exception as error:
                last_error = error
                
                # Handle the error
                error_record = await self.handle_error(
                    error, component, pipeline_id, url, context
                )
                
                # Determine if we should retry
                category = error_record.category
                retry_strategy = custom_retry_strategy or self.retry_strategies.get(
                    category, self.retry_strategies['default']
                )
                
                if not retry_strategy.should_retry(attempt, error):
                    break
                
                # Execute recovery action
                if error_record.recovery_action == RecoveryAction.STOP:
                    break
                elif error_record.recovery_action == RecoveryAction.PAUSE:
                    delay = retry_strategy.get_delay(attempt) * 5  # Longer pause
                    self.logger.info(f"Pausing for {delay:.1f}s due to {category.value} error")
                    await asyncio.sleep(delay)
                elif error_record.recovery_action == RecoveryAction.RETRY:
                    delay = retry_strategy.get_delay(attempt)
                    self.logger.info(f"Retrying in {delay:.1f}s (attempt {attempt + 1})")
                    await asyncio.sleep(delay)
                elif error_record.recovery_action == RecoveryAction.SKIP:
                    self.logger.info(f"Skipping due to {category.value} error")
                    break
                else:
                    break
        
        # If we get here, all retries failed
        if last_error:
            raise last_error

    def _get_retry_count(self, component: str, pipeline_id: str, url: str) -> int:
        """Get current retry count for a specific operation"""
        count = 0
        for record in self.error_records:
            if (record.component == component and 
                record.pipeline_id == pipeline_id and 
                record.url == url and 
                not record.resolved):
                count += 1
        return count

    async def _mark_errors_resolved(self, component: str, pipeline_id: str, url: str) -> None:
        """Mark errors as resolved for successful operations"""
        for record in self.error_records:
            if (record.component == component and 
                record.pipeline_id == pipeline_id and 
                record.url == url and 
                not record.resolved):
                record.resolved = True
                record.resolution_time = datetime.now()
        
        await self._save_error_records()

    async def get_error_statistics(self, pipeline_id: str = None, 
                                 hours: int = 24) -> Dict[str, Any]:
        """Get error statistics for analysis"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        relevant_errors = [
            record for record in self.error_records
            if record.timestamp >= cutoff_time and 
            (pipeline_id is None or record.pipeline_id == pipeline_id)
        ]
        
        stats = {
            'total_errors': len(relevant_errors),
            'by_severity': {},
            'by_category': {},
            'by_component': {},
            'by_recovery_action': {},
            'resolution_rate': 0,
            'most_common_errors': [],
            'error_trend': []
        }
        
        # Count by various dimensions
        for record in relevant_errors:
            # By severity
            severity = record.severity.value
            stats['by_severity'][severity] = stats['by_severity'].get(severity, 0) + 1
            
            # By category
            category = record.category.value
            stats['by_category'][category] = stats['by_category'].get(category, 0) + 1
            
            # By component
            component = record.component
            stats['by_component'][component] = stats['by_component'].get(component, 0) + 1
            
            # By recovery action
            action = record.recovery_action.value
            stats['by_recovery_action'][action] = stats['by_recovery_action'].get(action, 0) + 1
        
        # Calculate resolution rate
        resolved_count = sum(1 for record in relevant_errors if record.resolved)
        if relevant_errors:
            stats['resolution_rate'] = (resolved_count / len(relevant_errors)) * 100
        
        # Most common error messages
        error_counts = {}
        for record in relevant_errors:
            error_counts[record.error_message] = error_counts.get(record.error_message, 0) + 1
        
        stats['most_common_errors'] = sorted(
            error_counts.items(), key=lambda x: x[1], reverse=True
        )[:10]
        
        return stats

    async def get_recent_errors(self, limit: int = 50, 
                              pipeline_id: str = None) -> List[ErrorRecord]:
        """Get recent error records"""
        relevant_errors = [
            record for record in self.error_records
            if pipeline_id is None or record.pipeline_id == pipeline_id
        ]
        
        # Sort by timestamp (most recent first)
        relevant_errors.sort(key=lambda x: x.timestamp, reverse=True)
        
        return relevant_errors[:limit]

    async def cleanup_old_errors(self, days_old: int = 30) -> int:
        """Clean up old error records"""
        cutoff_time = datetime.now() - timedelta(days=days_old)
        
        initial_count = len(self.error_records)
        self.error_records = [
            record for record in self.error_records
            if record.timestamp >= cutoff_time or not record.resolved
        ]
        
        cleaned_count = initial_count - len(self.error_records)
        
        if cleaned_count > 0:
            await self._save_error_records()
            self.logger.info(f"Cleaned up {cleaned_count} old error records")
        
        return cleaned_count

    async def _save_error_records(self) -> None:
        """Save error records to file"""
        try:
            records_data = [record.to_dict() for record in self.error_records]
            
            with open(self.error_log_file, 'w', encoding='utf-8') as f:
                json.dump(records_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            self.logger.error(f"Failed to save error records: {e}")

    def _load_error_records(self) -> None:
        """Load error records from file"""
        try:
            if not self.error_log_file.exists():
                return
            
            with open(self.error_log_file, 'r', encoding='utf-8') as f:
                records_data = json.load(f)
            
            self.error_records = [
                ErrorRecord.from_dict(record_data) 
                for record_data in records_data
            ]
            
            self.logger.info(f"Loaded {len(self.error_records)} error records")
            
        except Exception as e:
            self.logger.error(f"Failed to load error records: {e}")
            self.error_records = []

    def set_error_callback(self, callback: Callable) -> None:
        """Set callback for error events"""
        self.on_error_callback = callback

    def set_recovery_callback(self, callback: Callable) -> None:
        """Set callback for recovery events"""
        self.on_recovery_callback = callback

    def set_critical_error_callback(self, callback: Callable) -> None:
        """Set callback for critical error events"""
        self.on_critical_error_callback = callback
