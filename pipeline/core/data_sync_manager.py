#!/usr/bin/env python3
"""
Data Synchronization Manager
Handles synchronization between Redis and CSV data, duplicate detection,
and ensures data consistency across the pipeline.
"""

import csv
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Any
import pandas as pd

# Redis imports with fallback
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False


class DataSyncManager:
    """Manages synchronization between Redis and CSV data stores"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.data_dir = Path(self.config.get('data_dir', 'data'))
        self.data_dir.mkdir(exist_ok=True)
        
        # File paths
        self.master_data_file = self.data_dir / "master_data.csv"
        self.completed_data_file = self.data_dir / "completed_data.csv"
        self.generated_content_file = self.data_dir / "generated_content.json"
        
        # Redis setup
        self.redis_client = None
        self.redis_available = False

        # Initialize logger first
        self.logger = logging.getLogger(__name__)

        # Initialize Redis
        self._init_redis()

        # Sync on initialization
        self.sync_redis_from_csv()

    def _init_redis(self):
        """Initialize Redis connection"""
        if not REDIS_AVAILABLE:
            self.logger.warning("Redis not available, using CSV-only mode")
            return
        
        try:
            redis_config = self.config.get('redis', {})
            redis_url = redis_config.get('url', 'redis://localhost:6379')
            redis_db = redis_config.get('db', 0)
            
            self.redis_client = redis.from_url(
                redis_url,
                db=redis_db,
                decode_responses=True,
                socket_timeout=5
            )
            self.redis_client.ping()
            self.redis_available = True
            self.logger.info("Connected to Redis for data synchronization")
        except Exception as e:
            self.logger.warning(f"Redis connection failed: {e}")
            self.redis_available = False

    def get_repo_key(self, repo_url: str) -> str:
        """Generate consistent key for repository"""
        # Normalize GitHub URL
        if 'github.com' in repo_url:
            # Extract owner/repo from URL
            parts = repo_url.rstrip('/').split('/')
            if len(parts) >= 2:
                owner_repo = f"{parts[-2]}/{parts[-1]}"
                return f"repo:{owner_repo}"
        
        # Fallback to URL-based key
        return f"repo:{repo_url.replace('/', '_').replace(':', '_')}"

    def is_repo_scraped(self, repo_url: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """Check if repository is already scraped"""
        try:
            repo_key = self.get_repo_key(repo_url)
            
            # Check Redis first if available
            if self.redis_available:
                repo_data = self.redis_client.hgetall(repo_key)
                if repo_data and repo_data.get('scraped') == 'true':
                    return True, repo_data
            
            # Check CSV file
            if self.completed_data_file.exists():
                df = pd.read_csv(self.completed_data_file)
                matching_rows = df[df['repo_url'] == repo_url]
                if not matching_rows.empty:
                    repo_data = matching_rows.iloc[0].to_dict()
                    return True, repo_data
            
            return False, None
            
        except Exception as e:
            self.logger.error(f"Error checking if repo is scraped: {e}")
            return False, None

    def is_content_generated(self, repo_url: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """Check if content is already generated for repository"""
        try:
            repo_key = self.get_repo_key(repo_url)
            
            # Check Redis first if available
            if self.redis_available:
                content_key = f"{repo_key}:content"
                content_data = self.redis_client.hgetall(content_key)
                if content_data and content_data.get('generated') == 'true':
                    return True, content_data
            
            # Check generated content file
            if self.generated_content_file.exists():
                with open(self.generated_content_file, 'r', encoding='utf-8') as f:
                    content_items = json.load(f)
                
                for item in content_items:
                    content = item.get('content', {})
                    if content.get('github_url') == repo_url:
                        return True, content
            
            return False, None
            
        except Exception as e:
            self.logger.error(f"Error checking if content is generated: {e}")
            return False, None

    def update_scraped_data(self, repo_url: str, scraped_data: Dict[str, Any], 
                           force_update: bool = False) -> bool:
        """Update scraped data with duplicate detection"""
        try:
            repo_key = self.get_repo_key(repo_url)
            
            # Check if already scraped
            is_scraped, existing_data = self.is_repo_scraped(repo_url)
            
            if is_scraped and not force_update:
                self.logger.info(f"Repository {repo_url} already scraped, skipping")
                return True
            
            # Prepare data for storage
            storage_data = {
                'repo_url': repo_url,
                'slug': scraped_data.get('slug', ''),
                'title': scraped_data.get('title', ''),
                'description': scraped_data.get('description', ''),
                'description_short': scraped_data.get('description_short', ''),
                'language': scraped_data.get('language', 'Unknown'),
                'stars': scraped_data.get('stars', '0'),
                'forks': scraped_data.get('forks', '0'),
                'author': scraped_data.get('author', ''),
                'category': scraped_data.get('category', 'Other'),
                'scraped': 'true',
                'scraped_at': datetime.now().isoformat(),
                'stored_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # Update Redis if available
            if self.redis_available:
                self.redis_client.hset(repo_key, mapping=storage_data)
                self.logger.debug(f"Updated Redis data for {repo_url}")
            
            # Update CSV file
            self._update_csv_data(storage_data, is_update=is_scraped)
            
            self.logger.info(f"{'Updated' if is_scraped else 'Added'} scraped data for {repo_url}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating scraped data for {repo_url}: {e}")
            return False

    def update_generated_content(self, repo_url: str, content_data: Dict[str, Any],
                               force_update: bool = False) -> bool:
        """Update generated content with duplicate detection"""
        try:
            repo_key = self.get_repo_key(repo_url)
            content_key = f"{repo_key}:content"
            
            # Check if content already generated
            is_generated, existing_content = self.is_content_generated(repo_url)
            
            if is_generated and not force_update:
                self.logger.info(f"Content for {repo_url} already generated, skipping")
                return True
            
            # Prepare content for storage
            storage_content = {
                'github_url': repo_url,
                'id': content_data.get('id', f"content_{int(datetime.now().timestamp())}"),
                'title': content_data.get('title', ''),
                'content': content_data.get('content', ''),
                'language': content_data.get('language', 'Unknown'),
                'github_stars': content_data.get('github_stars', 0),
                'github_repo': content_data.get('github_repo', ''),
                'generated': 'true',
                'generated_at': datetime.now().isoformat(),
                'last_content_update': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # Update Redis if available
            if self.redis_available:
                self.redis_client.hset(content_key, mapping=storage_content)
                self.logger.debug(f"Updated Redis content for {repo_url}")
            
            # Update JSON file
            self._update_json_content(storage_content, is_update=is_generated)
            
            # Update status in scraped data
            self._update_scraped_status(repo_url, has_content=True)
            
            self.logger.info(f"{'Updated' if is_generated else 'Added'} generated content for {repo_url}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating generated content for {repo_url}: {e}")
            return False

    def _update_csv_data(self, data: Dict[str, Any], is_update: bool = False):
        """Update CSV file with scraped data"""
        try:
            # Read existing data
            existing_data = []
            if self.completed_data_file.exists():
                with open(self.completed_data_file, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    existing_data = list(reader)
            
            # Update or add data
            repo_url = data['repo_url']
            updated = False
            
            for i, row in enumerate(existing_data):
                if row.get('repo_url') == repo_url:
                    # Update existing row
                    existing_data[i].update(data)
                    updated = True
                    break
            
            if not updated:
                # Add new row
                existing_data.append(data)
            
            # Write back to file
            if existing_data:
                fieldnames = existing_data[0].keys()
                with open(self.completed_data_file, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.DictWriter(f, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(existing_data)
                    
        except Exception as e:
            self.logger.error(f"Error updating CSV data: {e}")

    def _update_json_content(self, content_data: Dict[str, Any], is_update: bool = False):
        """Update JSON file with generated content"""
        try:
            # Read existing content
            existing_content = []
            if self.generated_content_file.exists():
                with open(self.generated_content_file, 'r', encoding='utf-8') as f:
                    existing_content = json.load(f)
            
            # Update or add content
            repo_url = content_data['github_url']
            updated = False
            
            for i, item in enumerate(existing_content):
                content = item.get('content', {})
                if content.get('github_url') == repo_url:
                    # Update existing content
                    existing_content[i]['content'].update(content_data)
                    updated = True
                    break
            
            if not updated:
                # Add new content
                new_item = {
                    'content': content_data,
                    'timestamp': datetime.now().isoformat()
                }
                existing_content.append(new_item)
            
            # Write back to file
            with open(self.generated_content_file, 'w', encoding='utf-8') as f:
                json.dump(existing_content, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            self.logger.error(f"Error updating JSON content: {e}")

    def _update_scraped_status(self, repo_url: str, has_content: bool = False):
        """Update scraped data status to indicate content generation"""
        try:
            repo_key = self.get_repo_key(repo_url)
            
            # Update Redis if available
            if self.redis_available:
                self.redis_client.hset(repo_key, 'has_content', 'true' if has_content else 'false')
                self.redis_client.hset(repo_key, 'content_updated_at', datetime.now().isoformat())
            
            # Update CSV file
            if self.completed_data_file.exists():
                df = pd.read_csv(self.completed_data_file)
                mask = df['repo_url'] == repo_url
                if mask.any():
                    df.loc[mask, 'has_content'] = has_content
                    df.loc[mask, 'content_updated_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    df.to_csv(self.completed_data_file, index=False)
                    
        except Exception as e:
            self.logger.error(f"Error updating scraped status: {e}")

    def sync_redis_from_csv(self) -> bool:
        """Sync Redis data from CSV files (recovery/initialization)"""
        if not self.redis_available:
            self.logger.info("Redis not available, skipping sync")
            return True
        
        try:
            synced_count = 0
            
            # Sync scraped data
            if self.completed_data_file.exists():
                df = pd.read_csv(self.completed_data_file)
                for _, row in df.iterrows():
                    repo_url = row.get('repo_url')
                    if repo_url:
                        repo_key = self.get_repo_key(repo_url)
                        row_dict = row.to_dict()
                        # Convert NaN to empty strings
                        row_dict = {k: (v if pd.notna(v) else '') for k, v in row_dict.items()}
                        self.redis_client.hset(repo_key, mapping=row_dict)
                        synced_count += 1
            
            # Sync generated content
            if self.generated_content_file.exists():
                with open(self.generated_content_file, 'r', encoding='utf-8') as f:
                    content_items = json.load(f)
                
                for item in content_items:
                    content = item.get('content', {})
                    repo_url = content.get('github_url')
                    if repo_url:
                        repo_key = self.get_repo_key(repo_url)
                        content_key = f"{repo_key}:content"
                        self.redis_client.hset(content_key, mapping=content)
                        synced_count += 1
            
            self.logger.info(f"Synced {synced_count} items from CSV to Redis")
            return True
            
        except Exception as e:
            self.logger.error(f"Error syncing Redis from CSV: {e}")
            return False

    def sync_csv_from_redis(self) -> bool:
        """Sync CSV data from Redis (backup)"""
        if not self.redis_available:
            self.logger.info("Redis not available, skipping sync")
            return True
        
        try:
            # Get all repository keys
            repo_keys = self.redis_client.keys("repo:*")
            content_keys = [k for k in repo_keys if k.endswith(':content')]
            repo_keys = [k for k in repo_keys if not k.endswith(':content')]
            
            # Sync scraped data
            scraped_data = []
            for repo_key in repo_keys:
                repo_data = self.redis_client.hgetall(repo_key)
                if repo_data:
                    scraped_data.append(repo_data)
            
            if scraped_data:
                df = pd.DataFrame(scraped_data)
                df.to_csv(self.completed_data_file, index=False)
            
            # Sync generated content
            content_data = []
            for content_key in content_keys:
                content = self.redis_client.hgetall(content_key)
                if content:
                    content_item = {
                        'content': content,
                        'timestamp': content.get('generated_at', datetime.now().isoformat())
                    }
                    content_data.append(content_item)
            
            if content_data:
                with open(self.generated_content_file, 'w', encoding='utf-8') as f:
                    json.dump(content_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Synced {len(scraped_data)} scraped items and {len(content_data)} content items from Redis to CSV")
            return True
            
        except Exception as e:
            self.logger.error(f"Error syncing CSV from Redis: {e}")
            return False

    def get_all_scraped_repos(self) -> List[Dict[str, Any]]:
        """Get all scraped repositories"""
        try:
            repos = []
            
            # Try Redis first
            if self.redis_available:
                repo_keys = self.redis_client.keys("repo:*")
                repo_keys = [k for k in repo_keys if not k.endswith(':content')]
                
                for repo_key in repo_keys:
                    repo_data = self.redis_client.hgetall(repo_key)
                    if repo_data and repo_data.get('scraped') == 'true':
                        repos.append(repo_data)
                
                if repos:
                    return repos
            
            # Fallback to CSV
            if self.completed_data_file.exists():
                df = pd.read_csv(self.completed_data_file)
                repos = df.to_dict('records')
            
            return repos
            
        except Exception as e:
            self.logger.error(f"Error getting scraped repos: {e}")
            return []

    def get_repos_without_content(self) -> List[Dict[str, Any]]:
        """Get repositories that are scraped but don't have generated content"""
        try:
            scraped_repos = self.get_all_scraped_repos()
            repos_without_content = []
            
            for repo in scraped_repos:
                repo_url = repo.get('repo_url')
                if repo_url:
                    is_generated, _ = self.is_content_generated(repo_url)
                    if not is_generated:
                        repos_without_content.append(repo)
            
            return repos_without_content
            
        except Exception as e:
            self.logger.error(f"Error getting repos without content: {e}")
            return []

    def cleanup_duplicates(self) -> Dict[str, int]:
        """Clean up duplicate entries in data files"""
        try:
            cleanup_stats = {'csv_duplicates': 0, 'json_duplicates': 0}
            
            # Clean CSV duplicates
            if self.completed_data_file.exists():
                df = pd.read_csv(self.completed_data_file)
                initial_count = len(df)
                df_clean = df.drop_duplicates(subset=['repo_url'], keep='last')
                final_count = len(df_clean)
                cleanup_stats['csv_duplicates'] = initial_count - final_count
                
                if cleanup_stats['csv_duplicates'] > 0:
                    df_clean.to_csv(self.completed_data_file, index=False)
            
            # Clean JSON duplicates
            if self.generated_content_file.exists():
                with open(self.generated_content_file, 'r', encoding='utf-8') as f:
                    content_items = json.load(f)
                
                initial_count = len(content_items)
                seen_urls = set()
                clean_items = []
                
                for item in content_items:
                    content = item.get('content', {})
                    repo_url = content.get('github_url')
                    if repo_url and repo_url not in seen_urls:
                        seen_urls.add(repo_url)
                        clean_items.append(item)
                
                final_count = len(clean_items)
                cleanup_stats['json_duplicates'] = initial_count - final_count
                
                if cleanup_stats['json_duplicates'] > 0:
                    with open(self.generated_content_file, 'w', encoding='utf-8') as f:
                        json.dump(clean_items, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Cleanup completed: {cleanup_stats}")
            return cleanup_stats
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
            return {'error': str(e)}

    def get_sync_status(self) -> Dict[str, Any]:
        """Get synchronization status between Redis and CSV"""
        try:
            status = {
                'redis_available': self.redis_available,
                'csv_scraped_count': 0,
                'csv_content_count': 0,
                'redis_scraped_count': 0,
                'redis_content_count': 0,
                'sync_needed': False
            }
            
            # Count CSV entries
            if self.completed_data_file.exists():
                df = pd.read_csv(self.completed_data_file)
                status['csv_scraped_count'] = len(df)
            
            if self.generated_content_file.exists():
                with open(self.generated_content_file, 'r', encoding='utf-8') as f:
                    content_items = json.load(f)
                status['csv_content_count'] = len(content_items)
            
            # Count Redis entries
            if self.redis_available:
                repo_keys = self.redis_client.keys("repo:*")
                content_keys = [k for k in repo_keys if k.endswith(':content')]
                repo_keys = [k for k in repo_keys if not k.endswith(':content')]
                
                status['redis_scraped_count'] = len(repo_keys)
                status['redis_content_count'] = len(content_keys)
                
                # Check if sync is needed
                status['sync_needed'] = (
                    status['csv_scraped_count'] != status['redis_scraped_count'] or
                    status['csv_content_count'] != status['redis_content_count']
                )
            
            return status
            
        except Exception as e:
            self.logger.error(f"Error getting sync status: {e}")
            return {'error': str(e)}
