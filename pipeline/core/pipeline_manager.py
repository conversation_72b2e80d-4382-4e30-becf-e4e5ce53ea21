#!/usr/bin/env python3
"""
Enhanced Pipeline Manager
Core orchestrator for the automated web scraping and content generation pipeline
with Redis and CSV dual storage, state management, and resumability features.
"""

import os
import json
import csv
import time
import asyncio
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum
import pandas as pd

# Redis imports with fallback
try:
    import redis
    import aioredis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logging.warning("Redis not available. Running in CSV-only mode.")


class PipelineState(Enum):
    """Pipeline execution states"""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    RESUMING = "resuming"


class TaskStatus(Enum):
    """Individual task status"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    RETRY = "retry"


@dataclass
class PipelineConfig:
    """Pipeline configuration settings"""
    redis_url: str = "redis://localhost:6379"
    redis_db: int = 0
    csv_backup_enabled: bool = True
    max_retries: int = 3
    retry_delay: int = 5
    batch_size: int = 10
    concurrent_workers: int = 3
    checkpoint_interval: int = 100
    data_dir: str = "data"
    logs_dir: str = "logs"
    enable_monitoring: bool = True
    auto_resume: bool = True


@dataclass
class TaskItem:
    """Individual task item with metadata"""
    id: str
    url: str
    task_type: str  # 'scrape' or 'generate'
    status: TaskStatus
    priority: int = 0
    attempts: int = 0
    created_at: datetime = None
    updated_at: datetime = None
    completed_at: datetime = None
    error_message: str = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
        if self.metadata is None:
            self.metadata = {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        data = asdict(self)
        # Convert datetime objects to ISO strings
        for field in ['created_at', 'updated_at', 'completed_at']:
            if data[field]:
                data[field] = data[field].isoformat()
        data['status'] = data['status'].value
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TaskItem':
        """Create TaskItem from dictionary"""
        # Convert ISO strings back to datetime objects
        for field in ['created_at', 'updated_at', 'completed_at']:
            if data.get(field):
                data[field] = datetime.fromisoformat(data[field])
        
        data['status'] = TaskStatus(data['status'])
        return cls(**data)


class DualStorageManager:
    """Manages dual storage in Redis and CSV with automatic fallback"""
    
    def __init__(self, config: PipelineConfig):
        self.config = config
        self.redis_client = None
        self.data_dir = Path(config.data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # Initialize Redis if available
        if REDIS_AVAILABLE:
            try:
                self.redis_client = redis.from_url(
                    config.redis_url, 
                    db=config.redis_db,
                    decode_responses=True
                )
                self.redis_client.ping()
                logging.info("Connected to Redis for dual storage")
            except Exception as e:
                logging.warning(f"Redis connection failed: {e}. Using CSV-only mode.")
                self.redis_client = None

    def _get_csv_path(self, key: str) -> Path:
        """Get CSV file path for a given key"""
        return self.data_dir / f"{key}.csv"

    def _get_json_path(self, key: str) -> Path:
        """Get JSON file path for a given key"""
        return self.data_dir / f"{key}.json"

    async def store_task_list(self, key: str, tasks: List[TaskItem]) -> bool:
        """Store task list in both Redis and CSV"""
        try:
            # Convert tasks to dictionaries
            task_dicts = [task.to_dict() for task in tasks]
            
            # Store in Redis if available
            if self.redis_client:
                try:
                    self.redis_client.set(f"tasks:{key}", json.dumps(task_dicts))
                    logging.debug(f"Stored {len(tasks)} tasks in Redis: {key}")
                except Exception as e:
                    logging.warning(f"Redis storage failed for {key}: {e}")
            
            # Store in CSV as backup
            if self.config.csv_backup_enabled:
                csv_path = self._get_csv_path(f"tasks_{key}")
                json_path = self._get_json_path(f"tasks_{key}")
                
                # Save as JSON for full metadata
                with open(json_path, 'w', encoding='utf-8') as f:
                    json.dump(task_dicts, f, indent=2, ensure_ascii=False)
                
                # Save as CSV for easy viewing
                if task_dicts:
                    df = pd.DataFrame(task_dicts)
                    df.to_csv(csv_path, index=False)
                
                logging.debug(f"Stored {len(tasks)} tasks in CSV: {csv_path}")
            
            return True
            
        except Exception as e:
            logging.error(f"Failed to store task list {key}: {e}")
            return False

    async def load_task_list(self, key: str) -> List[TaskItem]:
        """Load task list from Redis or CSV fallback"""
        try:
            # Try Redis first
            if self.redis_client:
                try:
                    data = self.redis_client.get(f"tasks:{key}")
                    if data:
                        task_dicts = json.loads(data)
                        tasks = [TaskItem.from_dict(task_dict) for task_dict in task_dicts]
                        logging.debug(f"Loaded {len(tasks)} tasks from Redis: {key}")
                        return tasks
                except Exception as e:
                    logging.warning(f"Redis load failed for {key}: {e}")
            
            # Fallback to JSON file
            json_path = self._get_json_path(f"tasks_{key}")
            if json_path.exists():
                with open(json_path, 'r', encoding='utf-8') as f:
                    task_dicts = json.load(f)
                    tasks = [TaskItem.from_dict(task_dict) for task_dict in task_dicts]
                    logging.debug(f"Loaded {len(tasks)} tasks from JSON: {json_path}")
                    return tasks
            
            # Fallback to CSV file
            csv_path = self._get_csv_path(f"tasks_{key}")
            if csv_path.exists():
                df = pd.read_csv(csv_path)
                tasks = []
                for _, row in df.iterrows():
                    task_dict = row.to_dict()
                    # Handle NaN values
                    for k, v in task_dict.items():
                        if pd.isna(v):
                            task_dict[k] = None
                    tasks.append(TaskItem.from_dict(task_dict))
                logging.debug(f"Loaded {len(tasks)} tasks from CSV: {csv_path}")
                return tasks
            
            return []
            
        except Exception as e:
            logging.error(f"Failed to load task list {key}: {e}")
            return []

    async def store_state(self, key: str, state: Dict[str, Any]) -> bool:
        """Store pipeline state"""
        try:
            state_json = json.dumps(state, default=str)
            
            # Store in Redis
            if self.redis_client:
                try:
                    self.redis_client.set(f"state:{key}", state_json)
                    self.redis_client.set(f"state:{key}:timestamp", datetime.now().isoformat())
                except Exception as e:
                    logging.warning(f"Redis state storage failed: {e}")
            
            # Store in file
            if self.config.csv_backup_enabled:
                state_path = self.data_dir / f"state_{key}.json"
                with open(state_path, 'w', encoding='utf-8') as f:
                    json.dump(state, f, indent=2, default=str)
            
            return True
            
        except Exception as e:
            logging.error(f"Failed to store state {key}: {e}")
            return False

    async def load_state(self, key: str) -> Dict[str, Any]:
        """Load pipeline state"""
        try:
            # Try Redis first
            if self.redis_client:
                try:
                    data = self.redis_client.get(f"state:{key}")
                    if data:
                        return json.loads(data)
                except Exception as e:
                    logging.warning(f"Redis state load failed: {e}")
            
            # Fallback to file
            state_path = self.data_dir / f"state_{key}.json"
            if state_path.exists():
                with open(state_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            
            return {}
            
        except Exception as e:
            logging.error(f"Failed to load state {key}: {e}")
            return {}


class PipelineManager:
    """Main pipeline manager with resumability and state management"""
    
    def __init__(self, config: PipelineConfig = None):
        self.config = config or PipelineConfig()
        self.storage = DualStorageManager(self.config)
        self.state = PipelineState.IDLE
        self.current_pipeline_id = None
        self.start_time = None
        self.end_time = None
        self.stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'skipped_tasks': 0
        }
        
        # Setup logging
        self._setup_logging()
        
    def _setup_logging(self):
        """Setup logging configuration"""
        logs_dir = Path(self.config.logs_dir)
        logs_dir.mkdir(exist_ok=True)
        
        log_file = logs_dir / f"pipeline_{datetime.now().strftime('%Y%m%d')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)

    async def create_pipeline(self, pipeline_id: str, urls: List[str], 
                            task_type: str = 'scrape') -> bool:
        """Create a new pipeline with given URLs"""
        try:
            self.logger.info(f"Creating pipeline {pipeline_id} with {len(urls)} URLs")
            
            # Create task items
            tasks = []
            for i, url in enumerate(urls):
                task = TaskItem(
                    id=f"{pipeline_id}_{i:06d}",
                    url=url,
                    task_type=task_type,
                    status=TaskStatus.PENDING,
                    priority=0
                )
                tasks.append(task)
            
            # Store tasks
            success = await self.storage.store_task_list(pipeline_id, tasks)
            if success:
                # Store pipeline metadata
                metadata = {
                    'pipeline_id': pipeline_id,
                    'task_type': task_type,
                    'total_tasks': len(tasks),
                    'created_at': datetime.now().isoformat(),
                    'state': PipelineState.IDLE.value
                }
                await self.storage.store_state(f"pipeline_{pipeline_id}", metadata)
                self.logger.info(f"Pipeline {pipeline_id} created successfully")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to create pipeline {pipeline_id}: {e}")
            return False

    async def get_pipeline_status(self, pipeline_id: str) -> Dict[str, Any]:
        """Get current status of a pipeline"""
        try:
            # Load pipeline metadata
            metadata = await self.storage.load_state(f"pipeline_{pipeline_id}")
            if not metadata:
                return {'error': 'Pipeline not found'}
            
            # Load tasks to get current statistics
            tasks = await self.storage.load_task_list(pipeline_id)
            
            # Calculate statistics
            stats = {
                'total': len(tasks),
                'pending': sum(1 for t in tasks if t.status == TaskStatus.PENDING),
                'in_progress': sum(1 for t in tasks if t.status == TaskStatus.IN_PROGRESS),
                'completed': sum(1 for t in tasks if t.status == TaskStatus.COMPLETED),
                'failed': sum(1 for t in tasks if t.status == TaskStatus.FAILED),
                'skipped': sum(1 for t in tasks if t.status == TaskStatus.SKIPPED),
                'retry': sum(1 for t in tasks if t.status == TaskStatus.RETRY)
            }
            
            # Calculate progress percentage
            progress = 0
            if stats['total'] > 0:
                completed = stats['completed'] + stats['skipped']
                progress = (completed / stats['total']) * 100
            
            return {
                'pipeline_id': pipeline_id,
                'state': metadata.get('state', PipelineState.IDLE.value),
                'task_type': metadata.get('task_type', 'unknown'),
                'created_at': metadata.get('created_at'),
                'updated_at': metadata.get('updated_at'),
                'progress_percentage': round(progress, 2),
                'statistics': stats,
                'can_resume': stats['pending'] > 0 or stats['retry'] > 0
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get pipeline status {pipeline_id}: {e}")
            return {'error': str(e)}

    async def start_pipeline(self, pipeline_id: str, resume: bool = False) -> bool:
        """Start or resume a pipeline"""
        try:
            self.logger.info(f"{'Resuming' if resume else 'Starting'} pipeline {pipeline_id}")

            # Load pipeline metadata
            metadata = await self.storage.load_state(f"pipeline_{pipeline_id}")
            if not metadata:
                self.logger.error(f"Pipeline {pipeline_id} not found")
                return False

            # Update state
            metadata['state'] = PipelineState.RUNNING.value
            metadata['updated_at'] = datetime.now().isoformat()
            if not resume:
                metadata['started_at'] = datetime.now().isoformat()

            await self.storage.store_state(f"pipeline_{pipeline_id}", metadata)

            self.current_pipeline_id = pipeline_id
            self.state = PipelineState.RUNNING
            self.start_time = datetime.now()

            return True

        except Exception as e:
            self.logger.error(f"Failed to start pipeline {pipeline_id}: {e}")
            return False

    async def pause_pipeline(self, pipeline_id: str) -> bool:
        """Pause a running pipeline"""
        try:
            self.logger.info(f"Pausing pipeline {pipeline_id}")

            metadata = await self.storage.load_state(f"pipeline_{pipeline_id}")
            if metadata:
                metadata['state'] = PipelineState.PAUSED.value
                metadata['paused_at'] = datetime.now().isoformat()
                await self.storage.store_state(f"pipeline_{pipeline_id}", metadata)

            self.state = PipelineState.PAUSED
            return True

        except Exception as e:
            self.logger.error(f"Failed to pause pipeline {pipeline_id}: {e}")
            return False

    async def stop_pipeline(self, pipeline_id: str) -> bool:
        """Stop a pipeline"""
        try:
            self.logger.info(f"Stopping pipeline {pipeline_id}")

            metadata = await self.storage.load_state(f"pipeline_{pipeline_id}")
            if metadata:
                metadata['state'] = PipelineState.IDLE.value
                metadata['stopped_at'] = datetime.now().isoformat()
                await self.storage.store_state(f"pipeline_{pipeline_id}", metadata)

            self.state = PipelineState.IDLE
            self.current_pipeline_id = None
            return True

        except Exception as e:
            self.logger.error(f"Failed to stop pipeline {pipeline_id}: {e}")
            return False

    async def get_next_tasks(self, pipeline_id: str, count: int = None) -> List[TaskItem]:
        """Get next tasks to process"""
        try:
            if count is None:
                count = self.config.batch_size

            tasks = await self.storage.load_task_list(pipeline_id)

            # Filter for pending and retry tasks
            available_tasks = [
                task for task in tasks
                if task.status in [TaskStatus.PENDING, TaskStatus.RETRY]
            ]

            # Sort by priority (higher first) and creation time
            available_tasks.sort(key=lambda t: (-t.priority, t.created_at))

            return available_tasks[:count]

        except Exception as e:
            self.logger.error(f"Failed to get next tasks for {pipeline_id}: {e}")
            return []

    async def update_task_status(self, pipeline_id: str, task_id: str,
                               status: TaskStatus, error_message: str = None) -> bool:
        """Update status of a specific task"""
        try:
            tasks = await self.storage.load_task_list(pipeline_id)

            # Find and update the task
            task_updated = False
            for task in tasks:
                if task.id == task_id:
                    task.status = status
                    task.updated_at = datetime.now()
                    if status == TaskStatus.COMPLETED:
                        task.completed_at = datetime.now()
                    if error_message:
                        task.error_message = error_message
                    if status == TaskStatus.RETRY:
                        task.attempts += 1
                    task_updated = True
                    break

            if task_updated:
                # Save updated tasks
                await self.storage.store_task_list(pipeline_id, tasks)

                # Update pipeline metadata if completed
                if status == TaskStatus.COMPLETED:
                    await self._check_pipeline_completion(pipeline_id)

                return True
            else:
                self.logger.warning(f"Task {task_id} not found in pipeline {pipeline_id}")
                return False

        except Exception as e:
            self.logger.error(f"Failed to update task status: {e}")
            return False

    async def _check_pipeline_completion(self, pipeline_id: str):
        """Check if pipeline is completed and update metadata"""
        try:
            tasks = await self.storage.load_task_list(pipeline_id)

            # Check if all tasks are in final states
            final_states = [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.SKIPPED]
            all_final = all(task.status in final_states for task in tasks)

            if all_final:
                metadata = await self.storage.load_state(f"pipeline_{pipeline_id}")
                metadata['state'] = PipelineState.COMPLETED.value
                metadata['completed_at'] = datetime.now().isoformat()

                # Calculate final statistics
                stats = {
                    'total': len(tasks),
                    'completed': sum(1 for t in tasks if t.status == TaskStatus.COMPLETED),
                    'failed': sum(1 for t in tasks if t.status == TaskStatus.FAILED),
                    'skipped': sum(1 for t in tasks if t.status == TaskStatus.SKIPPED)
                }
                metadata['final_statistics'] = stats

                await self.storage.store_state(f"pipeline_{pipeline_id}", metadata)

                self.logger.info(f"Pipeline {pipeline_id} completed. Stats: {stats}")

        except Exception as e:
            self.logger.error(f"Failed to check pipeline completion: {e}")

    async def get_failed_tasks(self, pipeline_id: str) -> List[TaskItem]:
        """Get all failed tasks for retry"""
        try:
            tasks = await self.storage.load_task_list(pipeline_id)
            return [task for task in tasks if task.status == TaskStatus.FAILED]

        except Exception as e:
            self.logger.error(f"Failed to get failed tasks: {e}")
            return []

    async def retry_failed_tasks(self, pipeline_id: str, max_attempts: int = None) -> int:
        """Mark failed tasks for retry"""
        try:
            if max_attempts is None:
                max_attempts = self.config.max_retries

            tasks = await self.storage.load_task_list(pipeline_id)
            retry_count = 0

            for task in tasks:
                if (task.status == TaskStatus.FAILED and
                    task.attempts < max_attempts):
                    task.status = TaskStatus.RETRY
                    task.updated_at = datetime.now()
                    retry_count += 1

            if retry_count > 0:
                await self.storage.store_task_list(pipeline_id, tasks)
                self.logger.info(f"Marked {retry_count} tasks for retry in pipeline {pipeline_id}")

            return retry_count

        except Exception as e:
            self.logger.error(f"Failed to retry failed tasks: {e}")
            return 0

    async def export_results(self, pipeline_id: str, format: str = 'csv') -> str:
        """Export pipeline results to file"""
        try:
            tasks = await self.storage.load_task_list(pipeline_id)
            metadata = await self.storage.load_state(f"pipeline_{pipeline_id}")

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            if format.lower() == 'csv':
                filename = f"pipeline_{pipeline_id}_results_{timestamp}.csv"
                filepath = self.config.data_dir / filename

                # Convert tasks to DataFrame
                task_dicts = [task.to_dict() for task in tasks]
                df = pd.DataFrame(task_dicts)
                df.to_csv(filepath, index=False)

            elif format.lower() == 'json':
                filename = f"pipeline_{pipeline_id}_results_{timestamp}.json"
                filepath = self.config.data_dir / filename

                export_data = {
                    'metadata': metadata,
                    'tasks': [task.to_dict() for task in tasks],
                    'exported_at': datetime.now().isoformat()
                }

                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, ensure_ascii=False)

            else:
                raise ValueError(f"Unsupported format: {format}")

            self.logger.info(f"Exported pipeline {pipeline_id} results to {filepath}")
            return str(filepath)

        except Exception as e:
            self.logger.error(f"Failed to export results: {e}")
            return None

    async def cleanup_old_pipelines(self, days_old: int = 30) -> int:
        """Clean up old completed pipelines"""
        try:
            # This would need to be implemented based on your storage strategy
            # For now, just log the intent
            self.logger.info(f"Cleanup requested for pipelines older than {days_old} days")
            return 0

        except Exception as e:
            self.logger.error(f"Failed to cleanup old pipelines: {e}")
            return 0
