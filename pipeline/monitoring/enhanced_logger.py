#!/usr/bin/env python3
"""
Enhanced Logging System
Comprehensive logging with structured output, log rotation, filtering,
and real-time log streaming for the enhanced MCP pipeline system.
"""

import logging
import logging.handlers
import json
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, TextIO
from pathlib import Path
from dataclasses import dataclass, asdict
from collections import deque
import gzip
import re


@dataclass
class LogEntry:
    """Structured log entry"""
    timestamp: datetime
    level: str
    logger_name: str
    message: str
    component: str
    pipeline_id: Optional[str] = None
    url: Optional[str] = None
    duration: Optional[float] = None
    error_type: Optional[str] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['timestamp'] = data['timestamp'].isoformat()
        return data

    def to_json(self) -> str:
        return json.dumps(self.to_dict(), ensure_ascii=False)


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured logging"""
    
    def __init__(self, include_metadata: bool = True):
        super().__init__()
        self.include_metadata = include_metadata

    def format(self, record: logging.LogRecord) -> str:
        # Extract structured data from record
        component = getattr(record, 'component', 'unknown')
        pipeline_id = getattr(record, 'pipeline_id', None)
        url = getattr(record, 'url', None)
        duration = getattr(record, 'duration', None)
        error_type = getattr(record, 'error_type', None)
        metadata = getattr(record, 'metadata', {})

        # Create log entry
        log_entry = LogEntry(
            timestamp=datetime.fromtimestamp(record.created),
            level=record.levelname,
            logger_name=record.name,
            message=record.getMessage(),
            component=component,
            pipeline_id=pipeline_id,
            url=url,
            duration=duration,
            error_type=error_type,
            metadata=metadata
        )

        if self.include_metadata:
            return log_entry.to_json()
        else:
            # Human-readable format
            parts = [
                log_entry.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                f"[{log_entry.level}]",
                f"[{component}]"
            ]
            
            if pipeline_id:
                parts.append(f"[{pipeline_id}]")
            
            parts.append(log_entry.message)
            
            if duration is not None:
                parts.append(f"({duration:.2f}s)")
            
            return " ".join(parts)


class LogBuffer:
    """Thread-safe log buffer for real-time streaming"""
    
    def __init__(self, max_size: int = 1000):
        self.buffer = deque(maxlen=max_size)
        self.lock = threading.Lock()
        self.subscribers = []

    def add_entry(self, log_entry: LogEntry):
        """Add log entry to buffer"""
        with self.lock:
            self.buffer.append(log_entry)
            
            # Notify subscribers
            for subscriber in self.subscribers:
                try:
                    subscriber(log_entry)
                except Exception:
                    pass  # Don't let subscriber errors affect logging

    def get_recent_entries(self, count: int = 100) -> List[LogEntry]:
        """Get recent log entries"""
        with self.lock:
            return list(self.buffer)[-count:]

    def subscribe(self, callback):
        """Subscribe to real-time log updates"""
        self.subscribers.append(callback)

    def unsubscribe(self, callback):
        """Unsubscribe from log updates"""
        if callback in self.subscribers:
            self.subscribers.remove(callback)


class BufferHandler(logging.Handler):
    """Custom handler that writes to log buffer"""
    
    def __init__(self, log_buffer: LogBuffer):
        super().__init__()
        self.log_buffer = log_buffer

    def emit(self, record: logging.LogRecord):
        try:
            # Extract structured data
            component = getattr(record, 'component', 'unknown')
            pipeline_id = getattr(record, 'pipeline_id', None)
            url = getattr(record, 'url', None)
            duration = getattr(record, 'duration', None)
            error_type = getattr(record, 'error_type', None)
            metadata = getattr(record, 'metadata', {})

            # Create log entry
            log_entry = LogEntry(
                timestamp=datetime.fromtimestamp(record.created),
                level=record.levelname,
                logger_name=record.name,
                message=record.getMessage(),
                component=component,
                pipeline_id=pipeline_id,
                url=url,
                duration=duration,
                error_type=error_type,
                metadata=metadata
            )

            self.log_buffer.add_entry(log_entry)
            
        except Exception:
            self.handleError(record)


class EnhancedLogger:
    """Enhanced logging system with structured output and real-time streaming"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # Configuration
        self.logs_dir = Path(self.config.get('logs_dir', 'logs'))
        self.logs_dir.mkdir(exist_ok=True)
        
        self.log_level = getattr(logging, self.config.get('log_level', 'INFO'))
        self.max_file_size = self.config.get('max_file_size', 10 * 1024 * 1024)  # 10MB
        self.backup_count = self.config.get('backup_count', 5)
        self.enable_compression = self.config.get('enable_compression', True)
        self.enable_structured_logging = self.config.get('enable_structured_logging', True)
        self.enable_real_time_streaming = self.config.get('enable_real_time_streaming', True)
        
        # Log buffer for real-time streaming
        self.log_buffer = LogBuffer(max_size=self.config.get('buffer_size', 1000))
        
        # Setup logging
        self._setup_logging()
        
        # Performance tracking
        self.log_stats = {
            'total_logs': 0,
            'logs_by_level': {'DEBUG': 0, 'INFO': 0, 'WARNING': 0, 'ERROR': 0, 'CRITICAL': 0},
            'logs_by_component': {},
            'start_time': datetime.now()
        }

    def _setup_logging(self):
        """Setup comprehensive logging configuration"""
        # Create root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(self.log_level)
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Console handler with human-readable format
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.log_level)
        console_formatter = StructuredFormatter(include_metadata=False)
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
        
        # Main log file handler with rotation
        main_log_file = self.logs_dir / 'pipeline.log'
        main_handler = logging.handlers.RotatingFileHandler(
            main_log_file,
            maxBytes=self.max_file_size,
            backupCount=self.backup_count
        )
        main_handler.setLevel(self.log_level)
        
        if self.enable_structured_logging:
            main_formatter = StructuredFormatter(include_metadata=True)
        else:
            main_formatter = StructuredFormatter(include_metadata=False)
        
        main_handler.setFormatter(main_formatter)
        root_logger.addHandler(main_handler)
        
        # Error log file handler
        error_log_file = self.logs_dir / 'errors.log'
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=self.max_file_size,
            backupCount=self.backup_count
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(main_formatter)
        root_logger.addHandler(error_handler)
        
        # Component-specific handlers
        self._setup_component_handlers()
        
        # Buffer handler for real-time streaming
        if self.enable_real_time_streaming:
            buffer_handler = BufferHandler(self.log_buffer)
            buffer_handler.setLevel(self.log_level)
            root_logger.addHandler(buffer_handler)
        
        # Custom log rotation with compression
        if self.enable_compression:
            self._setup_compression_rotation()

    def _setup_component_handlers(self):
        """Setup component-specific log handlers"""
        components = ['scraper', 'generator', 'orchestrator', 'admin', 'monitoring']
        
        for component in components:
            logger = logging.getLogger(f'pipeline.{component}')
            
            # Component-specific file
            component_log_file = self.logs_dir / f'{component}.log'
            component_handler = logging.handlers.RotatingFileHandler(
                component_log_file,
                maxBytes=self.max_file_size,
                backupCount=self.backup_count
            )
            component_handler.setLevel(self.log_level)
            
            if self.enable_structured_logging:
                formatter = StructuredFormatter(include_metadata=True)
            else:
                formatter = StructuredFormatter(include_metadata=False)
            
            component_handler.setFormatter(formatter)
            logger.addHandler(component_handler)

    def _setup_compression_rotation(self):
        """Setup log rotation with compression"""
        def compress_log_file(source_path: str):
            """Compress rotated log file"""
            try:
                source = Path(source_path)
                if source.exists():
                    compressed_path = source.with_suffix(source.suffix + '.gz')
                    
                    with open(source, 'rb') as f_in:
                        with gzip.open(compressed_path, 'wb') as f_out:
                            f_out.writelines(f_in)
                    
                    source.unlink()  # Remove original file
                    
            except Exception as e:
                logging.error(f"Failed to compress log file {source_path}: {e}")
        
        # Override rotation behavior for main handlers
        for handler in logging.getLogger().handlers:
            if isinstance(handler, logging.handlers.RotatingFileHandler):
                original_doRollover = handler.doRollover
                
                def enhanced_doRollover(self):
                    original_doRollover()
                    # Compress the rotated file
                    if self.backupCount > 0:
                        rotated_file = f"{self.baseFilename}.1"
                        threading.Thread(
                            target=compress_log_file,
                            args=(rotated_file,),
                            daemon=True
                        ).start()
                
                handler.doRollover = enhanced_doRollover.__get__(handler, type(handler))

    def get_logger(self, component: str, pipeline_id: str = None) -> logging.Logger:
        """Get a logger for a specific component"""
        logger_name = f'pipeline.{component}'
        if pipeline_id:
            logger_name += f'.{pipeline_id}'
        
        logger = logging.getLogger(logger_name)
        
        # Add custom methods for structured logging
        def log_with_context(level, message, **kwargs):
            extra = {
                'component': component,
                'pipeline_id': pipeline_id,
                **kwargs
            }
            logger.log(level, message, extra=extra)
            
            # Update stats
            self.log_stats['total_logs'] += 1
            level_name = logging.getLevelName(level)
            self.log_stats['logs_by_level'][level_name] = \
                self.log_stats['logs_by_level'].get(level_name, 0) + 1
            self.log_stats['logs_by_component'][component] = \
                self.log_stats['logs_by_component'].get(component, 0) + 1

        # Add convenience methods
        logger.log_operation = lambda msg, **kwargs: log_with_context(logging.INFO, msg, **kwargs)
        logger.log_error = lambda msg, **kwargs: log_with_context(logging.ERROR, msg, **kwargs)
        logger.log_warning = lambda msg, **kwargs: log_with_context(logging.WARNING, msg, **kwargs)
        logger.log_debug = lambda msg, **kwargs: log_with_context(logging.DEBUG, msg, **kwargs)
        
        # Add timing context manager
        class TimingContext:
            def __init__(self, operation_name: str):
                self.operation_name = operation_name
                self.start_time = None
            
            def __enter__(self):
                self.start_time = time.time()
                logger.log_debug(f"Starting {self.operation_name}")
                return self
            
            def __exit__(self, exc_type, exc_val, exc_tb):
                duration = time.time() - self.start_time
                if exc_type is None:
                    logger.log_operation(
                        f"Completed {self.operation_name}",
                        duration=duration
                    )
                else:
                    logger.log_error(
                        f"Failed {self.operation_name}: {exc_val}",
                        duration=duration,
                        error_type=exc_type.__name__ if exc_type else None
                    )
        
        logger.time_operation = TimingContext
        
        return logger

    def search_logs(self, query: str, component: str = None, 
                   level: str = None, hours: int = 24,
                   max_results: int = 100) -> List[LogEntry]:
        """Search logs with filters"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            results = []
            
            # Search in buffer first (most recent)
            recent_entries = self.log_buffer.get_recent_entries(1000)
            
            for entry in recent_entries:
                if entry.timestamp < cutoff_time:
                    continue
                
                # Apply filters
                if component and entry.component != component:
                    continue
                
                if level and entry.level != level:
                    continue
                
                # Search in message and metadata
                if (query.lower() in entry.message.lower() or
                    query.lower() in json.dumps(entry.metadata).lower()):
                    results.append(entry)
                
                if len(results) >= max_results:
                    break
            
            return results
            
        except Exception as e:
            logging.error(f"Failed to search logs: {e}")
            return []

    def get_log_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """Get logging statistics"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            # Count recent logs
            recent_entries = self.log_buffer.get_recent_entries(1000)
            recent_count = sum(1 for entry in recent_entries if entry.timestamp >= cutoff_time)
            
            # Calculate rates
            uptime_hours = (datetime.now() - self.log_stats['start_time']).total_seconds() / 3600
            logs_per_hour = self.log_stats['total_logs'] / max(uptime_hours, 1)
            
            return {
                'total_logs': self.log_stats['total_logs'],
                'recent_logs': recent_count,
                'logs_per_hour': round(logs_per_hour, 2),
                'logs_by_level': self.log_stats['logs_by_level'].copy(),
                'logs_by_component': self.log_stats['logs_by_component'].copy(),
                'uptime_hours': round(uptime_hours, 2),
                'buffer_size': len(self.log_buffer.buffer),
                'log_files': self._get_log_file_info()
            }
            
        except Exception as e:
            logging.error(f"Failed to get log statistics: {e}")
            return {'error': str(e)}

    def _get_log_file_info(self) -> List[Dict[str, Any]]:
        """Get information about log files"""
        try:
            log_files = []
            
            for log_file in self.logs_dir.glob('*.log*'):
                stat = log_file.stat()
                log_files.append({
                    'name': log_file.name,
                    'size_mb': round(stat.st_size / (1024 * 1024), 2),
                    'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    'compressed': log_file.suffix == '.gz'
                })
            
            return sorted(log_files, key=lambda x: x['modified'], reverse=True)
            
        except Exception as e:
            logging.error(f"Failed to get log file info: {e}")
            return []

    def cleanup_old_logs(self, days_old: int = 30) -> int:
        """Clean up old log files"""
        try:
            cutoff_time = datetime.now() - timedelta(days=days_old)
            cleaned_count = 0
            
            for log_file in self.logs_dir.glob('*.log*'):
                try:
                    stat = log_file.stat()
                    if datetime.fromtimestamp(stat.st_mtime) < cutoff_time:
                        log_file.unlink()
                        cleaned_count += 1
                except Exception as e:
                    logging.warning(f"Failed to clean up log file {log_file}: {e}")
            
            logging.info(f"Cleaned up {cleaned_count} old log files")
            return cleaned_count
            
        except Exception as e:
            logging.error(f"Failed to cleanup old logs: {e}")
            return 0

    def export_logs(self, output_file: str, component: str = None,
                   level: str = None, hours: int = 24) -> bool:
        """Export filtered logs to file"""
        try:
            entries = self.search_logs(
                query="",  # Empty query to get all
                component=component,
                level=level,
                hours=hours,
                max_results=10000
            )
            
            output_path = Path(output_file)
            output_path.parent.mkdir(exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                for entry in entries:
                    f.write(entry.to_json() + '\n')
            
            logging.info(f"Exported {len(entries)} log entries to {output_file}")
            return True
            
        except Exception as e:
            logging.error(f"Failed to export logs: {e}")
            return False

    def subscribe_to_logs(self, callback, filter_func=None):
        """Subscribe to real-time log updates"""
        def filtered_callback(log_entry):
            if filter_func is None or filter_func(log_entry):
                callback(log_entry)
        
        self.log_buffer.subscribe(filtered_callback)
        return filtered_callback  # Return for unsubscribing

    def unsubscribe_from_logs(self, callback):
        """Unsubscribe from log updates"""
        self.log_buffer.unsubscribe(callback)
