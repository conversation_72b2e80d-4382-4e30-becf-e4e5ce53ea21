#!/usr/bin/env python3
"""
Performance Monitoring System
Real-time performance tracking, bottleneck detection, and optimization
recommendations for the enhanced MCP pipeline system.
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import deque, defaultdict
import statistics

from .metrics_collector import MetricsCollector, PipelineMetrics


@dataclass
class PerformanceAlert:
    """Performance alert information"""
    alert_type: str
    severity: str  # 'info', 'warning', 'critical'
    message: str
    component: str
    metric_value: float
    threshold: float
    timestamp: datetime
    recommendations: List[str]

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['timestamp'] = data['timestamp'].isoformat()
        return data


@dataclass
class PerformanceReport:
    """Comprehensive performance report"""
    report_id: str
    period_start: datetime
    period_end: datetime
    system_performance: Dict[str, Any]
    pipeline_performance: Dict[str, Any]
    bottlenecks: List[Dict[str, Any]]
    recommendations: List[str]
    overall_score: float  # 0-100
    timestamp: datetime

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['period_start'] = data['period_start'].isoformat()
        data['period_end'] = data['period_end'].isoformat()
        data['timestamp'] = data['timestamp'].isoformat()
        return data


class PerformanceMonitor:
    """Advanced performance monitoring and analysis system"""
    
    def __init__(self, metrics_collector: MetricsCollector, config: Dict[str, Any] = None):
        self.metrics_collector = metrics_collector
        self.config = config or {}
        
        # Performance tracking
        self.performance_history = deque(maxlen=1000)
        self.bottleneck_history = deque(maxlen=100)
        self.alert_history = deque(maxlen=500)
        
        # Analysis settings
        self.analysis_interval = self.config.get('analysis_interval', 300)  # 5 minutes
        self.alert_cooldown = self.config.get('alert_cooldown', 600)  # 10 minutes
        self.performance_thresholds = self.config.get('performance_thresholds', {
            'cpu_efficiency': 70.0,
            'memory_efficiency': 75.0,
            'throughput_min': 1.0,  # tasks per minute
            'error_rate_max': 0.05,  # 5%
            'response_time_max': 15.0  # seconds
        })
        
        # State tracking
        self.monitoring = False
        self.last_analysis_time = None
        self.recent_alerts = defaultdict(datetime)
        
        # Callbacks
        self.alert_callbacks = []
        self.report_callbacks = []
        
        self.logger = logging.getLogger(__name__)

    def start_monitoring(self):
        """Start performance monitoring"""
        if self.monitoring:
            return
        
        self.monitoring = True
        
        # Add callbacks to metrics collector
        self.metrics_collector.add_alert_callback(self._handle_system_alert)
        self.metrics_collector.add_metric_callback(self._handle_metric_update)
        
        # Start analysis loop
        asyncio.create_task(self._analysis_loop())
        
        self.logger.info("Started performance monitoring")

    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.monitoring = False
        self.logger.info("Stopped performance monitoring")

    async def _analysis_loop(self):
        """Main performance analysis loop"""
        while self.monitoring:
            try:
                await asyncio.sleep(self.analysis_interval)
                await self._perform_analysis()
            except Exception as e:
                self.logger.error(f"Error in performance analysis: {e}")

    async def _perform_analysis(self):
        """Perform comprehensive performance analysis"""
        try:
            self.logger.debug("Performing performance analysis")
            
            # Analyze system performance
            system_analysis = await self._analyze_system_performance()
            
            # Analyze pipeline performance
            pipeline_analysis = await self._analyze_pipeline_performance()
            
            # Detect bottlenecks
            bottlenecks = await self._detect_bottlenecks()
            
            # Generate recommendations
            recommendations = await self._generate_recommendations(
                system_analysis, pipeline_analysis, bottlenecks
            )
            
            # Calculate overall performance score
            overall_score = self._calculate_performance_score(
                system_analysis, pipeline_analysis
            )
            
            # Create performance report
            report = PerformanceReport(
                report_id=f"perf_{int(time.time())}",
                period_start=datetime.now() - timedelta(seconds=self.analysis_interval),
                period_end=datetime.now(),
                system_performance=system_analysis,
                pipeline_performance=pipeline_analysis,
                bottlenecks=bottlenecks,
                recommendations=recommendations,
                overall_score=overall_score,
                timestamp=datetime.now()
            )
            
            self.performance_history.append(report)
            self.last_analysis_time = datetime.now()
            
            # Check for performance alerts
            await self._check_performance_alerts(report)
            
            # Call report callbacks
            for callback in self.report_callbacks:
                try:
                    await callback(report)
                except Exception as e:
                    self.logger.error(f"Error in report callback: {e}")
                    
        except Exception as e:
            self.logger.error(f"Failed to perform performance analysis: {e}")

    async def _analyze_system_performance(self) -> Dict[str, Any]:
        """Analyze system-level performance"""
        try:
            # Get recent system metrics
            system_summary = self.metrics_collector.get_system_metrics_summary(hours=1)
            
            if 'error' in system_summary:
                return {'error': system_summary['error']}
            
            averages = system_summary.get('averages', {})
            current = system_summary.get('current', {})
            
            # Calculate efficiency scores
            cpu_efficiency = max(0, 100 - averages.get('cpu_percent', 0))
            memory_efficiency = max(0, 100 - averages.get('memory_percent', 0))
            disk_efficiency = max(0, 100 - averages.get('disk_percent', 0))
            
            # Determine system health
            health_score = (cpu_efficiency + memory_efficiency + disk_efficiency) / 3
            
            if health_score >= 80:
                health_status = 'excellent'
            elif health_score >= 60:
                health_status = 'good'
            elif health_score >= 40:
                health_status = 'fair'
            else:
                health_status = 'poor'
            
            return {
                'health_status': health_status,
                'health_score': round(health_score, 2),
                'efficiency_scores': {
                    'cpu': round(cpu_efficiency, 2),
                    'memory': round(memory_efficiency, 2),
                    'disk': round(disk_efficiency, 2)
                },
                'resource_usage': averages,
                'current_state': current,
                'data_points': system_summary.get('data_points', 0)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to analyze system performance: {e}")
            return {'error': str(e)}

    async def _analyze_pipeline_performance(self) -> Dict[str, Any]:
        """Analyze pipeline-specific performance"""
        try:
            pipeline_analysis = {}
            
            # Analyze each active pipeline
            for pipeline_id in self.metrics_collector.pipeline_metrics_history:
                pipeline_summary = self.metrics_collector.get_pipeline_metrics_summary(pipeline_id)
                
                if 'error' not in pipeline_summary:
                    latest = pipeline_summary.get('latest_metrics', {})
                    trends = pipeline_summary.get('trends', {})
                    
                    # Calculate performance metrics
                    throughput = latest.get('tasks_per_minute', 0)
                    error_rate = latest.get('error_rate', 0)
                    progress = latest.get('progress_percentage', 0)
                    
                    # Determine pipeline health
                    pipeline_health = self._calculate_pipeline_health(
                        throughput, error_rate, progress
                    )
                    
                    pipeline_analysis[pipeline_id] = {
                        'health_status': pipeline_health['status'],
                        'health_score': pipeline_health['score'],
                        'throughput': throughput,
                        'error_rate': error_rate,
                        'progress': progress,
                        'trends': trends,
                        'latest_metrics': latest
                    }
            
            # Calculate overall pipeline performance
            if pipeline_analysis:
                avg_health = statistics.mean([
                    p['health_score'] for p in pipeline_analysis.values()
                ])
                avg_throughput = statistics.mean([
                    p['throughput'] for p in pipeline_analysis.values()
                ])
                avg_error_rate = statistics.mean([
                    p['error_rate'] for p in pipeline_analysis.values()
                ])
            else:
                avg_health = 100
                avg_throughput = 0
                avg_error_rate = 0
            
            return {
                'overall_health_score': round(avg_health, 2),
                'average_throughput': round(avg_throughput, 2),
                'average_error_rate': round(avg_error_rate, 4),
                'active_pipelines': len(pipeline_analysis),
                'pipeline_details': pipeline_analysis
            }
            
        except Exception as e:
            self.logger.error(f"Failed to analyze pipeline performance: {e}")
            return {'error': str(e)}

    def _calculate_pipeline_health(self, throughput: float, error_rate: float, 
                                 progress: float) -> Dict[str, Any]:
        """Calculate pipeline health score"""
        try:
            # Throughput score (0-40 points)
            throughput_threshold = self.performance_thresholds['throughput_min']
            throughput_score = min(40, (throughput / throughput_threshold) * 40)
            
            # Error rate score (0-30 points)
            error_threshold = self.performance_thresholds['error_rate_max']
            error_score = max(0, 30 - (error_rate / error_threshold) * 30)
            
            # Progress score (0-30 points)
            progress_score = (progress / 100) * 30
            
            # Total score
            total_score = throughput_score + error_score + progress_score
            
            # Determine status
            if total_score >= 80:
                status = 'excellent'
            elif total_score >= 60:
                status = 'good'
            elif total_score >= 40:
                status = 'fair'
            else:
                status = 'poor'
            
            return {
                'score': round(total_score, 2),
                'status': status,
                'component_scores': {
                    'throughput': round(throughput_score, 2),
                    'error_rate': round(error_score, 2),
                    'progress': round(progress_score, 2)
                }
            }
            
        except Exception as e:
            self.logger.error(f"Failed to calculate pipeline health: {e}")
            return {'score': 0, 'status': 'unknown'}

    async def _detect_bottlenecks(self) -> List[Dict[str, Any]]:
        """Detect system and pipeline bottlenecks"""
        bottlenecks = []
        
        try:
            # System bottlenecks
            system_summary = self.metrics_collector.get_system_metrics_summary(hours=1)
            if 'averages' in system_summary:
                averages = system_summary['averages']
                
                # CPU bottleneck
                if averages.get('cpu_percent', 0) > 85:
                    bottlenecks.append({
                        'type': 'cpu_bottleneck',
                        'severity': 'high',
                        'component': 'system',
                        'description': f"High CPU usage: {averages['cpu_percent']:.1f}%",
                        'impact': 'Reduced processing speed',
                        'recommendations': [
                            'Reduce concurrent workers',
                            'Increase delay between requests',
                            'Consider upgrading CPU'
                        ]
                    })
                
                # Memory bottleneck
                if averages.get('memory_percent', 0) > 90:
                    bottlenecks.append({
                        'type': 'memory_bottleneck',
                        'severity': 'critical',
                        'component': 'system',
                        'description': f"High memory usage: {averages['memory_percent']:.1f}%",
                        'impact': 'Risk of system instability',
                        'recommendations': [
                            'Reduce batch sizes',
                            'Implement memory cleanup',
                            'Add more RAM'
                        ]
                    })
                
                # Disk bottleneck
                if averages.get('disk_percent', 0) > 95:
                    bottlenecks.append({
                        'type': 'disk_bottleneck',
                        'severity': 'critical',
                        'component': 'system',
                        'description': f"High disk usage: {averages['disk_percent']:.1f}%",
                        'impact': 'Risk of data loss',
                        'recommendations': [
                            'Clean up old data',
                            'Archive completed pipelines',
                            'Add more storage'
                        ]
                    })
            
            # Pipeline bottlenecks
            for pipeline_id in self.metrics_collector.pipeline_metrics_history:
                pipeline_summary = self.metrics_collector.get_pipeline_metrics_summary(pipeline_id)
                
                if 'latest_metrics' in pipeline_summary:
                    latest = pipeline_summary['latest_metrics']
                    
                    # Low throughput bottleneck
                    if latest.get('tasks_per_minute', 0) < 0.5:
                        bottlenecks.append({
                            'type': 'throughput_bottleneck',
                            'severity': 'medium',
                            'component': f'pipeline_{pipeline_id}',
                            'description': f"Low throughput: {latest['tasks_per_minute']:.2f} tasks/min",
                            'impact': 'Slow pipeline completion',
                            'recommendations': [
                                'Increase concurrent workers',
                                'Reduce delay between requests',
                                'Check network connectivity'
                            ]
                        })
                    
                    # High error rate bottleneck
                    if latest.get('error_rate', 0) > 0.1:
                        bottlenecks.append({
                            'type': 'error_rate_bottleneck',
                            'severity': 'high',
                            'component': f'pipeline_{pipeline_id}',
                            'description': f"High error rate: {latest['error_rate']:.1%}",
                            'impact': 'Data quality issues',
                            'recommendations': [
                                'Increase retry attempts',
                                'Add longer delays',
                                'Check target site availability'
                            ]
                        })
            
            # Store bottlenecks in history
            if bottlenecks:
                self.bottleneck_history.append({
                    'timestamp': datetime.now(),
                    'bottlenecks': bottlenecks
                })
            
            return bottlenecks
            
        except Exception as e:
            self.logger.error(f"Failed to detect bottlenecks: {e}")
            return []

    async def _generate_recommendations(self, system_analysis: Dict[str, Any],
                                      pipeline_analysis: Dict[str, Any],
                                      bottlenecks: List[Dict[str, Any]]) -> List[str]:
        """Generate performance optimization recommendations"""
        recommendations = []
        
        try:
            # System recommendations
            if 'efficiency_scores' in system_analysis:
                scores = system_analysis['efficiency_scores']
                
                if scores.get('cpu', 100) < 70:
                    recommendations.append("Consider reducing concurrent workers to lower CPU usage")
                
                if scores.get('memory', 100) < 75:
                    recommendations.append("Reduce batch sizes to optimize memory usage")
                
                if scores.get('disk', 100) < 80:
                    recommendations.append("Clean up old data files and logs")
            
            # Pipeline recommendations
            if 'average_throughput' in pipeline_analysis:
                avg_throughput = pipeline_analysis['average_throughput']
                avg_error_rate = pipeline_analysis['average_error_rate']
                
                if avg_throughput < 1.0:
                    recommendations.append("Increase concurrent workers to improve throughput")
                
                if avg_error_rate > 0.05:
                    recommendations.append("Increase delays between requests to reduce errors")
            
            # Bottleneck-specific recommendations
            for bottleneck in bottlenecks:
                recommendations.extend(bottleneck.get('recommendations', []))
            
            # Remove duplicates while preserving order
            unique_recommendations = []
            for rec in recommendations:
                if rec not in unique_recommendations:
                    unique_recommendations.append(rec)
            
            return unique_recommendations[:10]  # Limit to top 10
            
        except Exception as e:
            self.logger.error(f"Failed to generate recommendations: {e}")
            return []

    def _calculate_performance_score(self, system_analysis: Dict[str, Any],
                                   pipeline_analysis: Dict[str, Any]) -> float:
        """Calculate overall performance score (0-100)"""
        try:
            system_score = system_analysis.get('health_score', 0)
            pipeline_score = pipeline_analysis.get('overall_health_score', 0)
            
            # Weighted average (system 40%, pipeline 60%)
            overall_score = (system_score * 0.4) + (pipeline_score * 0.6)
            
            return round(overall_score, 2)
            
        except Exception as e:
            self.logger.error(f"Failed to calculate performance score: {e}")
            return 0.0

    async def _check_performance_alerts(self, report: PerformanceReport):
        """Check for performance alerts based on report"""
        try:
            alerts = []
            
            # Overall performance alert
            if report.overall_score < 50:
                alerts.append(PerformanceAlert(
                    alert_type='overall_performance_low',
                    severity='warning',
                    message=f"Overall performance score is low: {report.overall_score}",
                    component='system',
                    metric_value=report.overall_score,
                    threshold=50.0,
                    timestamp=datetime.now(),
                    recommendations=report.recommendations[:3]
                ))
            
            # Bottleneck alerts
            for bottleneck in report.bottlenecks:
                if bottleneck['severity'] in ['high', 'critical']:
                    alerts.append(PerformanceAlert(
                        alert_type=bottleneck['type'],
                        severity=bottleneck['severity'],
                        message=bottleneck['description'],
                        component=bottleneck['component'],
                        metric_value=0,  # Would need to extract from description
                        threshold=0,
                        timestamp=datetime.now(),
                        recommendations=bottleneck['recommendations'][:2]
                    ))
            
            # Process alerts with cooldown
            for alert in alerts:
                alert_key = f"{alert.alert_type}_{alert.component}"
                last_alert_time = self.recent_alerts.get(alert_key)
                
                if (not last_alert_time or 
                    (datetime.now() - last_alert_time).total_seconds() > self.alert_cooldown):
                    
                    self.alert_history.append(alert)
                    self.recent_alerts[alert_key] = datetime.now()
                    
                    # Call alert callbacks
                    for callback in self.alert_callbacks:
                        try:
                            await callback(alert)
                        except Exception as e:
                            self.logger.error(f"Error in alert callback: {e}")
                            
        except Exception as e:
            self.logger.error(f"Failed to check performance alerts: {e}")

    def _handle_system_alert(self, alert: Dict[str, Any]):
        """Handle system alerts from metrics collector"""
        try:
            # Convert to performance alert format
            perf_alert = PerformanceAlert(
                alert_type=alert['type'],
                severity=alert['severity'],
                message=alert['message'],
                component='system',
                metric_value=alert['value'],
                threshold=alert['threshold'],
                timestamp=datetime.now(),
                recommendations=[]
            )
            
            self.alert_history.append(perf_alert)
            
        except Exception as e:
            self.logger.error(f"Failed to handle system alert: {e}")

    def _handle_metric_update(self, metric_point):
        """Handle metric updates from metrics collector"""
        # This could be used for real-time analysis
        pass

    def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get performance summary for the last N hours"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            # Filter recent reports
            recent_reports = [
                r for r in self.performance_history
                if r.timestamp >= cutoff_time
            ]
            
            if not recent_reports:
                return {'error': 'No recent performance data available'}
            
            # Calculate averages
            avg_score = statistics.mean([r.overall_score for r in recent_reports])
            
            # Get latest report
            latest_report = recent_reports[-1] if recent_reports else None
            
            # Count alerts
            recent_alerts = [
                a for a in self.alert_history
                if a.timestamp >= cutoff_time
            ]
            
            alert_counts = defaultdict(int)
            for alert in recent_alerts:
                alert_counts[alert.severity] += 1
            
            return {
                'period_hours': hours,
                'average_score': round(avg_score, 2),
                'latest_score': latest_report.overall_score if latest_report else 0,
                'reports_count': len(recent_reports),
                'alerts_count': dict(alert_counts),
                'latest_report': latest_report.to_dict() if latest_report else None,
                'monitoring_status': 'active' if self.monitoring else 'inactive'
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get performance summary: {e}")
            return {'error': str(e)}

    def add_alert_callback(self, callback: Callable):
        """Add performance alert callback"""
        self.alert_callbacks.append(callback)

    def add_report_callback(self, callback: Callable):
        """Add performance report callback"""
        self.report_callbacks.append(callback)
