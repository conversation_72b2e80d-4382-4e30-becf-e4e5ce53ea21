#!/usr/bin/env python3
"""
Comprehensive Monitoring and Metrics Collection System
Real-time monitoring, performance metrics, and system health tracking
for the enhanced MCP pipeline system.
"""

import asyncio
import logging
import time
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path
from dataclasses import dataclass, asdict
from collections import deque
import json

# Redis imports with fallback
try:
    import redis
    import aioredis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False


@dataclass
class MetricPoint:
    """Individual metric data point"""
    timestamp: datetime
    metric_name: str
    value: float
    tags: Dict[str, str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = {}

    def to_dict(self) -> Dict[str, Any]:
        return {
            'timestamp': self.timestamp.isoformat(),
            'metric_name': self.metric_name,
            'value': self.value,
            'tags': self.tags
        }


@dataclass
class SystemMetrics:
    """System resource metrics"""
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_percent: float
    disk_used_gb: float
    disk_free_gb: float
    network_bytes_sent: int
    network_bytes_recv: int
    active_connections: int
    timestamp: datetime

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class PipelineMetrics:
    """Pipeline-specific metrics"""
    pipeline_id: str
    pipeline_type: str
    status: str
    total_tasks: int
    completed_tasks: int
    failed_tasks: int
    skipped_tasks: int
    progress_percentage: float
    tasks_per_minute: float
    error_rate: float
    average_task_duration: float
    timestamp: datetime

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class MetricsCollector:
    """Comprehensive metrics collection and monitoring system"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.redis_client = None
        self.data_dir = Path(self.config.get('data_dir', 'data'))
        self.metrics_dir = self.data_dir / 'metrics'
        self.metrics_dir.mkdir(exist_ok=True)
        
        # Metric storage
        self.metrics_buffer = deque(maxlen=10000)  # In-memory buffer
        self.system_metrics_history = deque(maxlen=1440)  # 24 hours at 1-minute intervals
        self.pipeline_metrics_history = {}
        
        # Collection settings
        self.collection_interval = self.config.get('collection_interval', 60)  # seconds
        self.retention_days = self.config.get('retention_days', 7)
        self.buffer_flush_interval = self.config.get('buffer_flush_interval', 300)  # 5 minutes
        
        # State tracking
        self.collecting = False
        self.collection_thread = None
        self.flush_thread = None
        
        # Performance tracking
        self.start_time = datetime.now()
        self.last_collection_time = None
        self.collection_count = 0
        
        # Callbacks
        self.alert_callbacks = []
        self.metric_callbacks = []
        
        # Alert thresholds
        self.alert_thresholds = self.config.get('alert_thresholds', {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'disk_percent': 90.0,
            'error_rate': 0.1,
            'response_time': 30.0
        })
        
        self.logger = logging.getLogger(__name__)
        
        # Initialize Redis if available
        self._init_redis()

    def _init_redis(self):
        """Initialize Redis connection for metrics storage"""
        if not REDIS_AVAILABLE:
            return
        
        try:
            redis_url = self.config.get('redis_url', 'redis://localhost:6379')
            redis_db = self.config.get('redis_db', 3)  # Use different DB for metrics
            
            self.redis_client = redis.from_url(
                redis_url,
                db=redis_db,
                decode_responses=True,
                socket_timeout=5
            )
            self.redis_client.ping()
            self.logger.info("Connected to Redis for metrics storage")
        except Exception as e:
            self.logger.warning(f"Redis connection failed for metrics: {e}")
            self.redis_client = None

    def start_collection(self):
        """Start metrics collection"""
        if self.collecting:
            return
        
        self.collecting = True
        self.collection_thread = threading.Thread(target=self._collection_loop, daemon=True)
        self.flush_thread = threading.Thread(target=self._flush_loop, daemon=True)
        
        self.collection_thread.start()
        self.flush_thread.start()
        
        self.logger.info("Started metrics collection")

    def stop_collection(self):
        """Stop metrics collection"""
        self.collecting = False
        
        if self.collection_thread:
            self.collection_thread.join(timeout=5)
        if self.flush_thread:
            self.flush_thread.join(timeout=5)
        
        # Final flush
        self._flush_metrics()
        
        self.logger.info("Stopped metrics collection")

    def _collection_loop(self):
        """Main collection loop"""
        while self.collecting:
            try:
                # Collect system metrics
                system_metrics = self._collect_system_metrics()
                self._store_metric('system.cpu_percent', system_metrics.cpu_percent)
                self._store_metric('system.memory_percent', system_metrics.memory_percent)
                self._store_metric('system.disk_percent', system_metrics.disk_percent)
                
                # Store full system metrics
                self.system_metrics_history.append(system_metrics)
                
                # Check for alerts
                self._check_system_alerts(system_metrics)
                
                self.last_collection_time = datetime.now()
                self.collection_count += 1
                
                # Sleep until next collection
                time.sleep(self.collection_interval)
                
            except Exception as e:
                self.logger.error(f"Error in metrics collection: {e}")
                time.sleep(self.collection_interval)

    def _flush_loop(self):
        """Periodic flush of metrics to storage"""
        while self.collecting:
            try:
                time.sleep(self.buffer_flush_interval)
                self._flush_metrics()
            except Exception as e:
                self.logger.error(f"Error in metrics flush: {e}")

    def _collect_system_metrics(self) -> SystemMetrics:
        """Collect current system metrics"""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory metrics
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_mb = memory.used / (1024 * 1024)
            memory_available_mb = memory.available / (1024 * 1024)
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            disk_used_gb = disk.used / (1024 * 1024 * 1024)
            disk_free_gb = disk.free / (1024 * 1024 * 1024)
            
            # Network metrics
            network = psutil.net_io_counters()
            network_bytes_sent = network.bytes_sent
            network_bytes_recv = network.bytes_recv
            
            # Connection count
            active_connections = len(psutil.net_connections())
            
            return SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_used_mb=memory_used_mb,
                memory_available_mb=memory_available_mb,
                disk_percent=disk_percent,
                disk_used_gb=disk_used_gb,
                disk_free_gb=disk_free_gb,
                network_bytes_sent=network_bytes_sent,
                network_bytes_recv=network_bytes_recv,
                active_connections=active_connections,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"Failed to collect system metrics: {e}")
            # Return default metrics
            return SystemMetrics(
                cpu_percent=0, memory_percent=0, memory_used_mb=0,
                memory_available_mb=0, disk_percent=0, disk_used_gb=0,
                disk_free_gb=0, network_bytes_sent=0, network_bytes_recv=0,
                active_connections=0, timestamp=datetime.now()
            )

    def _store_metric(self, metric_name: str, value: float, tags: Dict[str, str] = None):
        """Store a metric point"""
        metric_point = MetricPoint(
            timestamp=datetime.now(),
            metric_name=metric_name,
            value=value,
            tags=tags or {}
        )
        
        self.metrics_buffer.append(metric_point)
        
        # Call metric callbacks
        for callback in self.metric_callbacks:
            try:
                callback(metric_point)
            except Exception as e:
                self.logger.error(f"Error in metric callback: {e}")

    def _flush_metrics(self):
        """Flush metrics buffer to persistent storage"""
        if not self.metrics_buffer:
            return
        
        try:
            # Convert buffer to list and clear
            metrics_to_flush = list(self.metrics_buffer)
            self.metrics_buffer.clear()
            
            # Store in Redis if available
            if self.redis_client:
                self._flush_to_redis(metrics_to_flush)
            
            # Store in files
            self._flush_to_files(metrics_to_flush)
            
            self.logger.debug(f"Flushed {len(metrics_to_flush)} metrics to storage")
            
        except Exception as e:
            self.logger.error(f"Failed to flush metrics: {e}")

    def _flush_to_redis(self, metrics: List[MetricPoint]):
        """Flush metrics to Redis"""
        try:
            pipe = self.redis_client.pipeline()
            
            for metric in metrics:
                key = f"metric:{metric.metric_name}:{int(metric.timestamp.timestamp())}"
                pipe.set(key, json.dumps(metric.to_dict()), ex=86400 * self.retention_days)
            
            pipe.execute()
            
        except Exception as e:
            self.logger.error(f"Failed to flush metrics to Redis: {e}")

    def _flush_to_files(self, metrics: List[MetricPoint]):
        """Flush metrics to JSON files"""
        try:
            # Group metrics by date
            metrics_by_date = {}
            for metric in metrics:
                date_str = metric.timestamp.strftime('%Y%m%d')
                if date_str not in metrics_by_date:
                    metrics_by_date[date_str] = []
                metrics_by_date[date_str].append(metric.to_dict())
            
            # Write to files
            for date_str, date_metrics in metrics_by_date.items():
                metrics_file = self.metrics_dir / f"metrics_{date_str}.json"
                
                # Load existing metrics if file exists
                existing_metrics = []
                if metrics_file.exists():
                    try:
                        with open(metrics_file, 'r') as f:
                            existing_metrics = json.load(f)
                    except Exception:
                        existing_metrics = []
                
                # Append new metrics
                existing_metrics.extend(date_metrics)
                
                # Write back to file
                with open(metrics_file, 'w') as f:
                    json.dump(existing_metrics, f, indent=2)
                    
        except Exception as e:
            self.logger.error(f"Failed to flush metrics to files: {e}")

    def _check_system_alerts(self, metrics: SystemMetrics):
        """Check system metrics against alert thresholds"""
        alerts = []
        
        if metrics.cpu_percent > self.alert_thresholds.get('cpu_percent', 80):
            alerts.append({
                'type': 'cpu_high',
                'message': f"High CPU usage: {metrics.cpu_percent:.1f}%",
                'severity': 'warning',
                'value': metrics.cpu_percent,
                'threshold': self.alert_thresholds['cpu_percent']
            })
        
        if metrics.memory_percent > self.alert_thresholds.get('memory_percent', 85):
            alerts.append({
                'type': 'memory_high',
                'message': f"High memory usage: {metrics.memory_percent:.1f}%",
                'severity': 'warning',
                'value': metrics.memory_percent,
                'threshold': self.alert_thresholds['memory_percent']
            })
        
        if metrics.disk_percent > self.alert_thresholds.get('disk_percent', 90):
            alerts.append({
                'type': 'disk_high',
                'message': f"High disk usage: {metrics.disk_percent:.1f}%",
                'severity': 'critical',
                'value': metrics.disk_percent,
                'threshold': self.alert_thresholds['disk_percent']
            })
        
        # Trigger alert callbacks
        for alert in alerts:
            for callback in self.alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    self.logger.error(f"Error in alert callback: {e}")

    def record_pipeline_metric(self, pipeline_id: str, metrics: PipelineMetrics):
        """Record pipeline-specific metrics"""
        try:
            # Store in history
            if pipeline_id not in self.pipeline_metrics_history:
                self.pipeline_metrics_history[pipeline_id] = deque(maxlen=1000)
            
            self.pipeline_metrics_history[pipeline_id].append(metrics)
            
            # Store individual metrics
            self._store_metric(f'pipeline.{pipeline_id}.progress', metrics.progress_percentage)
            self._store_metric(f'pipeline.{pipeline_id}.tasks_per_minute', metrics.tasks_per_minute)
            self._store_metric(f'pipeline.{pipeline_id}.error_rate', metrics.error_rate)
            
            # Check for pipeline alerts
            if metrics.error_rate > self.alert_thresholds.get('error_rate', 0.1):
                alert = {
                    'type': 'pipeline_error_rate_high',
                    'message': f"High error rate in pipeline {pipeline_id}: {metrics.error_rate:.1%}",
                    'severity': 'warning',
                    'pipeline_id': pipeline_id,
                    'value': metrics.error_rate,
                    'threshold': self.alert_thresholds['error_rate']
                }
                
                for callback in self.alert_callbacks:
                    try:
                        callback(alert)
                    except Exception as e:
                        self.logger.error(f"Error in alert callback: {e}")
                        
        except Exception as e:
            self.logger.error(f"Failed to record pipeline metrics: {e}")

    def get_system_metrics_summary(self, hours: int = 1) -> Dict[str, Any]:
        """Get system metrics summary for the last N hours"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            # Filter recent metrics
            recent_metrics = [
                m for m in self.system_metrics_history
                if m.timestamp >= cutoff_time
            ]
            
            if not recent_metrics:
                return {'error': 'No recent metrics available'}
            
            # Calculate averages
            avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
            avg_memory = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)
            avg_disk = sum(m.disk_percent for m in recent_metrics) / len(recent_metrics)
            
            # Get current metrics
            current = recent_metrics[-1] if recent_metrics else None
            
            return {
                'period_hours': hours,
                'data_points': len(recent_metrics),
                'averages': {
                    'cpu_percent': round(avg_cpu, 2),
                    'memory_percent': round(avg_memory, 2),
                    'disk_percent': round(avg_disk, 2)
                },
                'current': current.to_dict() if current else None,
                'collection_info': {
                    'collecting': self.collecting,
                    'last_collection': self.last_collection_time.isoformat() if self.last_collection_time else None,
                    'collection_count': self.collection_count,
                    'uptime_seconds': (datetime.now() - self.start_time).total_seconds()
                }
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get system metrics summary: {e}")
            return {'error': str(e)}

    def get_pipeline_metrics_summary(self, pipeline_id: str) -> Dict[str, Any]:
        """Get pipeline metrics summary"""
        try:
            if pipeline_id not in self.pipeline_metrics_history:
                return {'error': f'No metrics found for pipeline {pipeline_id}'}
            
            metrics_history = list(self.pipeline_metrics_history[pipeline_id])
            if not metrics_history:
                return {'error': 'No metrics data available'}
            
            # Get latest metrics
            latest = metrics_history[-1]
            
            # Calculate trends
            if len(metrics_history) > 1:
                previous = metrics_history[-2]
                progress_trend = latest.progress_percentage - previous.progress_percentage
                speed_trend = latest.tasks_per_minute - previous.tasks_per_minute
            else:
                progress_trend = 0
                speed_trend = 0
            
            return {
                'pipeline_id': pipeline_id,
                'latest_metrics': latest.to_dict(),
                'trends': {
                    'progress_change': round(progress_trend, 2),
                    'speed_change': round(speed_trend, 2)
                },
                'history_points': len(metrics_history)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get pipeline metrics summary: {e}")
            return {'error': str(e)}

    def add_alert_callback(self, callback: Callable):
        """Add alert callback function"""
        self.alert_callbacks.append(callback)

    def add_metric_callback(self, callback: Callable):
        """Add metric callback function"""
        self.metric_callbacks.append(callback)

    def cleanup_old_metrics(self, days_old: int = None) -> int:
        """Clean up old metric files"""
        try:
            if days_old is None:
                days_old = self.retention_days
            
            cutoff_date = datetime.now() - timedelta(days=days_old)
            cleaned_count = 0
            
            # Clean up metric files
            for metrics_file in self.metrics_dir.glob("metrics_*.json"):
                try:
                    # Extract date from filename
                    date_str = metrics_file.stem.replace('metrics_', '')
                    file_date = datetime.strptime(date_str, '%Y%m%d')
                    
                    if file_date < cutoff_date:
                        metrics_file.unlink()
                        cleaned_count += 1
                        
                except Exception as e:
                    self.logger.warning(f"Failed to process metrics file {metrics_file}: {e}")
            
            self.logger.info(f"Cleaned up {cleaned_count} old metric files")
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup old metrics: {e}")
            return 0
